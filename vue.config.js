/*
 * @Author: your name
 * @Date: 2020-12-17 18:21:37
 * @LastEditTime: 2021-01-29 13:41:44
 * @LastEditors: <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \yqzx_web\vue.config.js
 */
//打包分析工具
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const path = require('path');
const webpack = require('webpack')
const MomentLocalesPlugin = require('moment-locales-webpack-plugin');

function resolve(dir) {
  return path.join(__dirname, dir)
}
const isProd = process.env.NODE_ENV === 'production';
module.exports = {
  publicPath: './',
  outputDir: 'dist',
  css: {
    loaderOptions: {
      less: {
        modifyVars: {
          // less vars，customize ant design theme
          'primary-color': '#1890ff',
          'font-size-base': '13px'
        },
        // DO NOT REMOVE THIS LINE
        javascriptEnabled: true
      }
    }
  },
  // pluginOptions: {
  //   'style-resources-loader': {
  //     preProcessor: 'less',
      // patterns: [resolve('./src/assets/style/mixin.less')]
  //   }
  // },
  configureWebpack: {
    // devtool: 'source-map',//方便调试
    resolve: {
      alias: {
        '@': resolve('./src')
      }
    },
    plugins: [
      // new BundleAnalyzerPlugin(),
      new MomentLocalesPlugin({
        localesToKeep: ['zh-cn']
      })
    ]
  },

  devServer: {
    // development server port 8000
    port: 8080,
    open: true,
    proxy: {
      '/testApi': { //后端接口地址
        target: 'http://************/', //实际要访问的地址
        ws: false,
        changeOrigin: true, //是否是跨域请求，true:是
        pathRewrite: {
          '^/testApi': 'test/yqzx' //路径重写，target+此值为真正要请求的后端地址
        }
      },
      '/devApi': {
        target: 'http://************/',
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/devApi': 'dev/yqzx' //路径重写
        }
      },
      '/prodApi': {
        target: 'http://192.168.1.203:96/',
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/prodApi': 'api' //路径重写
        }
      },
      '/benDiApi': {
        target: 'http://192.168.0.16:8091',
        ws: false,
        changeOrigin: true,
        pathRewrite: {
          '^/benDiApi': '' //路径重写
        }
      }
    }
  },
  // disable source map in production
  productionSourceMap: false,
  chainWebpack(config) {
    config.plugins.delete('preload');
    config.plugins.delete('prefetch');
    // isProd?config.plugin('webpack-bundle-analyzer')
    //       .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin):''
    // set svg-sprite-loader
    const svgRule = config.module.rule('svg');
    svgRule.uses.clear();
    svgRule
      .test(/\.svg$/)
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      });
    // config.module
    //   .rule('svg')
    //   .exclude.add(resolve('src/icons'))
    //   .end();
    // config.plugin('chunkPlugin').use(webpack.optimize.LimitChunkCountPlugin, [{
    //   maxChunks: 3, // 必须大于或等于 1
    //   minChunkSize: 1000
    // }])
    //   config.module
    //     .rule('icons')
    //     .test(/\.svg$/)
    //     .include.add(resolve('src/icons'))
    //     .end()
    //     .use('svg-sprite-loader')
    //     .loader('svg-sprite-loader')
    //     .options({
    //       symbolId: 'icon-[name]'
    //     })
    //     .end()
  }
};
