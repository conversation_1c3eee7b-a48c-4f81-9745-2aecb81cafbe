## Build Setup

``` bash
# 通过命令行进入yq_web目录下，执行以下命令，安装依赖包
npm install 或 cnpm install

# 卸载未被使用的模块以加快打包速度
npm prune

# 启动热部署服务，用于开发调试 localhost:8080
npm run dev

# 构建压缩代码，用于生产环境
npm run build

# 构建并开启分析服务,分析处理情况
npm run analy

# 测试环境打包-速度快不压缩、不提取css
npm run test

# 预打包，通用且不会修改的组件生成静态dll
npm run dll
```

package.json
  "scripts": "命令执行入口"
  "dependencies": "生产环境依赖包",
  "devDependencies": "开发环境依赖包，如打包工具、less工具等"

# 请求数据资源组件 - vue-resource
    用法比较简单，详见文档
    https://www.npmjs.com/package/vue-resource

# UI组件iview3
    http://v3.iviewui.com/components/color

# 右键菜单组件 - vue-contextmenujs
    详细参数见文档 (由于有bug，本地拉取了版本进行修改，参见http://*************:3000/trs-common/vue-contextmenujs)
    https://www.npmjs.com/package/vue-contextmenujs

# 无限菜单组件
    http://*************:3000/trs-common/unlimited-menu.git

# 时间处理组件 - momentjs
    http://momentjs.cn/
# svg-icon svg图标
<svg-icon iconClass="svg名字" className="class"/>
其它说明
--------
    该版本中使用了ejs语法，如果看到类似<% %>标记，它并非jsp，而是一种方便的模板语言
    详情见官网：https://ejs.bootcss.com/
