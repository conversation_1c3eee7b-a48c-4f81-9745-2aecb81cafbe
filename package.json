{"name": "home-web", "group": "trs-jn", "version": "1.1.361", "description": "jn门户系统", "author": "slzs", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "build": "node build/build.js", "test": "node build/build.js --extract=false", "push": "node build/push.js", "push403": "npm run push 403", "dll": "webpack --config build/webpack.dll.config.js", "analy": "npm run build --report", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"axios": "^0.21.1", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-es2015": "^6.24.1", "clipboard": "^2.0.6", "crypto-js": "^4.1.1", "echarts": "4.9", "echarts-wordcloud": "^1.1.3", "iview": "^3.4.2", "jquery": "^3.6.0", "moment": "^2.29.1", "muuri": "^0.4.1", "odometer": "^0.4.8", "trs-tool-highlight": "^1.0.4", "trs-tool-matcher": "^1.0.9", "vue": "^2.5.13", "vue-clipboard2": "^0.3.0", "vue-contextmenujs": "^1.3.13-1", "vue-cookies": "^1.7.0", "vue-infinite-scroll": "^2.0.2", "vue-odometer": "^1.0.2", "vue-resource": "^1.5.2", "vue-router": "^3.0.1", "vuex": "^3.6.2"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.25.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.6.0", "babel-preset-stage-2": "^6.24.1", "chalk": "^2.0.1", "chokidar": "^3.5.1", "copy-webpack-plugin": "^4.5.4", "css-loader": "^1.0.1", "file-loader": "^2.0.0", "friendly-errors-webpack-plugin": "^1.7.0", "html-webpack-plugin": "^3.2.0", "js-md5": "^0.8.3", "less": "^2.7.2", "less-loader": "^4.0.4", "mini-css-extract-plugin": "^0.4.1", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^5.0.1", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "scp2": "^0.5.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "ssh2": "^1.15.0", "svg-sprite-loader": "^3.8.0", "svgo": "1.3.2", "svgo-loader": "^3.0.0", "uglifyjs-webpack-plugin": "^2.2.0", "url-loader": "^1.1.2", "vue-loader": "^15.4.2", "vue-style-loader": "^4.1.2", "vue-template-compiler": "^2.5.17", "webpack": "^4.23.1", "webpack-bundle-analyzer": "^3.0.3", "webpack-cli": "^3.1.2", "webpack-dev-server": "^3.1.10", "webpack-merge": "^4.1.4"}, "engines": {"node": ">= 8.11.4", "npm": ">= 6.4.1"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 11"]}