'use strict'
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require("path");
const { version } = require('../package.json'); // 获取 package.json 中的版本号

module.exports = {
  dev: require('./dev.env'),
  build: {
    env: require('./prod.env'),
    // Template for index.html
    index: path.resolve(__dirname, `../dist/${version}/index.html`),

    // Paths
    assetsRoot: path.resolve(__dirname, `../dist/${version}`),
    assetsSubDirectory: "static",
    assetsPublicPath: "./",

    /**
     * Source Maps
     */
    productionSourceMap: false, // true生成sourcemap便于调试，体积较大较为耗时
    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ['js', 'css'],
    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report
  }
}
