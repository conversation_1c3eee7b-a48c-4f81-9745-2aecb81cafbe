'use strict'
const path = require('path')
const utils = require('./utils')
const webpack = require('webpack')
const config = require('../config')
const merge = require('webpack-merge')
const baseWebpackConfig = require('./webpack.base.conf')
const CopyWebpackPlugin = require('copy-webpack-plugin')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const MiniCssExtractPlugin = require("mini-css-extract-plugin")

const env = config.build.env
if (process.env.npm_lifecycle_event == 'build') {
  console.log("\n< 生产模式 >，抽离css会多花费少量时间...\n\n*** 测试时可使用npm run test ***\n");
} else {
  console.log("\n< 测试模式 >，不抽离css,\n\n=== 生产环境需使用 npm run build ===\n");
}
const webpackConfig = merge(baseWebpackConfig, {
  mode: process.env.npm_lifecycle_event == 'build' ? 'production' : 'development',
  module: {
    rules: utils.styleLoaders({
      sourceMap: config.build.productionSourceMap,
      //extract: process.env.npm_lifecycle_event == 'build'
      extract: false
    })
  },
  devtool: config.build.productionSourceMap ? config.build.devtool : false,
  output: {
    path: config.build.assetsRoot,
    filename: utils.assetsPath('js/[name].[chunkhash].js'),
    chunkFilename: utils.assetsPath('js/[id].[chunkhash].js')
  },
  plugins: [
    // http://vuejs.github.io/vue-loader/en/workflow/production.html
    new webpack.DefinePlugin({
      'process.env': env
    }),
    // 提取css组合到指定文件
    new MiniCssExtractPlugin({
      filename: utils.assetsPath('css/[name].[contenthash].css'),
      chunkFilename: utils.assetsPath('css/[id].[contenthash].css')
    }),
    // 提取压缩css Compress extracted CSS. We are using this plugin so that possible
    // 去除重复css代码，可能导致css顺序问题 duplicated CSS from different components can be deduped.
    //new OptimizeCSSPlugin({
    //  cssProcessorOptions: {
    //    safe: true
    //  }
    //}),
    // generate dist index.html with correct asset hash for caching.
    // you can customize output by editing /index.html
    // see https://github.com/ampedandwired/html-webpack-plugin
    new HtmlWebpackPlugin({
      filename: config.build.index,
      template: 'index.html',
      inject: true,
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeAttributeQuotes: true
      },
      chunksSortMode: 'dependency',
      // chunks: ['manifest', 'vendor', 'app']
      chunks: ['app']
    }),
    // copy custom static assets
    new CopyWebpackPlugin([{
      from: path.resolve(__dirname, '../static'),
      to: config.build.assetsSubDirectory,
      ignore: ['.*', 'README.md']
    }]),
    // dll模式下不能使用vue-dev-tool工具debug    
    // 通用的一些工具
    new webpack.DllReferencePlugin({
      context: __dirname,
      manifest: require('./dll/common-manifest.json')
    }),
    // new UglifyJsPlugin({
    //   uglifyOptions: {
    //     compress: {
    //       warnings: false,
    //       drop_console: true, //console
    //       // pure_funcs: ['console.log'] //移除console
    //     }
    //   },
    //   sourceMap: config.build.productionSourceMap,
    //   parallel: true
    // }),
    // iview的组件
    new webpack.DllReferencePlugin({
      context: __dirname,
      manifest: require('./dll/iview-manifest.json')
    }),
    // Echarts相关的
    // new webpack.DllReferencePlugin({
    //   context: __dirname,
    //   manifest: require('./dll/echarts-manifest.json')
    // }),
    // moment相关的
    new webpack.DllReferencePlugin({
      context: __dirname,
      manifest: require('./dll/moment-manifest.json')
    })
  ]
})

if (config.build.productionGzip) {
  const CompressionWebpackPlugin = require('compression-webpack-plugin')

  webpackConfig.plugins.push(
    new CompressionWebpackPlugin({
      asset: '[path].gz[query]',
      algorithm: 'gzip',
      test: new RegExp(
        '\\.(' +
        config.build.productionGzipExtensions.join('|') +
        ')$'
      ),
      threshold: 10240,
      minRatio: 0.8
    })
  )
}

if (config.build.bundleAnalyzerReport) {
  const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
  webpackConfig.plugins.push(new BundleAnalyzerPlugin())
}

module.exports = webpackConfig