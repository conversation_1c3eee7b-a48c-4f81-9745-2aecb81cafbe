{"name": "common_library", "content": {"../node_modules/webpack/buildin/global.js": {"id": 1, "buildMeta": {"providedExports": true}}, "../node_modules/timers-browserify/main.js": {"id": 2, "buildMeta": {"providedExports": true}}, "../node_modules/setimmediate/setImmediate.js": {"id": 3, "buildMeta": {"providedExports": true}}, "../node_modules/process/browser.js": {"id": 4, "buildMeta": {"providedExports": true}}, "../node_modules/vue/dist/vue.esm.js": {"id": 143, "buildMeta": {"exportsType": "namespace", "providedExports": ["EffectScope", "computed", "customRef", "default", "defineAsyncComponent", "defineComponent", "del", "effectScope", "getCurrentInstance", "getCurrentScope", "h", "inject", "isProxy", "isReactive", "is<PERSON><PERSON><PERSON>ly", "isRef", "isShallow", "mark<PERSON>aw", "mergeDefaults", "nextTick", "onActivated", "onBeforeMount", "onBeforeUnmount", "onBeforeUpdate", "onDeactivated", "onErrorCaptured", "onMounted", "onRenderTracked", "onRenderTriggered", "onScopeDispose", "onServerPrefetch", "onUnmounted", "onUpdated", "provide", "proxyRefs", "reactive", "readonly", "ref", "set", "shallowReactive", "shallowReadonly", "shallowRef", "toRaw", "toRef", "toRefs", "triggerRef", "unref", "useAttrs", "useCssModule", "useCssVars", "useListeners", "useSlots", "version", "watch", "watchEffect", "watchPostEffect", "watchSyncEffect"]}}, "../node_modules/vue-resource/dist/vue-resource.esm.js": {"id": 144, "buildMeta": {"exportsType": "namespace", "providedExports": ["default", "Url", "Http", "Resource"]}}, "../node_modules/vue-router/dist/vue-router.esm.js": {"id": 146, "buildMeta": {"exportsType": "namespace", "providedExports": ["NavigationFailureType", "RouterLink", "RouterView", "START_LOCATION", "default", "isNavigationFailure", "version"]}}}}