// 测试环境
gl.serverURL = "./api";

gl.jbCountAPI = "/static/json/test-jbCount.json"; // 举报数量接口
gl.pyCountAPI = "/static/json/test-pyCount.json", // 辟谣数量接口

gl.waCountAPI = "/static/json/test-waCount.json";
gl.wlgjCountAPI = "/static/json/test-waCount.json";
gl.tbListAPI="/static/json/test-tbList.json"  // 网安通报列表接口

// 导航菜单
SYSTEM_CONFIG_XZ = [{
        "sysNo": "trs_jccz",
        "sysName": "舆情监测处置评价系统",
        "sysURL": "https://172.31.0.19:39094/api/login",
        "moduleNmae": "门户屏/系统入口",
        "logContent": "哨点直报系统"
    },
    {
        "sysNo": "trs_sdzb",
        "sysName": "舆情哨点直报系统",
        "sysURL": "https://172.31.0.20:39435/api/login",
        "moduleNmae": "门户屏/系统入口",
        "logContent": "哨点直报系统"
    }
];
