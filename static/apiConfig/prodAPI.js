//专网正式环境
gl.serverURL = "./api";
gl.jbCountAPI = "/jbApi/daping/v2/json/today.json", // 举报数量 代理转发 http://************/daping/v2/json/today.json

gl.pyCountAPI = "/jbApi/daping/v2/json/jnpy.json", // 辟谣数量接口 代理转发 http://************/daping/v2/json/jnpy.json

gl.waCountAPI = "/waApi"; // 代理转发 https://************
gl.unitCountAPI="/waApi/open/api/v1/company/getCompanyListPage"  //代理转发 网安单位数接口
gl.systemCountAPI="/waApi/open/api/v1/webmanage/getListPage"  //代理转发 网安系统数数接口
gl.ipCountAPI="/waApi/open/api/v1/asset/list"  //代理转发 网安ip资料数数接口
gl.aqCountAPI="/waApi/open/api/v1/retrieval/securityIncident"  //代理转发 网安安全事件数接口
gl.fxCountAPI="/waApi/open/api/v1/retrieval/getHiddenDanger"  //代理转发 网安风险数数接口
gl.tbListAPI="/waApi/open/api/v2/warn/list"  //代理转发 网安通报列表接口


gl.wlgjCountAPI = "/waApi/ailpha/screen/attack_query";  //代理转发 网安攻击数接口

// 导航菜单
SYSTEM_CONFIG_XZ = [{
        "sysNo": "trs_jccz",
        "sysName": "舆情监测处置评价系统",
        "sysURL": gl.isPad?"https://************:34193/api/login":"https://***********:34093/api/login",
        "moduleNmae": "门户屏/系统入口",
        "logContent": "哨点直报系统"
    },
    {
        "sysNo": "trs_sdzb",
        "sysName": "舆情哨点直报系统",
        "sysURL": gl.isPad?"https://************:34094/api/login":"https://***********:34094/api/login",
        "moduleNmae": "门户屏/系统入口",
        "logContent": "哨点直报系统"
    }
];
gl.homeUrl="https://10.61.23.75:34092/#/"