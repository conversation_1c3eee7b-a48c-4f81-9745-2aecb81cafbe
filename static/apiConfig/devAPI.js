//本地dev环境
gl.serverURL = "/api";

gl.develop=true

gl.jbCountAPI = "/static/json/test-jbCount.json"; // 举报数量接口

gl.pyCountAPI = "/static/json/test-pyCount.json", // 辟谣数量接口

gl.waCountAPI = "/static/json/test-waCount.json";
gl.wlgjCountAPI = "/static/json/test-waCount.json";
gl.unitCountAPI="/static/json/test-unitCount.json" ;
gl.systemCountAPI="/static/json/text-systemCount.json";  // 网安系统数数接口
gl.ipCountAPI="/static/json/test-ipCount.json";  // 网安ip资料数数接口
gl.aqCountAPI="/static/json/test-aqCount.json";  // 网安安全事件数接口
gl.fxCountAPI="/static/json/test-fxCount.json";  // 网安风险数数接口
gl.tbListAPI="/static/json/test-tbList.json";  // 网安通报列表接口

// 导航菜单
SYSTEM_CONFIG_XZ = [{
        "sysNo": "trs_jccz",
        "sysName": "舆情监测处置评价系统",
        "sysURL": "https://***********:39094/api/login",
        "moduleNmae": "门户屏/系统入口",
        "logContent": "监测处置系统"
    },
    {
        "sysNo": "trs_sdzb",
        "sysName": "舆情哨点直报系统",
        "sysURL": "https://***********:39435/api/login",
        "moduleNmae": "门户屏/系统入口",
        "logContent": "哨点直报系统"
    }
];