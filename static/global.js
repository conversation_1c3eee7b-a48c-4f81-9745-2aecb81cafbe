/**
 * 全局变量/函数
 * 此文件不通过编译处理，没有统一兼容处理，使用ES5
 * 修改此文件前应详细了解其特性，一般公用函数应写在src/assets/中
 */
var thisLink =
  document.getElementsByTagName("script")[
    document.getElementsByTagName("script").length - 1
  ].src;
// 全局变量
// 在 main.js 或相应的入口文件中
if (typeof window !== "undefined") {
  window.uino = window.uino || {};
  window.uino.refreshTime = 5000; // 设置默认值为 5 秒
  window.uino.taxCarouselTime = 10000;
}

var gl = {
  version: thisLink.substr(thisLink.lastIndexOf("v=") + 2), // 读取版本号，该版本号通过引用页参数传进来
  // 接口服务器地址(每个环境各自走代理，开发环境代理在dev.env.js)
  serverURL: "/api",
  jcczAPI: "/jcczapi",
  sdzbAPI: "/sdzbapi",
  wxPingApi: "/wxPingApi",
  jbCountAPI: "http://************/daping/v2/json/today.json",
  waCountAPI: window.location.host.startsWith("************")?"https://************:12443":"https://************",
  wlgjCountAPI: "/waApi/ailpha/screen/attack_query",
  unitCountAPI: "/waApi/open/api/v1/company/getCompanyListPage", //代理转发 网安单位数接口
  systemCountAPI: "/waApi/open/api/v1/webmanage/getListPage", //代理转发 网安系统数数接口
  ipCountAPI: "/waApi/open/api/v1/asset/list", //代理转发 网安ip资料数数接口
  aqCountAPI: "/waApi/open/api/v1/retrieval/securityIncident", //代理转发 网安安全事件数接口
  fxCountAPI: "/waApi/open/api/v1/retrieval/getHiddenDanger", //代理转发 网安风险数数接口
  tbCountAPI: "/waApi/open/api/v2/warn/list", //代理转发 网安通报列表接口
  aqrzCountAPI: "/waApi/ailpha/screen/seclog_query", //网安 安全日志数
  aqtbCountAPI: "/waApi/open/api/v2/warn/list", //网安 安全通报数
  // 用于存储菜单信息
  menu: {
    firstActive: "", // 一级菜单
    secondActive: "", // 二级菜单
    loginU: "",
  },
  isPad: window.location.host.startsWith("************"),
};

// 根据不同地址，加载不同配置
{
  var currentUrl = window.location.host;
  var loadConfig = "devAPI.js";
  // 根据URL判断并加载不同的JS文件
  if (currentUrl.startsWith("172.31")) {
    // 测试环境
    loadConfig = "testAPI.js";
  } else if (
    currentUrl.startsWith("10.61") ||
    currentUrl.startsWith("************")
  ) {
    // 对于另一个路径，加载另一份JS文件
    loadConfig = "prodAPI.js";
  }
  var script = document.createElement("script");
  script.src = "./static/apiConfig/" + loadConfig + "?v=" + gl.version;
  document.body.appendChild(script);
}

/**
 * 设置菜单激活状态
 */
menuActive = function (firstActive, secondActive) {
  gl.menu.secondActive = secondActive;
  if (secondActive && firstActive == null) {
    return; // 单独设置二级菜单
  }
  gl.menu.firstActive = firstActive;
};
