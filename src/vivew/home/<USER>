<template>
  <div class="homeFrame">
    <div class="header">济南市委网信办统一登录门户</div>
    <div class="content">
      <div class="title">
        {{
          loginWay == "验证码登录"
            ? "短信验证码登录"
            : loginWay == "人脸登录"
            ? "人脸识别"
            : "Ukey登录"
        }}
      </div>
      <p
        class="xian"
        :style="loginWay == '验证码登录' ? 'width:254px;' : ''"
      ></p>
      <!-- 人脸识别为开始 -->
      <div class="dynamic-img" v-show="loginWay == '人脸登录' && !loginLoading">
        <div class="zong">
          <div class="dynamic-xian"></div>
        </div>
      </div>
      <!-- 人脸识别中 -->
      <div
        class="dynamic-imgNew"
        v-show="loginWay == '人脸登录' && loginLoading"
      >
        <div class="zong">
          <div class="dynamic-xian"></div>
          <div class="dynamic-content">
            <!-- 识别中 -->
            <div v-show="loginStatus == 0">
              <video id="video" autoplay></video>
            </div>
            <!-- 识别失败 -->
            <svg-icon
              icon-class="识别失败"
              style="width: 128.3px; height: 128.3px"
              v-show="loginStatus == 1"
            />
            <!-- 识别成功 -->
            <svg-icon
              icon-class="识别成功"
              style="width: 128px; height: 138.62px"
              v-show="loginStatus == 2"
            />
          </div>
        </div>
      </div>
      <div class="dynamic-input" v-show="loginWay == '验证码登录'">
        <Input
          prefix="ios-contact"
          placeholder="请输入手机号"
          v-model="phone"
          style="width: 465px; margin: 96px 0 0 116px"
        >
          <svg-icon icon-class="手机" slot="prefix"
        /></Input>

        <Input
          prefix="ios-contact"
          placeholder="请输入验证码"
          v-model="verificationCode"
          style="width: 465px; margin: 36px 0 57.77px 116px"
        >
          <svg-icon icon-class="验证码" slot="prefix"
        /></Input>
        <div class="verify" @click="verifyPhone()">
          <span v-show="!sendFlag">获取验证码</span>
          <span v-show="sendFlag">{{ countdown }}</span>
          <span v-show="sendFlag && countdown != '重新发送'">秒后重发</span>
        </div>
      </div>

      <div class="ukey-login" v-show="loginWay == 'Ukey登录'">

        <span v-if="ukeyCertID == ''" style="font-size:20px;font-weight:600;">
         <svg-icon icon-class="叉" />
          请插入Ukey！
        </span>
        
        <Input
          prefix="ios-contact"
          placeholder="请输入用户名"
          v-model="userAccount"
          style="width: 456px; margin: 30px 0 58px 0px"
        >
          <svg-icon icon-class="用户名" slot="prefix"
        /></Input>
        <Input
          prefix="ios-contact"
          placeholder="请输入PIN码"
          v-model="ukeyPassword"
          style="width: 456px; margin: 30px 0 58px 0px"
        >
          <svg-icon icon-class="验证码" slot="prefix"
        /></Input>

      </div>

      <div
        class="btn"
        v-show="loginWay == '验证码登录' || loginWay == 'Ukey登录'"
        @click="noteLogin"
      >
        登录
      </div>
      <div
        class="btn"
        v-show="loginWay == '人脸登录' && !loginLoading"
        @click="handleStart"
      >
        开始人脸识别
      </div>
      <div
        class="btn"
        v-if="loginWay == '人脸登录' && loginLoading && loginStatus == 0"
        style="margin-top: 30px"
      >
        {{ sbz }}
      </div>
      <div
        class="btn"
        v-show="loginWay == '人脸登录' && loginLoading && loginStatus == 1"
        style="margin-top: 30px; cursor: pointer"
        @click="handleStart"
      >
        人脸识别失败，点击重新识别！
      </div>
      <div
        class="btn"
        v-show="loginWay == '人脸登录' && loginLoading && loginStatus == 2"
        style="margin-top: 30px"
      >
        识别成功
      </div>

      <div class="login-itm">
        <div
          v-for="itm in loginList"
          :key="itm"
          @click="changLogin(itm)"
          :style="loginWay == itm ? 'color:#5D87E4;' : ''"
        >
          <svg-icon :icon-class="itm" style="width: 16px; height: 16px" />
          {{ itm }}
          <span
            class="gxian"
            v-if="itm != 'Ukey登录'"
            :style="itm != '人脸登录' ? 'left:325px;' : ''"
          ></span>
        </div>
      </div>
    </div>

    <canvas id="canvas" width="300" height="424" style="display: none"></canvas>
  </div>
</template>

<script>
const CryptoJS = require("crypto-js"); // 引用AES源码js
const key = CryptoJS.enc.Utf8.parse("ADF2081720D2A3F1"); // 十六位十六进制数作为密钥
const iv = CryptoJS.enc.Utf8.parse("A2B46F813D55E34C"); //十六位十六进制数作为密钥偏移量

import $ from "jquery";

export default {
  data() {
    return {
      timerJpg: null, //判断是否开始定时器
      context: null,
      canvasJpg: null,
      video: null, //视频文件测试
      files: null, // 视频文件
      loginStatus: 0, //0是识别中，1识别失败， 2是失败成功
      loginLoading: false, //判断是否识别
      countdown: "",
      timerId: null,
      sendFlag: false, //判断短信是否已发送
      phone: "", //手机号
      verificationCode: "", //手机验证码
      loginWay: "人脸登录", //登录方式切换
      sbz: "识别中...",
      loginList: {
        rlLogin: "人脸登录",
        sms: "验证码登录",
        ukey: "Ukey登录",
      },
      userAccount: '',
      ukeyPassword:'',  //ukey PIN码
      ukeyCertID:'',//ukey ID
      randomString: '',
      signString: '',
      certString: ''
    };
  },
  methods: {
    //打开摄像头
    openVideo() {
      this.canvasJpg = document.getElementById("canvas");
      this.context = this.canvasJpg.getContext("2d");
      this.video = document.getElementById("video");
      if (navigator.mediaDevices === undefined) {
        navigator.mediaDevices = {};
      }
      //缺少getUserMedia属性
      if (navigator.mediaDevices.getUserMedia === undefined) {
        navigator.mediaDevices.getUserMedia = function (constraints) {
          // 首先获取现存的getUserMedia(如果存在)
          let getUserMedia =
            navigator.webkitGetUserMedia || navigator.mozGetUserMedia;
          // 浏览器不支持，返回错误信息
          if (!getUserMedia) {
            return Promise.reject(
              new Error("getUserMedia is not implemented in this browser")
            );
          }
          //使用Promise将调用包装到navigator.getUserMedia
          return new Promise(function (resolve, reject) {
            getUserMedia.call(navigator, constraints, resolve, reject);
          });
        };
      }
      let constraints = {
        audio: false,
        video: {
          width: 300,
          height: 300,
        },
      };
      let _this = this;
      navigator.mediaDevices
        .getUserMedia(constraints)
        .then(function (stream) {
          let video = document.querySelector("video");
          _this.mediaStreamTrack = stream;
          // 旧的浏览器可能没有srcObject
          if ("srcObject" in video) {
            video.srcObject = stream;
          } else {
            //避免在新的浏览器中使用它，因为它正在被弃用。
            video.src = window.URL.createObjectURL(stream);
          }
          video.onloadedmetadata = function (e) {
            video.play();
            //开始识别
            _this.getFile(1);
            //_this.setImage();
            //  setTimeout(_this.setImage(), 1000);
          };
        })
        .catch(function (err) {
          _this
            .$confirm(
              "是否前往查看设置教程?",
              "打开摄像头失败请检查浏览器设置或域名是否安全",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
            )
            .then(() => {
              // window.open("https://www.cnblogs.com/tuofei13/p/16636145.html", "_blank");
            });
        });
    },

    setImage() {
      let _this = this;
      // 点击，canvas画图
      _this.context.drawImage(_this.video, 0, 0, 300, 400);
      // 获取图片base64链接
      let image = this.canvasJpg.toDataURL("image/jpg");
      _this.files = image;
      console.log("this.files", _this.files, _this.dataURLtoFile(_this.files));
    },
    dataURLtoFile(dataurl) {
      let arr = dataurl.split(",");
      let mime = arr[0].match(/:(.*?);/)[1];
      let bstr = atob(arr[1]);
      let n = bstr.length;
      let u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], "jpg", { type: "image/jpg" });
    },
    // 关闭摄像头
    stopNavigator() {
      this.jpgFlag = false;
      this.video.srcObject.getTracks()[0].stop();
    },
    // 上传文件
    getFile(errorNum) {
      if (this.loginStatus == 2) {
        return;
      }
      this.setImage();
      let url = gl.serverURL + "/faceDetection";
      let parmas = new FormData();
      parmas.append("file", this.dataURLtoFile(this.files));
      this.$http.post(url, parmas).then((res) => {
        let result = res.body;
        if (result.status == 0) {
          this.sbz = "识别成功,正在登入...";

          this.loginStatus = 2;
          this.loginLoading = false;
          this.stopNavigator();
          sessionStorage.setItem("userName", result.data.userName);
          sessionStorage.setItem(
            "sysNos",
            result.data.sysNos ? result.data.sysNos.join(",") : ""
          );
          sessionStorage.setItem("userAccount", result.data.userAccount);
          sessionStorage.setItem("userId", result.data.userId);
          if (result.data.powers) {
            sessionStorage.setItem(
              "resources",
              JSON.stringify(result.data.powers)
            );
          }
          this.getUserToken();
        } else {
          if (result.status  == -2) {
             this.$Message.error({content: "您的系统账号因超过30天未登录已被停用，如有疑问请联系系统管理员", duration: 0, closable: true});
             return
            }
          errorNum++;
          
          if (errorNum >= 5) {
            this.$Message.error("人脸识别失败，请对准人脸");
            errorNum = 1;
          }
          this.sbz = "识别中... 请对准人脸";
          setTimeout(() => {
            this.getFile(errorNum);
          }, 2000);
        }
      });
    },

    getUserToken() {
      let url = gl.serverURL + "/tokenMsg";
      let params = { systemNo: "trs_home" };

      this.$http.get(url, { params: params }).then((response) => {
        let data = response.body;
        if (data.status == 0) {
          localStorage.setItem("tokens", data.data.token);
          this.getUserMsg();
        } else {
          localStorage.removeItem("tokens");
          this.$Message.error("登录失败，token获取错误");
        }
      });
    },
    //获取登陆用户
    getUserMsg() {
      let url = gl.serverURL + "/getUserMsg";
      let params = { systemNo: "trs_home" };

      this.$http.get(url, { params: params }).then((response) => {
        let data = response.body;
        if (data.status == 0 && data.data) {
          this.$Message.success("登陆成功");

          localStorage.setItem("userId", data.data.userId);
          localStorage.setItem("userName", data.data.userName);
          localStorage.setItem("organName", data.data.organName);
          localStorage.setItem("organId", data.data.organId);
          localStorage.setItem("iphone", data.data.userTelephone);
          localStorage.setItem("userAccount", data.data.userAccount);
          localStorage.setItem("resources", JSON.stringify(data.data.resource));
          localStorage.setItem(
            "sysNos",
            data.data.sysNos ? data.data.sysNos.toString() : ""
          );
          localStorage.setItem(
            "roleName",
            data.data.roleName ? data.data.roleName : ""
          );
          localStorage.setItem("departmentName", data.data.departmentName);
          localStorage.setItem("departmentId", data.data.departmentId);
          localStorage.setItem("browser", data.data.browser);
          localStorage.setItem("ip", data.data.ip);
          localStorage.setItem("organType", data.data.organType);
          console.log(data.data.organType);
          gl.loginU = {
            userId: data.data.userId,
            userName: data.data.userName,
            organId: data.data.organId,
            organName: data.data.organName,
            userAccount: data.data.userAccount,
            resources: data.data.resource,
            sysNos: data.data.sysNos,
          };
          this.$router.push("/gateWay");
        } else {
          localStorage.removeItem("userId");
          localStorage.removeItem("userName");
          localStorage.removeItem("organName");
          localStorage.removeItem("organId");
          localStorage.removeItem("organName");
          localStorage.removeItem("userAccount");
          localStorage.removeItem("resources");
          localStorage.removeItem("sysNos");
          localStorage.removeItem("iphone");
          localStorage.removeItem("roleName");
          localStorage.removeItem("organType");
          gl.loginU = {};
          this.$Message.error("登陆失败，账号信息获取失败");
        }
      });
    },
    // 开启人脸识别
    handleStart() {
      this.loginLoading = true;
      this.loginStatus = 0;
      this.openVideo();
    },
    // ukey 登录
    test_SOF_Login() {
    let certId = this.ukeyCertID;
    if (certId == "") {
        return;
    }
    let passwd = this.ukeyPassword;

    if (!this.userAccount) {
      return this.$Message.error('请输入用户名');
    }
    if (!passwd) {
        return this.$Message.error('请输入PIN码');
       
    }

    this.dealUkeyLogin(certId, passwd);
},
dealUkeyLogin(certId, passwd) {
  let params = {};
  params.userAccount = this.userAccount;
  console.log("params:" + JSON.stringify(params));
  
  this.$http.get(gl.serverURL + '/randomCode', {params:params}).then((res) => {
    if (res.body.status === 0) {
      this.randomString = res.body.data;
      //生成签名字符串
      let toSignString = this.userAccount + this.randomString;

      // 签名
      SOF_SignData(certId, toSignString, this.test_signData_callback, {certId:certId, passwd:passwd});
    } else {
      this.$Message.error('登录失败');
    }
  })
},
test_signData_callback(retObj) {
    if (retObj.retVal == "") {
        alert("数据签名失败!");
        return;
    }

    this.signString = retObj.retVal;
    var certId = retObj.ctx.certId;
    var passwd = retObj.ctx.passwd;
    let that = this;
    SOF_ExportUserCert(certId, function(retObj) {
      that.certString = retObj.retVal;
      
      let time_begin = new Date();
      SOF_Login(certId, passwd, that.test_login_callback, {begin:time_begin, certId:certId});
    });
},
test_login_callback(retObj) {
    var certId = retObj.ctx.certId;
    if (retObj.retVal) { //retObj.retVal表示sof_login结果true或false
        // form_xtx.id_login_out_res.value = "证书[" + certId + "]登录成功";
        console.log('验证ukey证书成功');
        let params = {
          userAccount: this.userAccount,
          serialNo:this.Encrypt(this.ukeyCertID),
          randomString: this.randomString,
          certString: this.certString,
          signString: this.signString
        }

        console.log(JSON.stringify(params));
        console.log(this.randomString);
        this.$http.post(gl.serverURL + '/ukeyLogin', params).then((res) => {
        let result = res.body;
        if (result.status == 0) {
           sessionStorage.setItem("userName", result.data.userName);
          sessionStorage.setItem(
            "sysNos",
            result.data.sysNos ? result.data.sysNos.join(",") : ""
          );
          sessionStorage.setItem("userAccount", result.data.userAccount);
          sessionStorage.setItem("userId", result.data.userId);
          if (result.data.powers) {
            sessionStorage.setItem(
              "resources",
              JSON.stringify(result.data.powers)
            );
          }
          this.getUserToken();
        }else{
          if (result.status  == -2) {
             this.$Message.error({content: "您的系统账号因超过30天未登录已被停用，如有疑问请联系系统管理员", duration: 0, closable: true});
          }
        }
        })
        
    } else {
       this.$Message.error('登录失败')
        // form_xtx.id_login_out_res.value = "证书[" + certId + "]登录失败";
		//当登录失败时，调用SOF_GetLastError取错误码，errcode为79时表示pin码复杂度不符合
		SOF_GetLastError(function(retObj) {
			var err = retObj.retVal;
			if (err == 79) {
				this.$Message.error("请核对pin码复杂度是否符合要求!");
			}
		}, retObj.ctx);
    }
    // var str_waring = "消耗时间" + (new Date() - retObj.ctx.begin) / 1000 + "秒";
    this.$Message.error(str_waring);
},
    // 短信登录
    noteLogin() {
      if (this.loginWay == 'Ukey登录') {
        this.test_SOF_Login();
        return 
      }
      if (!this.phone) {
        return this.$Message.warning("手机号不能为空！");
      }
      if (!/^1[3456789]\d{9}$/.test(this.phone)) {
        return this.$Message.error("请输入正确的手机号！");
      }
      if (!this.sendFlag) {
        return this.$Message.warning("请先发送验证码！");
      }
      if (!this.verificationCode) {
        return this.$Message.warning("请输入验证码！");
      }
      let url = gl.serverURL + "/checkSmsByNum";
      this.$http
        .post(url, { userNumber: this.phone, code: this.verificationCode })
        .then((res) => {
          let result = res.body;
          if (result.data == 0) {
            this.getUserToken();
          } else {
            if (result.data  == -2) {
             this.$Message.error({content: "您的系统账号因超过30天未登录已被停用，如有疑问请联系系统管理员", duration: 0, closable: true});
            }
            return this.$Message.error(result.message);
          }
        });
    },
    handSend() {
      if (!this.phone) {
        return this.$Message.error("手机号不能为空！");
      } else {
        if (!/^1[3456789]\d{9}$/.test(this.phone)) {
          return this.$Message.error("请输入正确的手机号！");
        }
      }
      if (this.sendFlag && this.countdown != "重新发送") {
        return;
      } else {
        this.handleMessage();
      }
    },
    // 验证手机号是否存在
    verifyPhone() {
      let url = gl.serverURL + "/checkUserNumber";
      let params = {
        userNumber: this.phone,
      };
      this.$http.get(url, { params: params }).then((res) => {
        let result = res.body;
        if (result.data > 0) {
          this.handSend();
          this.startCountdown();
        } else {
          return this.$Message.error("该手机号不存在！");
        }
      });
    },
    // 手机号存在发送短信
    handleMessage() {
      let url = gl.serverURL + "/sendSms";
      let params = {
        userNumber: this.phone,
      };
      this.$http.post(url, { userNumber: params.userNumber }).then((res) => {
        let result = res.body;
        if (result.data == 0) {
          this.sendFlag = true;
          this.startCountdown();
        } else {
          return this.$Message.error(result.message);
        }
      });
    },
    startCountdown() {
      if (this.timerId) {
        clearInterval(this.timerId); // 清除已有的定时器
      }

      this.countdown = "60"; // 更新显示的倒计时值

      let interval = setInterval(() => {
        if (this.countdown > 1) {
          this.countdown--; // 每次间隔减少1s
        } else {
          clearInterval(interval); // 当倒计时结束时清除定时器
          this.countdown = "重新发送"; // 更新显示的文本
        }
      }, 1000);

      this.timerId = interval; // 保存最新的定时器ID
    },
    // 切换登录方式
    changLogin(d) {
      console.log(d, "dddddddddd");
      this.loginWay = d;
      this.loginLoading = false;
      this.loginStatus = 0;
      if (this.loginWay == 'Ukey登录') {
        this.getUkey();
    }else {
      clearInterval(this.timerNew);
    }
    },
    getUkey () {
      let that = this
      SOF_GetUserList(function (retObj) {
        let strUserList = retObj.retVal;
        if (strUserList.indexOf("&&&") == -1) {
           that.ukeyCertID = ''
          //  clearInterval(that.timerNew);
           return
        }
        
        let i = strUserList.indexOf("&&&");
        if (i <= 0) {
          return;
        }
        let strOneUser = strUserList.substring(0, i);
        let strCertID = strOneUser.substring(
          strOneUser.indexOf("||") + 2,
          strOneUser.length
        );
        that.ukeyCertID = strCertID
    })
      this.timerNew = setTimeout(() => this.getUkey(), 3 * 1000);
    },
    // 加密方法
    Encrypt(word) {
      let encrypted = CryptoJS.AES.encrypt(word, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });
      return encrypted.toString();
    },
    hasSystemPermission(thisSysNo) {
      console.log(this.sysNos);
      return (
        this.sysNos &&
        this.sysNos.length > 0 &&
        this.sysNos.indexOf(thisSysNo) >= 0
      );
    },
    out() {
      this.$http.get(gl.serverURL + "/logout").then((response) => {
        let data = response.body;
        if (data.status == 0) {
          sessionStorage.removeItem("userName");
          sessionStorage.removeItem("sysNos");
          localStorage.removeItem("userName");
          localStorage.removeItem("sysNos");
          this.userName = "";
          this.logged = false;
        }
      });
    },
    loginFrame() {
      this.getUserInfo();

      this.toLog = false;
      this.logged = true;
    },
    loginModal() {
      this.toLog = true;
    },

    systemClick(sysUrl, sysNo) {
      if (!this.sysNo && (!this.logged || !this.hasSystemPermission(sysNo))) {
        this.sysNo = "";
        return;
      }
      //临时加上舆情态势的跳转
      if (sysNo == "yqts") {
        window.open(sysUrl, this.sysNo ? "_self" : "_blank");
        return;
      }
      if (sysNo == this.yunWeiSysNo) {
        window.open(this.yunWeiURL, this.sysNo ? "_self" : "_blank");
        return;
      }
      let url = gl.serverURL + "/tokenMsg";
      let params = { systemNo: sysNo, to: this.to };

      this.$http.get(url, { params: params }).then((response) => {
        let data = response.body;

        if (data.status == 0 && data.data) {
          $("#fortheticketvalue").val(data.data.token);
          $("#sessionId").val(data.data.sessionId);
          $("#cityFlag").val(this.cityFlag);

          if (this.to) $("#to").val(this.to);
          if (
            this.cityFlag &&
            this.cityFlag.length > 0 &&
            (!this.to || this.to.length == 0)
          ) {
            $("#to").val("jc");
          }
          $("#ticketmessage").attr("action", sysUrl);
          $("#ticketmessage").submit();
        } else {
          this.out();
          this.$Message.error({
            content: data.message,
            duration: 3,
            closable: true,
          });
        }
      });
    },

    getUserInfo() {
      let url = gl.serverURL + "/getUser";
      let $this = this;
      this.$http.get(url).then((response) => {
        let data = response.body;
        if (data.status == 0) {
          $this.userName = data.data.nickName;
          $this.sysNos = data.data.systemCodes;
          sessionStorage.setItem("userName", $this.userName);
          sessionStorage.setItem("sysNos", $this.sysNos);
          localStorage.setItem("region", this.$route.path);
          if (data.data.powers) {
            sessionStorage.setItem(
              "resources",
              JSON.stringify(data.data.powers)
            );
          }
          this.toLog = false;
          this.logged = true;
          this.userName = sessionStorage.getItem("userName");
          this.sysNos = sessionStorage.getItem("sysNos");
          // localStorage.setItem("userName", $this.userName);
          // localStorage.setItem("sysNos", $this.sysNos);
          // if (data.data.powers) {
          //   localStorage.setItem("resources", JSON.stringify(data.data.powers));
          // }
        } else {
          $this.out();
        }
      });
    },
  },
  mounted() {
    gl.home = this;
    // this.getUserInfo();
   
    // GetAllDeviceSN(function(retObj){
    //         let strDeviceList = retObj.retVal;
    //         console.log(strDeviceList,'strDeviceList');

    //         while (true) {
    //             let pos = strDeviceList.indexOf(";");
    //             if (pos <= 0 ) {
    //                 break;
    //             }
    //             let strOneDevice = strDeviceList.substring(0, pos);
    //             let objItem = new Option(strOneDevice, strOneDevice);
    //             let len = strDeviceList.length;
    //             strDeviceList = strDeviceList.substring(pos + 1, len);
    //         }
    //     });
  },
  created() {
    this.btnJSONData = SYSTEM_CONFIG_XZ;

    if (this.$route.path !== localStorage.getItem("region")) {
      return;
    }
  },
  beforeDestroy() {
    // 清除定时器，防止内存泄漏
    clearInterval(this.timerNew);
  },
};
</script>

<style lang="less" scoped>
.homeFrame {
  width: 100%;
  height: 1080px;
  background-size: cover;
  overflow: hidden;
  background: url("../../../static/img/homeBacground-1.png") no-repeat;
  .header {
    margin-top: 92.09px;
    text-align: center;
    text-shadow: 0px 3px 0px #032a90;
    font-family: DIN-Medium;
    color: #ffffff;
    font-size: 50px;
    letter-spacing: 10px;
    margin-bottom: 47.91px;
  }
  .content {
    margin: 0 auto;
    width: 690px;
    height: 740px;
    background: #fff;
    position: relative;
    box-shadow: 0px 1px 10px rgba(63, 64, 65, 0.4);
    .icons {
      position: absolute;
      right: 9px;
      top: 25px;
      cursor: pointer;
    }
    .iconsSan {
      position: absolute;
      right: 0px;
      top: 0px;
    }
    .title {
      font-family: DIN-Medium;
      color: #333333;
      font-size: 30px;
      text-align: center;
      padding-top: 78.09px;
      margin-bottom: 13.91px;
    }
    .xian {
      width: 166px;
      height: 3px;
      background: #5585ec;
      margin: 0 auto;
    }
    // 识别中背景
    .dynamic-imgNew {
      width: 335px;
      height: 335px;
      background: #fff;
      margin: 26px auto;
      overflow: hidden;
      .zong {
        height: 100%;
        width: 100%;
        position: relative;
        .dynamic-xian {
          height: 100%;
          width: 100%;
          margin: 0 auto;
          position: absolute;
          animation: slideYuan 4s linear infinite;
          background: url("../../../static/img/huan.png") no-repeat;
        }
        .dynamic-content {
          width: calc(~"100% - 104px");
          height: calc(~"100% - 104px");
          position: relative;
          top: 52px;
          left: 52px;
          border-radius: 50%;
          overflow: hidden;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }

    .dynamic-img {
      width: 290px;
      height: 290px;
      background: #fff;
      margin: 43px auto;
      overflow: hidden;
      background: url("../../../static/img/renlian.png") no-repeat;
      padding: 59px 54px;
      .zong {
        height: 100%;
        width: 100%;
        position: relative;
        overflow: hidden;
        .dynamic-xian {
          height: 100%;
          width: 100%;
          margin: 0 auto;
          position: absolute;
          background-color: transparent;
          background: linear-gradient(
            180deg,
            #c4d7fc 0%,
            rgba(214, 227, 253, 0.55) 30%,
            #f6f9fe 100%
          );
          animation: slideUp 2.5s linear infinite;
        }
      }
    }
    .dynamic-input {
      position: relative;
      /deep/.ivu-input-prefix {
        padding-left: 10px;
        top: 50%;
        transform: translateY(-50%);
        height: 18px;
      }
      /deep/.ivu-input {
        height: 72px;
        font-size: 18px;
        color: black;
        box-shadow: 0px 5px 59px rgba(170, 169, 169, 0.17) inset;
      }
      .verify {
        position: absolute;
        cursor: pointer;
        font-size: 18px;
        bottom: 81.77px;
        right: 127px;
      }
    }
    .btn {
      cursor: pointer;
      margin: 57.23px auto;
      background: #5585ec;
      width: 456px;
      height: 72px;
      border-radius: 4px;
      box-shadow: 0px 5px 59px rgba(170, 169, 169, 0.17) inset;
      font-family: MicrosoftYaHei;
      color: #ffffff;
      font-size: 24px;
      line-height: 72px;
      text-align: center;
    }

    .ukey-login {
      height: 333px;
      width: 456px;
      margin: 0px auto;
      padding-top: 20px;
      /deep/.ivu-input {
        height: 72px;
        line-height: 72px;
        font-size: 24px;
      }
      /deep/ .ivu-input-prefix {
        line-height: 72px;
      }
    }
  }
}
.login-itm {
  display: flex;
  justify-content: space-between;
  width: 456px;
  margin: 0 auto;
  position: relative;
  div {
    cursor: pointer;
    font-size: 16px;
  }
  .gxian {
    width: 1px;
    height: 18px;
    background: #dcdbdb;
    position: absolute;
    left: 150px;
    top: 3px;
  }
}
@keyframes slideYuan {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes slideUp {
  0% {
    bottom: -10%;
    opacity: 0;
    height: 0%;
  }
  70% {
    opacity: 0.8;
    height: 100%;
  }
  71% {
    opacity: 0.8;
    transform: translateY(-1%);
  }

  99% {
    transform: translateY(-100%);
  }
  100% {
    bottom: 0px;
    opacity: 0;
  }
}
</style>
