@charset "utf-8";
@import './theme-variate.less';
/* Grid */
.grid {
	position: relative;
	margin: 0px 0px 0px 0px;
	box-sizing: content-box;
	width: calc(~"100% + 25px");
	.item {
		position: absolute;
		width: calc(~"100% - 25px");
		height: 405px;
		max-width: @max-width - 25px;
		min-width: @min-width - 25px;
		margin-bottom: 20px;
		margin-right: 20px;
		z-index: 1;
		will-change: transform;
		&.h1 {
			height: 166px;
		}
		&.h2 {
			height: 431px;
		}
		&.h4 {
            height: 270px;
        }
		&.h3 {
			height: 596px;
		}
		&.h5 {
            height: 329px;
        }
		&.w2{
			width: calc(~"(100% - 25px)/2 - 10px");
			max-width: calc((@max-width - 25)/2 + - 10px);
			min-width: calc((@min-width - 25)/2 + - 10px);
		}
		&.w31{
            width: calc(~"(100% - 25px)/3 - 10px");
            max-width: calc((@max-width - 25)/3 + - 10px);
            min-width: calc((@min-width - 25)/3 + - 10px);
        }
        &.w32{
            width: calc(~"2 * (100% - 25px)/3 - 10px");
            max-width: calc((@max-width - 25) * 2 /3 + - 10px);
            min-width: calc((@min-width - 25) * 2 /3 + - 10px);
        }
		.item-title {
			line-height: 16px;
			font-size: 16px;
			margin-left: 20px;
			font-weight: bold;
			i {
				float: right;
				margin-right: 20px;
				cursor: pointer;
				color: #807E7E;
			}
		}
		.item-content {
			margin-top: 10px;
			background-color: white;
			width: 100%;
			height: calc(~"100% - 26px");
			border-radius: 3px;
			box-shadow: 0px 3px 10px #d5d5d5;
		}
		.item-content-chart {
            margin-top: 10px;
            background-color: white;
            width: 100%;
            height: calc(~"100% - 25px");
            border-radius: 3px;
            box-shadow: 0px 3px 10px #d5d5d5;
        }
	}
}

.item.muuri-item-positioning {
	z-index: 2;
}

.item.muuri-item-dragging,
.item.muuri-item-releasing {
	z-index: 9999;
}

.item.muuri-item-dragging {
	cursor: move;
}

.item.muuri-item-hidden {
	z-index: 0;
}