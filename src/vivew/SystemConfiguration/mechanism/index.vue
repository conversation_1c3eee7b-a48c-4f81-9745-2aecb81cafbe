<template>
  <div class="content">
    <!-- 模态框 -->
    <div>
      <!-- 新建机构模态框 -->
      <Modal
        v-model="addOrganModal"
        :title="addTitle"
        :loading="addOrganLoading"
      >
        <div style="display: flex; align-items: baseline; white-space: nowrap;">
          {{
            !selectPId ? (addTitle === "新建机构" ? "机构" : "部门") : "部门"
          }}名称：
          <span
            v-if="selectPId || (selectPId == 0 && addTitle === '新建一级部门')"
            style="width: 100%;"
          >
            <Input
              v-model="newOrganName"
              placeholder="请输入部门名称"
              style="width: 85%;"
            />
          </span>
          <!-- <span v-else>
          <Input
            v-model="newOrganName"
            placeholder="请输入机构名称"
            style="width: 85%"
          />
        </span> -->
          <span v-else style="display: inline-block; width: 86%;">
            <div style="display: flex; align-items: center;">
              <Input
                v-model="newOrganName"
                placeholder="请输入机构名称"
                style="width: 55%;"
              />

              <span style="white-space: nowrap; margin-left: 10px;"
                >机构类别：</span
              ><Select
                class="newUserPrivilege"
                v-model="organType"
                clearable
                style="width: 40%;"
              >
                <Option
                  :disabled="
                    item.id == 1 &&
                    depList[0] &&
                    depList[0].organName == '市委网信办'
                  "
                  v-for="item in organUnitList"
                  :value="item.id"
                  :key="item.id"
                  >{{ item.name }}</Option
                >
              </Select>
            </div>
            <div style="margin: 20px 0px 0px -61px;">
              <span>联系电话：</span>
              <Input
                v-model="organTelephone"
                placeholder="请输入机构联系电话"
                style="width: 42.4%;"
              />
            </div>
          </span>
        </div>

        <div slot="footer">
          <Button
            size="large"
            @click="
              newOrganName = '';
              addOrganModal = false;
            "
            >取消</Button
          >
          <Button size="large" type="primary" @click="addOrgan">确认</Button>
        </div>
        <!-- <div
          v-if="!(selectPId || (selectPId == 0 && addTitle === '新建一级部门'))"
          class="input-item"
        >
          <span>机构对应省份：</span>
          <div class="inline">
            <Select v-model="provinceId" style="width: 394px">
              <Option
                v-for="item in provinceList"
                :value="item.id"
                :key="item.id"
                >{{ item.allName }}</Option
              >
            </Select>
          </div>
        </div> -->
      </Modal>

      <!-- 修改部门构模态框 -->
      <Modal
        v-model="editDepModal"
        :title="editTitle"
        @on-ok="validateEditName"
        :loading="editDepLoading"
        @on-cancel="
          newDepName = '';
          editDepModal = false;
        "
      >
        <div style="display: flex; align-items: baseline; white-space: nowrap;">
          {{ !selectPId ? "机构" : "部门" }}名称：
          <span v-if="!selectPId" style="display: inline-block; width: 86%;">
            <!-- <Input
            v-model="newDepName"
            placeholder="请输入机构名称"
            style="width: 85%"
          /> -->
            <div style="display: flex; align-items: center;">
              <Input
                v-model="newDepName"
                placeholder="请输入机构名称"
                style="width: 55%;"
              />

              <span style="white-space: nowrap; margin-left: 10px;"
                >机构类别：</span
              ><Select
                class="newUserPrivilege"
                v-model="newOrganType"
                clearable
                style="width: 40%;"
              >
                <Option
                  :disabled="
                    item.id == 1 &&
                    depList[0] &&
                    depList[0].organName == '市委网信办'
                  "
                  v-for="item in organUnitList"
                  :value="item.id"
                  :key="item.id"
                  >{{ item.name }}</Option
                >
              </Select>
            </div>
            <div style="margin: 20px 0px 0px -61px;">
              <span>联系电话：</span>
              <Input
                v-model="newOrganTelephone"
                placeholder="请输入机构联系电话"
                style="width: 42.4%;"
              />
            </div>
          </span>
          <span v-else style="width: 100%;">
            <Input
              v-model="newDepName"
              placeholder="请输入部门名称"
              style="width: 85%;"
            />
          </span>
        </div>
      </Modal>

      <!-- 添加账号模态框 -->
      <Modal
        v-model="addAccountModal"
        title="新建账号"
        @on-ok="valiDataAndCommit"
        :loading="addLoading"
      >
        <p class="pad10">
          <span>
            在<span :title="selectDepName" class="name ellipsis">{{
              selectDepName
            }}</span
            >下新建账号：
          </span>
        </p>
        <div class="pad10">
          <span class="w70">用户名：</span
          ><Input
            v-model="newUserAccount"
            clearable
            placeholder="请输入用户名"
            @on-blur="generatePassword"
            style="width: 80%;"
          />
        </div>
        <div class="pad10">
          <span class="w70">用户昵称：</span
          ><Input
            v-model="newUserName"
            clearable
            placeholder="请输入用户昵称"
            style="width: 80%;"
          />
        </div>
        <div class="pad10">
          <span class="w70">联系方式：</span
          ><Input
            v-model="newUserTelePhone"
            clearable
            placeholder="请输入用户联系方式"
            style="width: 80%;"
          />
        </div>
        <div class="pad10">
          <span class="w70">初始密码：</span
          ><Input
            v-model="pwdAutoCreated"
            clearable
            placeholder="仅接受6位数字"
            style="width: 80%;"
          />
        </div>
        <div class="pad10">
          <span class="w70">用户角色：</span>
          <Select
            class="newUserPrivilege"
            v-model="defaultRole"
            clearable
            style="width: 80%;"
          >
            <Option
              v-for="item in roleList"
              :value="item.roleId"
              :key="item.roleId"
              >{{ item.roleName }}</Option
            >
          </Select>
        </div>
        <div class="pad10">
          <span class="w70">系统编码：</span>
          <Select
            class="newUserPrivilege"
            v-model="systemCoding"
            clearable
            multiple
            style="width: 80%;"
          >
            <Option
              v-for="item in systemCodingList"
              :value="item.sysNo"
              :key="item.sysNo"
              >{{ item.sysName }}</Option
            >
          </Select>
        </div>

        <div class="pad10" style="position: relative;">
          <span class="w70">ukey码：</span
          ><Input v-model="newUkey" clearable style="width: 80%;" />
          <span class="acquire" @click="getUkey">
            自动获取
          </span>
        </div>
        <div class="pad10">
          <span class="w70">山东通Id：</span
          ><Input
            v-model="sdtId"
            clearable
            placeholder="请输入山东通Id"
            style="width: 80%;"
          />
        </div>
        <div class="pad10">
          <span class="w70">用户类型：</span>
          <Select
            class="newUserPrivilege"
            v-model="isDistrict"
            clearable
            style="width: 80%;"
          >
            <Option :value="0">系统用户</Option>
            <Option :value="1">街道用户</Option>
          </Select>
        </div>
        <div class="pad10" style="display: flex;">
          <span class="w70">图片上传：</span>
          <Upload
            v-if="!file"
            action=""
            accept=".jpg"
            :format="['jpg']"
            :show-upload-list="false"
            :before-upload="uploadFile"
          >
            <span style="text-decoration: underline; cursor: pointer;"
              >上传文件</span
            >
          </Upload>
          <div v-if="file != null">
            已上传文件: {{ file.name }}
            <span class="remove" @click="removeFile">×</span>
          </div>
        </div>
      </Modal>

      <!-- 部门变更模态框 -->
      <Modal
        v-model="editModal"
        :mask-closable="false"
        :closable="false"
        title="部门变更"
      >
        <div class="dep">
          <div class="up">
            将<span :title="selectedUserName" class="name">{{
              selectedUserName
            }}</span
            >所属部门变更为：
          </div>
          <div class="down">
            <Spin fix size="large" v-show="depModalLoading"></Spin>
            <Tree
              :data="depModalList"
              empty-text="''"
              @handle-organ="handleOrgan"
              @on-select-change="choseDep"
            ></Tree>
          </div>
        </div>
        <div slot="footer">
          <Button
            size="large"
            @click="
              editModal = false;
              selectedUserName = '';
            "
            >取消</Button
          >
          <Button size="large" type="primary" @click="editConfirm()"
            >确认</Button
          >
        </div>
      </Modal>

      <Modal
        v-model="confirmModal"
        title="部门变更确认"
        @on-ok="changeDep"
        @on-cancel="confirmModal = false"
      >
        <center>
          是否确认将用户
          <span :title="selectedUserName" class="name">{{
            selectedUserName
          }}</span>
          所属部门变更为
          <span :title="selectedDepName" class="name">{{
            selectedDepName | filterOrganName
          }}</span>
        </center>
      </Modal>

      <Modal
        v-model="deleteModal"
        :title="deleteTitle"
        @on-ok="deleteDep"
        @on-cancel="deleteModal = false"
      >
        <center>
          是否确认删除
          <span :title="selectDepName" class="name">{{
            selectDepName | getDeleteName
          }}</span>
          {{ !selectPId ? "机构" : "部门" }}
        </center>
      </Modal>

      <!-- 重置密码 -->
      <Modal
        v-model="resetModal"
        title="重置密码"
        @on-ok="resetPassword"
        @on-cancel="resetModal = false"
      >
        <center>是否确认重置该用户密码？</center>
      </Modal>
      <!-- 添加账号模态框 -->
      <Modal
        v-model="openChangedRole"
        title="编辑用户"
        @on-ok="changeRoleMed"
        @on-cancel="openChangedRole = false"
      >
        <div class="pad10">
          <span class="w70">用户名：</span
          ><Input
            v-model="userData.userAccount"
            clearable
            disabled
            placeholder="请输入用户名"
            @on-blur="generatePassword"
            style="width: 80%;"
          />
        </div>
        <div class="pad10">
          <span class="w70">用户昵称：</span
          ><Input
            v-model="userData.userName"
            clearable
            placeholder="请输入用户昵称"
            style="width: 80%;"
          />
        </div>
        <div class="pad10">
          <span class="w70">联系方式：</span
          ><Input
            v-model="userData.userTelephone"
            clearable
            placeholder="请输入用户联系方式"
            style="width: 80%;"
          />
        </div>
        <div class="pad10">
          <span class="w70">初始密码：</span
          ><Input
            v-model="userData.userPassword"
            clearable
            disabled
            placeholder="仅接受6位数字"
            type="password"
            style="width: 80%;"
          />
        </div>
        <div class="pad10">
          <span class="w70">用户角色：</span>
          <Select
            class="changeRoleClass"
            v-model="changedRoleId"
            clearable
            style="width: 80%;"
          >
            <Option
              v-for="item in roleList"
              :value="item.roleId"
              :key="item.roleId"
              >{{ item.roleName }}</Option
            >
          </Select>
        </div>
        <div class="pad10">
          <span class="w70">系统编码：</span>
          <Select
            class="newUserPrivilege"
            v-model="userData.systemCodes"
            clearable
            multiple
            style="width: 80%;"
          >
            <Option
              v-for="item in systemCodingList"
              :value="item.sysNo"
              :key="item.sysNo"
              >{{ item.sysName }}</Option
            >
          </Select>
        </div>
        <div class="pad10" style="position: relative;">
          <span class="w70">ukey码：</span>
          <Input
            v-model="userData.serialNo"
            clearable
            placeholder="仅接受6位数字"
            type="text"
            style="width: 80%;"
          />
          <span class="acquire" @click="getUkey(1)">
            自动获取
          </span>
        </div>
        <div class="pad10">
          <span class="w70">山东通Id：</span>
          <Input
            v-model="userData.sdtId"
            clearable
            placeholder="请输入山东通Id"
            style="width: 80%;"
          />
        </div>
        <div class="pad10">
          <span class="w70">用户类型：</span>
          <Select
            class="newUserPrivilege"
            v-model="userData.isDistrict"
            clearable
            style="width: 80%;"
          >
            <Option :value="0">系统用户</Option>
            <Option :value="1">街道用户</Option>
          </Select>
        </div>
        <div class="pad10" style="display: flex;">
          <span class="w70">图片上传：</span>
          <Upload
            v-if="!file"
            action=""
            accept=".jpg"
            :format="['jpg']"
            :show-upload-list="false"
            :before-upload="uploadFile"
          >
            <span style="text-decoration: underline; cursor: pointer;"
              >上传文件</span
            >
          </Upload>
          <div v-if="file != null">
            已上传文件: {{ file.name }}
            <span class="remove" @click="removeFile">×</span>
          </div>
        </div>
      </Modal>
    </div>
    <!-- 实体 -->
    <div class="flex spacing">
      <!-- 机构目录 -->
      <div v-if="isShowOrgan" class="left shadow">
        <div v-if="hasSuperPerm" class="head">
          <span class="text super bold" @click="showOrganList">机构目录</span>
          <span
            class="plus bold"
            title="添加新机构"
            @click="addNewOrgan"
            v-if="hasPermission('/sys/organ/insert')"
            >+</span
          >
        </div>
        <div v-else class="head">
          <span class="text bold">机构目录</span>
          <span class="plus bold"></span>
        </div>
        <EasyScrollbar :barOption="myBarOption" ref="scrollVue">
          <div v-if="!isDepListNull" class="tree">
            <Spin fix size="large" v-show="depLoading"></Spin>
            <Tree
              ref="leftTree"
              :data="depList"
              empty-text
              @handle-organ="handleOrgan"
              @on-select-change="selectMenu"
            ></Tree>
          </div>
          <div v-else class="datanull organ">
            <nodata-page :classVal="'nodata2'"></nodata-page>
          </div>
        </EasyScrollbar>
      </div>

      <div v-else class="left shadow"></div>

      <!-- 机构列表 -->
      <div v-if="hasSuperPerm && !isSingle" class="right">
        <!-- 标题 -->
        <div v-if="isShowOrganList" class="flex-between">
          <div class="hl">机构列表</div>
          <div class="hr">
            共有机构<span class="bold">{{ organCount }}</span
            >个， 共有用户<span class="bold">{{ userCount }}</span
            >个
          </div>
        </div>

        <!-- 列表 -->
        <div v-if="isShowOrganList" class="olist shadow">
          <ul class="ul bgc">
            <li>序号</li>
            <li>机构名称</li>
            <li>用户数</li>
            <li>创建日期</li>
          </ul>
          <div class="relative" v-if="!isOrganListNull">
            <Spin fix size="large" v-show="organLoading"></Spin>
            <ul class="ul" v-for="(item, index) of organList" :key="index">
              <li>{{ index + 1 + (pageNo - 1) * pageSize }}</li>
              <li>
                <span class="ellipsis" :title="item.organName">{{
                  item.organName
                }}</span>
              </li>
              <li>{{ item.userCount }}</li>
              <li>{{ item.createTime | formatYearDate }}</li>
            </ul>
            <ul class="ul page">
              <Page-bar
                @refreshPage="queryOrgPageData"
                :total="organCount"
                :page-size="pageSize"
                :current="pageNo"
              ></Page-bar>
            </ul>
          </div>
          <div v-else class="datanull">
            <nodata-page :classVal="'nodata2'"></nodata-page>
          </div>
        </div>
      </div>

      <!-- 用户列表 -->
      <div v-else class="right">
        <!-- 标题 -->
        <div class="flex-between">
          <div class="hl">
            <span class="tn-width" :title="selectDepName | getFinalName">{{
              selectDepName | getFinalName
            }}</span>
            <span>用户列表</span>
          </div>
          <div class="hr">
            <span class="m10">
              <!-- <Poptip
                confirm
                title="是否确定要删除您选中的用户，请谨慎操作！"
                @on-ok="delUser()"
              >
                <Button icon="md-add" @click="delUserOne()">批量删除</Button>
              </Poptip> -->
              <Button
                icon="md-add"
                @click="delUserOne"
                v-if="hasPermission('/sys/user/delete')"
                >批量删除</Button
              >
            </span>
            <span class="m10">
              <Button
                icon="md-add"
                @click="clickAddBtn"
                v-if="hasPermission('/sys/user/insertOrUpdate')"
                >新建账号</Button
              >
            </span>
            <Button
              icon="md-swap"
              @click="openModal"
              v-if="hasPermission('/sys/user/update')"
              >部门变更</Button
            >
            <div class="m10">
              <Input
                v-model="searchWord"
                @on-search="searchDepUsers"
                search
                placeholder="输入用户昵称或用户名"
                style="width: 250px;"
              />
            </div>
            共有用户<span class="bold">{{ count }}</span
            >个
          </div>
        </div>

        <!-- 列表 -->
        <div class="ulist shadow">
          <ul class="ul bgc">
            <li>
              <Checkbox
                :indeterminate="indeterminate"
                :value="checkAll"
                @click.prevent.native="handleCheckAll"
              ></Checkbox>
            </li>
            <li>序号</li>
            <li>用户昵称</li>
            <li>用户名</li>
            <li>初始密码</li>
            <li>用户角色</li>
            <li>操作</li>
          </ul>
          <div class="relative" v-if="!isUserListNull">
            <Spin fix size="large" v-show="userLoading"></Spin>
            <CheckboxGroup
              v-model="checkAllGroup"
              @on-change="checkAllGroupChange"
            >
              <ul class="ul" v-for="(item, index) of userList" :key="index">
                <li><Checkbox :label="item.userId"></Checkbox></li>
                <li>{{ index + 1 + (pageNo - 1) * pageSize }}</li>
                <li>
                  <div class="user-info">
                    <div
                      class="ellipsis"
                      @mouseenter="
                        getUserInfo(index, item.userId, item.userInfo)
                      "
                      @mouseleave="showTip = false"
                    >
                      {{ item.userName }}
                    </div>
                    <div
                      v-show="showTip && index == activedRow"
                      class="tooltip"
                    >
                      <span class="info">{{ userInfo }}</span>
                    </div>
                  </div>
                </li>
                <li>
                  <span
                    class="ellipsis"
                    style="white-space: pre;"
                    :title="item.userAccount"
                    >{{ item.userAccount }}</span
                  >
                </li>
                <li>
                  <span class="ellipsis" :title="item.defaultPassword">{{
                    item.defaultPassword
                  }}</span>
                </li>
                <li>{{ item.roleName }}</li>
                <li>
                  <Button
                    type="text"
                    @click="prepareChangeRole(item)"
                    v-if="hasPermission('/sys/user/insertOrUpdate')"
                  >
                    <span class="blue">编辑用户</span>
                  </Button>
                  <Button
                    type="text"
                    @click="openResetModal(item)"
                    v-if="hasPermission('/sys/user/updatePassword')"
                  >
                    <span class="blue">重置密码</span>
                  </Button>
                  <Button type="text" @click="addWhitelist(item)">
                    <span class="blue">{{
                      item.white === 1 ? "移除白名单" : "添加白名单"
                    }}</span>
                  </Button>
                  <Poptip
                    confirm
                    title="是否确定要删除您选中的用户，请谨慎操作！"
                    @on-ok="delUser(item)"
                    placement="top-end"
                    v-if="hasPermission('/sys/user/delete')"
                  >
                    <Button type="text">
                      <span class="blue">删除用户</span>
                    </Button>
                  </Poptip>

                  <Poptip
                    confirm
                    :title="
                      '是否确定要' +
                      (item.status ? '停用' : '启用') +
                      '您选中的用户：' +
                      item.userName +
                      '，请谨慎操作！'
                    "
                    @on-ok="operateUser(item, !item.status)"
                    placement="top-end"
                    :disabled="item.ukeyOperateLoading"
                  >
                    <Button type="text" :loading="item.ukeyOperateLoading">
                      <span class="red" v-if="item.status">停用</span>
                      <span class="blue" v-else>启用</span>
                    </Button>
                  </Poptip>
                </li>
              </ul>
            </CheckboxGroup>
            <ul class="ul page">
              <Page-bar
                @refreshPage="queryUserPageData"
                :total="count"
                :page-size="pageSize"
                :current="pageNo"
              ></Page-bar>
            </ul>
          </div>
          <div v-else class="datanull">
            <nodata-page :classVal="'nodata2'"></nodata-page>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const CryptoJS = require("crypto-js"); // 引用AES源码js
const key = CryptoJS.enc.Utf8.parse("ADF2081720D2A3F1"); // 十六位十六进制数作为密钥
const iv = CryptoJS.enc.Utf8.parse("A2B46F813D55E34C"); //十六位十六进制数作为密钥偏移量

import { formatDate } from "./date.js";
import Tree from "./tree";
import PageUtils from "./pageUtil.js";
import PageBar from "./pageBar.vue";
import NodataPage from "../components/nodataPage.vue";
import { Form } from "iview";
//import urlandnameJSON from "@/assets/json/urlandname";
var app;
export default {
  data() {
    menuActive("/config", "/config/organ");
    return {
      hasSuperPerm: false, //是否拥有超管权限
      selectNode: {}, //当前选择的节点

      organId: 1, //当前用户所属机构的organId,从session中得到
      isSingle: true, //左侧目录树是否选中某个机构/部门

      /*当前选中的机构*/
      activeOrgan: {},
      selectDepName: "", //左侧目录树选中那个部门的部门名称
      selectDepCode: "", //当前选中的机构/部门层级编码
      selectPId: 0, //右键时选中的部门的父级部门id
      selectNodeId: 0, //右键时选中的当前节点的organId
      selectDepTitle: "", //当前选中的部门标题（名称）

      /*新建账户*/
      newUserName: "",
      newUserTelePhone: "",
      newUserAccount: "",
      pwdAutoCreated: "",
      sdtId: "",
      isDistrict: "",
      newUkey: "",
      addAccountModal: false,
      activeRoleId: 2,
      file: null,
      /**  roleList中角色的关键字和格式
       * 	 			roleId 				主键id
       *   			roleImgName   原始文件名
       *			  roleName  		角色名称
       *				roleImgPath		权限功能图片在ftp服务器中的获取路径，只保存文件名
       */
      roleList: [],
      //每次点开新建账号之后，角色选项出现的默认值
      defaultRole: "",
      //变更角色模态框开关
      openChangedRole: false,
      //该变量记录变更后的角色id
      changedRoleId: "",

      /* 重置密码 */
      activeUser: {},
      resetModal: false,

      //添加机构对话框
      provinceId: 110000,
      provinceList: [],
      addTitle: "", //添加对话框标题显示内容
      addLoading: true,
      newOrganName: "",
      organType: "", //新建机构id
      organTelephone: "", //新建机构联系方式
      organUnitList: [
        { id: 1, name: "市委网信办" },
        { id: 2, name: "区县网信办" },
        { id: 3, name: "市直单位" },
        { id: 4, name: "服务公司" },
      ], //新建机构选择机构类型
      isAddOrgan: false,
      addOrganModal: false,
      addOrganLoading: true,

      //修改和删除部门对话框绑定值
      editTitle: "", //修改对话框标题显示内容
      deleteTitle: "", //删除对话框标题显示内容
      newDepName: "",
      newOrganTelephone: "", //修改机构电话
      newOrganType: "", //修改机构id
      editLoading: true,
      deleteModal: false,
      editDepModal: false,
      editDepLoading: true,

      //部门变更对话框
      selectUsers: [], //已选中的用户的userId
      editModal: false,
      selectedOrganId: 1, //部门变更时选中的用户所在的机构id
      selectedDepName: "", //部门变更时选中的变更后的部门名称
      selectedUserName: "", //部门变更时选中的用户名

      //部门变更确认对话框
      confirmModal: false,
      editedDepCode: "", //部门变更时选中的机构/部门层级编码

      /*机构列表绑定数据*/
      depList: [],
      userCount: 0,
      organList: [],
      organCount: 0,
      depModalList: [],
      depLoading: false,
      organLoading: false,
      isDepListNull: false,
      isOrganListNull: false,
      depModalLoading: false,

      /*用户列表绑定数据*/
      count: 0,
      pageNo: 1,
      orgName: "",
      userList: [],
      userInfo: "",
      pageSize: 15,
      activedRow: 1,
      searchWord: "",
      showTip: false,
      checkAll: false,
      checkAllGroup: [],
      userLoading: false,
      indeterminate: false,
      isUserListNull: false,

      /*其它*/
      myBarOption: {
        barWidth: 3, //滚动条宽度
        zIndex: "auto", //滚动条z-Index
        railColor: "#eee", //导轨颜色
        barMarginRight: 0, //垂直滚动条距离整个容器右侧距离单位（px）
        barMaginBottom: 0, //水平滚动条距离底部距离单位（px)
        barOpacityMin: 0.3, //滚动条非激活状态下的透明度
        barColor: "#959595", //滚动条颜色
        autohidemode: true, //自动隐藏模式
        horizrailenabled: true, //是否显示水平滚动条
      },
      isShowOrganList: false, //是否显示机构列表
      isShowOrgan: false, //是否显示机构目录
      systemCoding: [],
      systemCodingList: SYSTEM_CONFIG_XZ,
      userData: {},
      socket: null,
      snTimeoutObj: null,
      ukeyNo: "",
      ukeyUserIndex: -1,
      ukeyStateLoading: true,
    };
  },
  beforeRouteLeave(to, form, next) {
    if (this.socket) {
      this.socket.close();
    }
    next();
  },
  methods: {
    addWhitelist(d) {
      let params = new FormData();
      params.append("userId", d.userId);
      params.append("white", d.white === 0);
      this.$http
        .post(gl.serverURL + "/sys/user/whitelist", params)
        .then((res) => {
          console.log(res);
          this.getDepUsers();
        });
    },
    // 自动获取ukey
    getUkey(type) {
      let that = this;
      SOF_GetUserList(function (retObj) {
        let strUserList = retObj.retVal;
        let i = strUserList.indexOf("&&&");
        if (i <= 0) {
          return;
        }
        let strOneUser = strUserList.substring(0, i);
        let strCertID = strOneUser.substring(
          strOneUser.indexOf("||") + 2,
          strOneUser.length
        );
        if (type) {
          that.userData.serialNo = strCertID;
        } else {
          that.newUkey = strCertID;
        }
      });
    },
    initSocket() {
      console.log("开始准备websocket");
      let self = this;
      //初始化websocket
      this.socket = new WebSocket("ws://127.0.0.1:30833");
      this.socket.onclose = function (event) {
        console.log("Client notified socket has closed");
        self.logc(event);
      };
      this.socket.onerror = function (event) {
        console.log("Client notified socket has error", event);
        self.logc(event);
      };
      this.socket.onmessage = function (event) {
        try {
          // console.log('Client received a message', event);
          self.logc(event);
        } catch (e) {
          console.log("message: " + e);
        }
      };
      this.socket.onopen = function (event) {
        console.log("Connected");
        self.logc(event);
      };
    },
    logc(ev) {
      if (ev.type == "message") {
        try {
          var myarray = JSON.parse(ev.data);
          if (myarray.FunName == "GetKeySerialNumber") {
            if (myarray.SerialNum) {
              console.log("获取到ukey了，" + myarray.SerialNum);
              if (myarray.SerialNum == "no insert key") {
                this.ukeyNo = "no insert key";
              } else {
                this.ukeyNo = this.Encrypt(myarray.SerialNum);
              }
            } else {
              console.log("获取SN失败");
              this.$Message.error({
                content: "获取ukey序列号失败，启用失败！",
                duration: 3,
                closable: false,
              });
            }
          } else if (myarray.FunName == "IsPresent") {
            this.ukeyStateLoading = false;
            if (!myarray.Ret) {
              this.ukeyNo = "no insert key";
              return;
            }
            var msg = {
              FunName: "GetKeySerialNumber",
            };
            this.socket.send(JSON.stringify(msg)); //获取ukey序列码
            console.log("开始准备获取ukey，" + new Date());
          }
        } catch (error) {
          console.log("logc: " + error);
        }
      }
    },
    // 加密方法
    Encrypt(word) {
      let encrypted = CryptoJS.AES.encrypt(word, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });
      return encrypted.toString();
    },
    dealSN(sn) {
      let ukeyUserId = this.userList[this.ukeyUserIndex].userId;
      let self = this;
      //启用
      this.$http
        .post(
          gl.serverURL + "/sys/user/enableUkey",
          { userId: ukeyUserId, serialNo: sn },
          { emulateJSON: true }
        )
        .then((resp) => {
          this.$set(
            this.userList[this.ukeyUserIndex],
            "ukeyOperateLoading",
            false
          );
          let ret = resp.body;
          let status = ret.status;
          this.ukeyNo = null;
          if (status == 0) {
            self.$set(self.userList[self.ukeyUserIndex], "serialNo", sn);
            self.$Message.success({
              content: "启用成功！",
              duration: 3,
              closable: false,
            });
          } else {
            self.$Message.error({
              content: ret.message,
              duration: 3,
              closable: false,
            });
          }
        });
    },
    ukeyOperate(item, itemIndex) {
      //启用、停用
      this.$set(item, "ukeyOperateLoading", true);
      this.ukeyUserIndex = itemIndex;
      let serialNo = item.serialNo;
      if (serialNo && serialNo.length > 0) {
        //停用
        this.$http
          .post(
            gl.serverURL + "/sys/user/invalidUkey",
            { userId: item.userId },
            { emulateJSON: true }
          )
          .then((resp) => {
            this.$set(item, "ukeyOperateLoading", false);
            let ret = resp.body;
            let status = ret.status;
            if (status == 0) {
              this.$set(item, "serialNo", "");
              this.$Message.success({
                content: "停用成功！请注意：启用时需要重新插入ukey！",
                duration: 3,
                closable: false,
              });
            } else {
              this.$Message.error({
                content: ret.message,
                duration: 3,
                closable: false,
              });
            }
          });
      } else {
        try {
          // var msg = {
          //   FunName: 'HCOMGetKeySN',
          // }
          // this.socket.send(JSON.stringify(msg));
          // this.watchSNTimer();//防止sn读取失败，页面无响应
          this.ukeyStateLoading = true;
          var msg = {
            FunName: "IsPresent",
          };
          this.socket.send(JSON.stringify(msg));
          let self = this;
          // setTimeout(function() {
          //     var msg = {
          //       FunName: 'GetKeySerialNumber',
          //     }
          //     self.socket.send(JSON.stringify(msg));//获取ukey序列码
          // }, 2);
          setTimeout(function () {
            self.awaitUkeyNo(itemIndex);
          }, 5);
        } catch (err) {
          console.log("ukey读取失败: " + e);
        }
      }
    },
    awaitUkeyNo(itemIndex) {
      let self = this;
      var asyncFunc = function (i) {
        return new Promise(function (resolve, reject) {
          setTimeout(function () {
            // console.log("index is : ", i);
            resolve(); //异步操作完成后执行resolve方法，配合.then使用，本例子中未使用到。
          }, 50);
        });
      };
      var awaitSubmit = async function () {
        for (var i = 0; i < 1000; i++) {
          if (self.ukeyNo && self.ukeyNo.length > 0) {
            // console.log("break index is : ", i);
            console.log("监听获取到ukey序列号了，" + new Date());
            break;
          }
          await asyncFunc(i); //async函数执行时，如果遇到await就会先暂停执行，等待Promise对象异步操作完成后，恢复async函数的执行并返回解析值。
        }
        if (self.ukeyNo && self.ukeyNo.length > 0) {
          if (self.ukeyNo == "no insert key") {
            self.$set(self.userList[itemIndex], "ukeyOperateLoading", false);
            self.$Message.error({
              content: "请插入ukey！",
              duration: 3,
              closable: false,
            });

            return;
          }
          self.dealSN(self.ukeyNo);
        } else {
          self.$set(self.userList[itemIndex], "ukeyOperateLoading", false);
          self.$Message.error({
            content: "未读取到ukey信息，请重插！",
            duration: 3,
            closable: false,
          });
        }
      };
      awaitSubmit();
    },
    operateUser(item, status) {
      this.$set(item, "ukeyOperateLoading", true);
      this.$http
        .post(
          gl.serverURL + "/sys/user/operate",
          { userId: item.userId, status: status },
          { emulateJSON: true }
        )
        .then((resp) => {
          this.$set(item, "ukeyOperateLoading", false);
          let ret = resp.body;
          if (ret.status == 0) {
            this.$set(item, "status", status);
            this.$Message.success({
              content: "成功",
              duration: 3,
              closable: false,
            });
          } else {
            this.$Message.error({
              content: ret.message,
              duration: 3,
              closable: false,
            });
          }
        })
        .catch((error) => {
          this.$set(item, "ukeyOperateLoading", false);
          this.$Message.error({
            content: error,
            duration: 3,
            closable: false,
          });
        });
    },
    delUser(data) {
      // return
      let params = {
        depCode: this.selectDepCode,
      };
      if (data) {
        params.userIds = data.userId;
      } else {
        if (this.checkAllGroup.length == 0) {
          this.$Message.error({
            content: "至少勾选一条数据",
            duration: 3,
            closable: false,
          });
          return;
        } else {
          let str = this.checkAllGroup.toString();
          params.userIds = str;
        }
      }
      let getURL = gl.serverURL + "/sys/user/delete";
      this.$http.delete(getURL, { params }).then((res) => {
        if (res.body.status === 0) {
          this.$Message.success({
            content: res.body.message,
            duration: 3,
            closable: false,
          });
          this.getDepUsers();
          this.checkAllGroup = [];
          this.checkAll = false;
          this.indeterminate = false;
          this.getDepList();
        } else {
          this.$Message.error({
            content: res.body.message,
            duration: 3,
            closable: false,
          });
        }
      });
    },
    getRoleList() {
      let getURL = gl.serverURL + "/sys/power/roleList";
      this.$http.get(getURL).then((resp) => {
        let ret = resp.body;
        if (ret.status === 0) {
          this.roleList = ret.data;
        } else {
          this.$Message.error({
            content: ret.message,
            duration: 3,
            closable: false,
          });
        }
      });
    },
    /* 打开重置密码对话框 */
    openResetModal(item) {
      this.resetModal = true;
      this.activeUser = item;
    },
    /* 打开变更角色对话框 */
    prepareChangeRole(item) {
      this.file = null;
      this.activeUser = item;
      this.userData = {};
      if (this.valiStrNotNull(this.activeUser.roleName)) {
        for (var curRoleObj in this.roleList) {
          if (this.roleList[curRoleObj].roleName == this.activeUser.roleName) {
            this.changedRoleId = this.roleList[curRoleObj].roleId;
          }
        }
      } else {
        this.changedRoleId = this.roleList[0].roleId;
      }
      this.$http
        .get(gl.serverURL + "/sys/user/getUser", {
          params: {
            userId: item.userId,
          },
        })
        .then((res) => {
          this.userData = res.body.data;
          this.userData.systemCodes = res.body.data.systemCodes.split(",");
          this.openChangedRole = true;
        });
    },
    /* 确认重置密码 */
    resetPassword() {
      let patchParams = {
        userId: this.activeUser.userId,
        password: this.activeUser.defaultPassword,
        userAccount: this.activeUser.userAccount,
      };
      let patchURL = gl.serverURL + "/sys/user/updatePassword";
      this.$http.patch(patchURL, patchParams, { emulateJSON: true }).then(
        (resp) => {
          let ret = resp.body;
          let status = ret.status;
          this.resetModal = false;
          if (status === 0) {
            this.$Message.success({
              content: "密码已重置！",
              duration: 3,
              closable: false,
            });
          }
        },
        (resp) => {
          this.resetModal = false;
          this.$Message.error({
            content: "重置密码出现异常！",
            duration: 3,
            closable: false,
          });
        }
      );
    },
    /*点击部门变更确认按钮*/
    editConfirm() {
      if (!this.selectedDepName) {
        this.$Message.error({
          content: "请选择要变更的部门！",
          duration: 3,
          closable: false,
        });
        return;
      }
      this.confirmModal = true;
    },
    /*点击机构目录后的+号*/
    addNewOrgan() {
      this.isAddOrgan = true;
      this.selectDepCode = "";
      this.newOrganName = "";
      this.organType = "";
      this.organTelephone = "";
      this.addTitle = "新建机构";
      this.selectPId = 0;
      // this.getProvinceList();
      this.addOrganModal = true;
    },
    /* 获取省份列表 */
    // getProvinceList() {
    //   let getURL = gl.serverURL + "/organ/province";
    //   this.$http.get(getURL).then((resp) => {
    //     let ret = resp.body;
    //     let status = ret.status;
    //     if (status === 0) {
    //       this.provinceList = ret.data;
    //       if (this.provinceList.length > 0) {
    //         this.provinceId = this.provinceList[0].id;
    //       } else {
    //         this.provinceId = 0;
    //       }

    //       this.addOrganModal = true;
    //     }
    //   });
    // },
    /*右键菜单单击事件*/
    handleOrgan(obj) {
      let method = obj.event.funName;

      switch (method) {
        case "addOrgan":
          this.isAddOrgan = false;
          this.addOrganModal = true;
          this.newOrganName = "";
          this.addTitle = obj.event.title;
          this.organType = obj.node.organType;
          break;
        case "editOrgan":
          this.editDepModal = true;
          this.editTitle = obj.event.title;
          this.newDepName = this.$options.filters.getFinalName(
            obj.node.organName
          );
          this.newOrganType = obj.node.organType;
          this.newOrganTelephone = obj.node.organTelephone;
          break;
        case "delOrgan":
          this.deleteModal = true;
          this.deleteTitle = obj.event.title;
          break;
      }
      this.activeOrgan = obj.node;
      this.selectPId = obj.node.parentId;
      this.selectNodeId = obj.node.organId;
      this.selectDepTitle = obj.node.title;
      this.selectDepCode = obj.node.depCode;
      this.selectDepName = obj.node.organName;
    },
    /* 新建、修改、删除机构后重新加载机构列表 */
    reloadOrganList() {
      if (this.hasSuperPerm && !this.isSingle) {
        this.getOrganList();
      }
    },
    /*修改机构名称时验证修改后的机构名*/
    validateEditName() {
      let re = /[-]/;
      let str = this.newDepName.trim();
      if (re.test(str)) {
        this.$Message.error({
          content:
            (this.selectPId === 0 ? "机构" : "部门") + "名称不能含有 - 符号！",
          duration: 3,
          closable: false,
        });
        this.editDepLoading = false;
        this.$nextTick(() => {
          this.editDepLoading = true;
        });
        return;
      }

      if (!str) {
        setTimeout(() => {
          this.editDepLoading = false;
          this.$nextTick(() => {
            this.editDepLoading = true;
          });
          this.newDepName = "";
          this.$Message.error({
            content:
              (this.selectPId === 0 ? "机构" : "部门") + "名称不能为空！",
            duration: 3,
            closable: false,
          });
        }, 200);
        return;
      }

      if (!this.newOrganType && this.selectPId === 0) {
        setTimeout(() => {
          this.editDepLoading = false;
          this.$nextTick(() => {
            this.editDepLoading = true;
          });
          this.$Message.error({
            content: "机构类别不能为空",
            duration: 3,
            closable: false,
          });
        }, 200);
        return;
      }

      if (str.length >= 30) {
        setTimeout(() => {
          this.editDepLoading = false;
          this.$nextTick(() => {
            this.editDepLoading = true;
          });
          this.$Message.error({
            content: "名称不要超过30个字符！",
            duration: 3,
            closable: false,
          });
        }, 200);
        return;
      }

      this.editDepName();
    },
    /*修改机构名称*/
    editDepName() {
      let patchParams = {
        organId: this.selectNodeId,
        organName: this.newDepName,
        depCode: this.selectDepCode,
        organTelephone: this.newOrganTelephone,
        organType: this.newOrganType,
      };
      let patchURL = gl.serverURL + "/sys/organ/updateName";
      this.$http
        .post(patchURL, patchParams, { emulateJSON: true })
        .then((resp) => {
          let ret = resp.body;
          let status = ret.status;
          this.editDepLoading = false;
          if (status == 0) {
            this.$Message.success({
              content: "修改成功！",
              duration: 3,
              closable: false,
            });
            this.handleUserOrganInfo(
              this.selectDepName,
              this.selectDepCode,
              this.newDepName
            );
            this.handleOrganNameLocal(
              this.depList,
              this.selectNodeId,
              this.newDepName
            );
            this.editDepModal = false;
            this.reloadOrganList();
            this.getDepList();
          } else {
            this.$Message.error({
              content: ret.message,
              duration: 3,
              closable: false,
            });
          }
        });
    },
    /* 递归处理免刷新更新部门列表信息 */
    handleOrganNameLocal(depArr, selectOrganId, depName) {
      let depTempList = depArr;
      for (let i = 0; i < depTempList.length; i++) {
        let organ = depTempList[i];
        if (organ.organId === selectOrganId) {
          organ.title =
            organ.depLevel == 1
              ? depName + "（" + organ.userNum + "）"
              : depName;
          organ.organName = depName;
          this.depList = depTempList;
          return;
        }
        if (organ.children) {
          this.handleOrganNameLocal(organ.children, selectOrganId, depName);
        }
      }
      this.depList = depTempList;
    },
    /* 免刷新更新用户列表信息 */
    handleUserOrganInfo(selectName, depCode, depName) {
      let nameArr = [];
      if (selectName.indexOf("-") != -1) {
        nameArr = selectName.split("-");
        nameArr[nameArr.length - 1] = depName;
      } else {
        nameArr.push(depName);
      }

      for (let i = 0; i < this.userList.length; i++) {
        if (this.userList[i].depCode === depCode) {
          this.userList[i].organName = nameArr.join("-");
        }
      }
    },
    /*删除机构*/
    deleteDep() {
      let delParams = {
        parentId: this.selectPId,
        depCode: this.selectDepCode,
      };
      let delURL = gl.serverURL + "/sys/organ/delete";
      this.$http.delete(delURL, { params: delParams }).then((resp) => {
        let ret = resp.body;
        let status = ret.status;
        this.deleteModal = false;
        if (status == 0) {
          let showName = "";
          if (
            this.selectDepCode.indexOf(this.organId) != -1 &&
            this.selectDepName.indexOf("-") != -1
          ) {
            let arr = this.selectDepName.split("-");
            for (let i = 0; i < arr.length; i++) {
              if (i == 0) {
                showName = arr[i];
              } else {
                if (i != arr.length - 1) {
                  showName = showName + "-" + arr[i];
                }
              }
            }
            this.selectDepName = showName;
          }
          this.$Message.success({
            content: "删除成功！",
            duration: 3,
            closable: false,
          });
          this.removeOrganWithoutRefresh(this.depList, this.selectNodeId);
          this.reloadOrganList();
        } else {
          this.$Message.error({
            content: ret.message,
            duration: 3,
            closable: false,
          });
        }
      });
    },
    /* 删除部门时免刷新更新机构列表 */
    removeOrganWithoutRefresh(orgList, organId) {
      let orgTempList = orgList;
      for (let i = 0; i < orgTempList.length; i++) {
        if (orgTempList[i].organId === organId) {
          orgTempList.splice(i, 1);
          this.depList = orgTempList;
          return;
        }

        if (orgTempList[i].children) {
          this.removeOrganWithoutRefresh(orgTempList[i].children, organId);
        }
      }
      this.depList = orgTempList;
    },
    /*查询左侧机构树*/
    getDepList() {
      this.depList = [];
      this.depLoading = true;
      let getURL = this.hasSuperPerm
        ? gl.serverURL + "/sys/list/allDep"
        : gl.serverURL + "/organ/list/curDep";
      this.$http
        .get(getURL, {
          params: { organId: this.hasSuperPerm ? 0 : this.organId },
        })
        .then(
          (resp) => {
            let ret = resp.body;
            let status = ret.status;
            this.depLoading = false;
            if (status == 0) {
              this.isShowOrgan = true;
              let list = [
                {
                  disabled: true,
                  title: "区县网信办",
                  organName: "区县网信办",
                  expand: 2,
                  events: [],
                  children: ret.data[2],
                },
                {
                  disabled: true,
                  title: "市直单位",
                  organName: "市直单位",
                  expand: 3,
                  events: [],
                  children: ret.data[3],
                },
                {
                  disabled: true,
                  title: "服务公司",
                  organName: "服务公司",
                  expand: 4,
                  events: [],
                  children: ret.data[4],
                },
              ];
              list.unshift(ret.data[1][0]);
              this.depList = list;
              if (!this.hasSuperPerm) {
                this.depList[0].events[2] = {};
              }
              this.isDepListNull = this.depList.length > 0 ? false : true;
              this.selectDepName = this.selectDepName
                ? this.selectDepName
                : this.depList[0].organName;
            } else if (status == 120) {
              this.isShowOrgan = false; //没有权限
            }
          },
          (resp) => {
            this.depLoading = false;
            this.$Message.error({
              content: "获取机构目录异常！",
              duration: 3,
              closable: false,
            });
          }
        );
      //this.addRecord();
    },

    //查询机构配置时添加浏览日志信息
    // addRecord() {
    //   let url = gl.menu.secondActive.split("?")[0];
    //   let name = urlandnameJSON[url];
    //   console.log(name,url)
    // 	let addParam = {
    // 		menuModule: name,
    // 		operateModule: 200,
    //     // groupId: -1,
    //     names: name,
    //     operationContent: "浏览",
    //   };
    // 	let demoUrl = gl.serverURL + "/common/addRecord";
    // 	let $this = this;
    // 	this.$http.get(demoUrl, { params: addParam })
    // 		.then(response => {}, response => {});
    // },

    /*添加新机构*/
    addOrgan() {
      let re = /[-]/;
      let str = this.newOrganName.trim();
      let msg =
        this.selectPId === 0 && this.addTitle == "新建机构" ? "机构" : "部门";
      if (re.test(str)) {
        this.$Message.error({
          content: msg + "名称不能含有 - 符号！",
          duration: 3,
          closable: false,
        });
        this.addOrganLoading = false;
        this.addOrganModal = true;
        this.$nextTick(() => {
          this.addOrganLoading = true;
        });
        return;
      }
      if (!str) {
        // setTimeout(() => {
        this.addOrganLoading = false;
        this.newOrganName = "";
        this.$Message.error({
          content: msg + "名称不能为空！",
          duration: 3,
          closable: false,
        });
        // }, 200);
        this.addOrganModal = true;
        return false;
      }
      if (!this.organType && msg == "机构") {
        // setTimeout(() => {
        this.addOrganLoading = false;
        this.$Message.error({
          content: "机构类别不能为空！",
          duration: 3,
          closable: false,
        });
        // }, 200);
        this.addOrganModal = true;
        return false;
      }
      if (str.length >= 30) {
        setTimeout(() => {
          this.addOrganLoading = false;
          this.$nextTick(() => {
            this.addOrganLoading = true;
          });
          this.$Message.error({
            content: "名称不要超过30个字符！",
            duration: 3,
            closable: false,
          });
        }, 200);
        return;
      }
      if (this.provinceId == "" && this.isAddOrgan) {
        setTimeout(() => {
          this.addOrganLoading = false;
          this.$nextTick(() => {
            this.addOrganLoading = true;
          });
          this.$Message.error({
            content: "对应省份不能为空！",
            duration: 3,
            closable: false,
          });
        }, 200);
        return;
      }
      this.confirmAddOrgan();
    },
    /*确认添加机构*/
    confirmAddOrgan() {
      let postParams = {
        provinceId: this.isAddOrgan
          ? this.provinceId
          : this.activeOrgan.provinceId,
        depCode: this.selectDepCode,
        organName: this.newOrganName,
        organTelephone: this.organTelephone,
        organType: this.organType,
      };
      let postURL = this.isAddOrgan
        ? gl.serverURL + "/sys/organ/insert"
        : gl.serverURL + "/sys/organ/insert";
      this.$http.post(postURL, postParams, { emulateJSON: true }).then(
        (resp) => {
          let ret = resp.body;
          let status = ret.status;
          this.addOrganLoading = false;
          if (status == 0) {
            this.$Message.success({
              content: "新建成功",
              duration: 3,
              closable: false,
            });
            this.addOrganModal = false;
            this.reloadOrganList();
            this.getDepList();
          } else {
          }
        },
        (resp) => {
          this.$Message.error({
            content: "系统服务异常！",
            duration: 3,
            closable: false,
          });
        }
      );
    },
    /*点击机构目录事件*/
    showOrganList() {
      this.pageNo = 1;
      this.isSingle = false;
      this.getOrganList();
    },
    /*获取右侧机构列表*/
    getOrganList() {
      this.organList = [];
      let getParams = {
        pageSize: this.pageSize, //每页数据量
        extIndex: this.pageNo, //当前页页码
      };
      this.organLoading = true;
      let queryURL = gl.serverURL + "/sys/list/organ";
      this.$http.get(queryURL, { params: getParams }).then(
        (resp) => {
          let ret = resp.body;
          let status = ret.status;
          this.organLoading = false;
          if (status == 0) {
            this.isShowOrganList = true;
            if (ret.data) {
              this.userCount = ret.data.userCount;
              this.organCount = ret.data.organCount;
              this.organList = ret.data.organList;
            }
            this.isOrganListNull = this.organList.length > 0 ? false : true;
          } else if (status == 120) {
            this.isShowOrganList = false; //没有权限
          }
        },
        (resp) => {
          this.organLoading = false;
          this.$Message.error({
            content: "服务请求异常！",
            duration: 3,
            closable: false,
          });
        }
      );
    },
    valiStrNotNull(str) {
      return this.valiDomNotNull(str) && str != null && str !== "";
    },
    valiDomNotNull(dom) {
      return typeof dom !== "undefined" && dom;
    },
    /*点击新建账号按钮*/
    clickAddBtn() {
      this.file = null;
      this.checkAll = false;
      if (
        this.valiDomNotNull(this.roleList) &&
        this.valiDomNotNull(this.roleList[0]) &&
        this.valiStrNotNull(this.roleList[0].roleId)
      ) {
        this.defaultRole = this.roleList[0].roleId;
      }
      this.checkAllGroup = [];
      this.indeterminate = false;

      if (!this.activeOrgan) {
        this.$Message.error({
          content: "请至少选中一个部门再进行操作！",
          duration: 3,
          closable: false,
        });
        return;
      }
      this.addAccountModal = true;
      this.newUserName = "";
      this.newUserTelePhone = "";
      this.newUserAccount = "";
      this.pwdAutoCreated = "";
      this.sdtId = "";
      this.isDistrict = "";
      this.defaultRole = "";
      this.systemCoding = [];
    },
    /* 生成6位初始密码 */
    generatePassword() {
      if (!this.pwdAutoCreated) {
        this.pwdAutoCreated = Math.random().toString().slice(-6);
      }
    },
    uploadFile(file) {
      this.file = file;
    },
    removeFile() {
      this.file = null;
    },
    /*验证用户名是否为空*/
    valiDataAndCommit() {
      if (!this.newUserAccount.trim()) {
        setTimeout(() => {
          this.addLoading = false;
          this.$nextTick(() => {
            this.addLoading = true;
          });
          this.newUserAccount = "";
          this.$Message.error({
            content: "用户名不能为空！",
            duration: 3,
            closable: false,
          });
        }, 200);
        return;
      }
      let reg = /[\u4E00-\u9FA5\uF900-\uFA2D]/;
      if (reg.test(this.newUserAccount)) {
        setTimeout(() => {
          this.addLoading = false;
          this.$nextTick(() => {
            this.addLoading = true;
          });
          this.$Message.error({
            content: "用户名不能包含中文字符！",
            duration: 3,
            closable: false,
          });
        }, 200);
        return;
      }
      /* 验证用户名是否为空 */
      if (!this.newUserName.trim()) {
        setTimeout(() => {
          this.addLoading = false;
          this.$nextTick(() => {
            this.addLoading = true;
          });
          this.newUserName = "";
          this.$Message.error({
            content: "用户名不能为空！",
            duration: 3,
            closable: false,
          });
        }, 200);
        return;
      } else {
        // 用户名长度检测
        if (this.newUserName.length > 15) {
          setTimeout(() => {
            this.addLoading = false;
            this.$nextTick(() => {
              this.addLoading = true;
            });
            this.$Message.error({
              content: "用户名最多包容15个字符！",
              duration: 3,
              closable: false,
            });
          }, 200);
          return;
        }
      }
      if (this.isDistrict != 1 && this.isDistrict != 0) {
        setTimeout(() => {
          this.addLoading = false;
          this.$nextTick(() => {
            this.addLoading = true;
          });
          this.$Message.error({
            content: "请选择用户类型！",
            duration: 3,
            closable: false,
          });
        }, 200);
        return;
      }
      /* 验证用户名联系方式是否符合规范 */
      // if (!this.newUserTelePhone || this.newUserTelePhone.trim() == ''  || !(/^1(3|4|5|7|8)\d{9}$/.test(this.newUserTelePhone.trim()))) {
      //     this.$Message.error({
      //         content: "用户联系方式有误，请确认！",
      //     closable: false,
      //});
      //return;
      //}
      /* 初始密码长度监测 */
      if (!this.pwdAutoCreated.trim()) {
        setTimeout(() => {
          this.addLoading = false;
          this.$nextTick(() => {
            this.addLoading = true;
          });
          this.pwdAutoCreated = "";
          this.$Message.error({
            content: "初始密码不能为空！",
            duration: 3,
            closable: false,
          });
        }, 200);
        return;
      } else {
        // 初始密码长度只能为6位
        let reg = new RegExp("^[0-9]{6}$");
        if (!reg.test(this.pwdAutoCreated)) {
          setTimeout(() => {
            this.addLoading = false;
            this.$nextTick(() => {
              this.addLoading = true;
            });
            this.$Message.error({
              content: "初始密码为6位数字！",
              duration: 3,
              closable: false,
            });
          }, 200);
          return;
        }
      }
      if (!this.valiStrNotNull(this.defaultRole)) {
        setTimeout(() => {
          this.addLoading = false;
          this.$nextTick(() => {
            this.addLoading = true;
          });
          this.$Message.error({
            content: "请选择用户角色！",
            duration: 3,
            closable: false,
          });
        }, 200);
        return;
      }
      this.addAccount();
    },
    /*添加新账号按钮点击事件*/
    addAccount() {
      let params = new FormData();
      params.append("file", this.file);
      params.append("userName", this.newUserName);
      params.append("userTelephone", this.newUserTelePhone);
      params.append("userAccount", this.newUserAccount);
      params.append("depCode", this.activeOrgan.depCode);
      params.append("defaultPassword", this.pwdAutoCreated);
      params.append("roleId", this.defaultRole);
      params.append("systemCodes", this.systemCoding.toString());
      params.append("serialNo", this.Encrypt(this.newUkey));
      params.append("sdtId", this.sdtId);
      params.append("isDistrict", this.isDistrict);
      let postURL = gl.serverURL + "/sys/user/insertOrUpdate";
      this.$http.post(postURL, params).then((resp) => {
        let ret = resp.body;
        let status = ret.status;
        this.addLoading = false;
        if (status == 0) {
          this.newUserName = "";
          this.newUserAccount = "";
          this.pwdAutoCreated = "";
          this.defaultRole = "";
          this.systemCoding = [];
          this.$Message.success({
            content: ret.message,
            duration: 3,
            closable: true,
          });
          this.getDepUsers(); //刷新当前部门用户列表
          let depLen = this.depList.length;
          // debugger;
          for (let i = 0; i < depLen; i++) {
            let organ = this.depList[i];
            if (organ.organId == this.activeOrgan.depCode.split("#")[1]) {
              this.depList[i].userNum += 1;
              this.depList[i].title =
                this.depList[i].organName +
                "（" +
                this.depList[i].userNum +
                "）";
              break;
            }
          }
          this.addAccountModal = false;
        } else {
          this.$nextTick(() => {
            this.addLoading = true;
          });
          this.$Message.error({
            content: ret.message,
            duration: 3,
            closable: false,
          });
        }
      });
    },
    //角色改变事件
    changeRoleMed() {
      /* 验证用户名联系方式是否符合规范 */
      //if (!this.userData.userTelephone || this.userData.userTelephone.trim() == '' || !(/^1(3|4|5|7|8)\d{9}$/.test(this.userData.userTelephone.trim()))) {
      //    this.$Message.error({
      //        content: "用户联系方式有误，请确认！",
      //      duration: 3,
      //    closable: false,
      //});
      //return;
      //}
      if (this.valiStrNotNull(this.changedRoleId)) {
        let params = new FormData();
        params.append("file", this.file);
        params.append("userId", this.activeUser.userId);
        params.append("roleId", this.changedRoleId);
        params.append("userName", this.userData.userName);
        params.append("systemCodes", this.userData.systemCodes.toString());
        params.append("userTelephone", this.userData.userTelephone);
        params.append("serialNo", this.Encrypt(this.userData.serialNo));
        params.append("sdtId", this.userData.sdtId);
        params.append("isDistrict", this.userData.isDistrict);
        let patchURL = gl.serverURL + "/sys/user/insertOrUpdate";
        this.$http.post(patchURL, params).then((resp) => {
          let ret = resp.body;
          let status = ret.status;
          this.openChangedRole = false;
          if (status == 0) {
            this.$Message.success({
              content: "编辑角色成功！",
              duration: 3,
              closable: false,
            });
          } else {
            this.$Message.error({
              content: ret.message,
              duration: 3,
              closable: false,
            });
          }
          this.getDepUsers(); //刷新当前部门用户列表
        });
      }
    },
    /*部门变更按钮点击事件*/
    openModal() {
      this.selectedDepName = "";
      if (!this.activeOrgan) {
        this.$Message.error({
          content: "请先选中一个机构或者部门！",
          duration: 3,
          closable: false,
        });
        return;
      }
      if (this.checkAllGroup.length == 0) {
        this.$Message.error({
          content: "请至少选择一个人员！",
          duration: 3,
          closable: false,
        });
        return;
      }
      // 验证所勾选的人员是否含有其他机构的人
      let organIdArray = [];
      for (let i = 0; i < this.checkAllGroup.length; i++) {
        let id = this.checkAllGroup[i];
        for (let j = 0; j < this.userList.length; j++) {
          let user = this.userList[j];
          if (id === user.userId) {
            organIdArray.push(user.organId);
          }
        }
      }
      let newArray = new Set(organIdArray); // 数组元素去重
      if (newArray.size > 1) {
        this.$Message.error({
          content: "请勾选同机构的人员进行部门变更操作！",
          duration: 3,
          closable: false,
        });
        return;
      }
      //定义当前选中的机构id
      this.selectedUserName = "";
      for (let id of newArray) {
        this.selectedOrganId = id;
      }
      //将选中的用户的用户名拼起来
      this.selectUsers.forEach((user, index) => {
        this.selectedUserName +=
          index < this.selectUsers.length - 1
            ? user.userName + "、"
            : user.userName;
      });
      this.getModalDepList();
      this.editModal = true;
    },
    /*获取部门变更对话框中的机构目录树*/
    getModalDepList() {
      this.depModalList = [];
      this.depModalLoading = true;
      let getURL = gl.serverURL + "/sys/user/list/dep";
      this.$http
        .get(getURL, { params: { organId: this.selectedOrganId } })
        .then((resp) => {
          let ret = resp.body;
          let status = ret.status;
          this.depModalLoading = false;
          if (status == 0) {
            this.depModalList = ret.data;
          }
        });
    },
    /*部门变更选择部门*/
    choseDep(node) {
      if (node.length > 0) {
        this.editedDepCode = node[0].depCode;
        this.selectedDepName = node[0].title;
      } else {
        this.selectedDepName = "";
      }
    },
    /*确认部门变更*/
    changeDep() {
      this.editLoading = true;
      let patchParams = {
        depCodeAfter: this.editedDepCode,
        userIds: this.checkAllGroup.join(","),
      };
      let patchURL = gl.serverURL + "/sys/user/update";
      this.$http.patch(patchURL, patchParams, { emulateJSON: true }).then(
        (resp) => {
          let ret = resp.body;
          let status = ret.status;
          this.editLoading = false;
          if (status == 0) {
            this.indeterminate = false;
            this.checkAll = false;
            this.getDepUsers();
            this.editModal = false;
            this.selectDepTitle = "";
            this.$Message.success({
              content: ret.message,
              duration: 3,
              closable: true,
            });
            this.selectedDepName = "";
          } else {
            this.$Message.error({
              content: ret.message,
              duration: 3,
              closable: false,
            });
          }
        },
        (resp) => {
          this.editLoading = false;
          this.$Message.error({
            content: "服务请求异常！",
            duration: 3,
            closable: false,
          });
        }
      );
    },
    //批量删除
    delUserOne() {
      let params = {
        depCode: this.selectDepCode,
      };
      if (this.checkAllGroup.length == 0) {
        this.$Message.error({
          content: "至少勾选一条数据",
          duration: 3,
          closable: false,
        });
        return;
      } else {
        let str = this.checkAllGroup.toString();
        params.userIds = str;
      }

      this.$Modal.confirm({
        title: "确认删除信息",
        content: "是否确定要删除您选中的用户，请谨慎操作！",
        onOk: () => {
          let getURL = gl.serverURL + "/sys/user/delete";
          this.$http.delete(getURL, { params }).then((res) => {
            if (res.body.status === 0) {
              this.$Message.success({
                content: res.body.message,
                duration: 3,
                closable: false,
              });
              this.getDepUsers();
              this.checkAllGroup = [];
              this.checkAll = false;
              this.indeterminate = false;
              this.getDepList();
            } else {
              this.$Message.error({
                content: res.body.message,
                duration: 3,
                closable: false,
              });
            }
          });
        },
        onCancel: () => {},
      });
    },

    /*搜索部门用户*/
    searchDepUsers() {
      // if (!this.searchWord.trim()) {
      //   this.$Message.error({
      //     content: "搜索内容不能为空！",
      //     duration: 3,
      //     closable: false,
      //   });
      //   return;
      // }
      this.pageNo = 1;
      this.checkAll = false;
      this.checkAllGroup = [];
      this.indeterminate = false;
      //按照机构id查询
      // this.organId = this.hasSuperPerm ? 0 : this.organId;
      this.organId = this.organId;
      // this.selectDepCode = this.hasSuperPerm ? null : this.selectDepCode;
      this.selectDepCode = this.selectDepCode;
      this.getDepUsers();
    },
    /*点击某一节点*/
    selectMenu(node) {
      this.isSingle = true;
      this.selectNode = node;
      console.log(node, "node");
      if (node.length > 0) {
        this.activeOrgan = node[0];
        this.selectDepCode = node[0].depCode;
        this.selectDepName = node[0].organName;
        this.selectDepTitle = node[0].organName;
        this.organId = node[0].depCode.split("#")[1];
        this.pageNo = node.length > 0 ? 1 : this.pageNo;
        this.searchWord = node.length > 0 ? "" : this.searchWord;
        this.getDepUsers();
      } else {
        this.activeOrgan = null;
      }
      this.checkAll = false;
      this.selectUsers = [];
      this.checkAllGroup = [];
      this.indeterminate = false;
      this.selectedUserName = "";
    },
    /* 获取右侧部门下的用户列表 */
    getDepUsers() {
      this.userList = [];
      let getParams = {
        organId: this.organId, //机构id
        pageSize: this.pageSize, //每页数据量
        pageNo: this.pageNo,
        extContent: this.selectDepCode,
        searchWord: this.searchWord, //搜索关键词
        //        startRow: (this.pageNo - 1) * this.pageSize, //起始偏移位置
      };
      this.userLoading = true;
      let queryURL = gl.serverURL + "/sys/user/list";
      this.$http.get(queryURL, { params: getParams }).then(
        (resp) => {
          let ret = resp.body;
          let status = ret.status;
          this.userLoading = false;
          if (status == 0) {
            if (ret.data) {
              this.count = ret.data.count;
              this.userList = ret.data.list;
              this.checkAllGroup = [];
              this.selectUsers = [];
            }
            this.isUserListNull = this.userList.length > 0 ? false : true;
          }
        },
        (resp) => {
          this.userLoading = false;
          this.$Message.error({
            content: "服务请求异常！",
            duration: 3,
            closable: false,
          });
        }
      );
    },
    /*获取门户同步的用户所述部门信息*/
    getUserInfo(index, userId, userInfo) {
      return;
      this.activedRow = index;
      if (!userInfo) {
        let getURL = gl.serverURL + "/user/depInfo";
        this.$http.get(getURL, { params: { userId: userId } }).then(
          (resp) => {
            let ret = resp.body;
            let status = ret.status;
            if (status == 0) {
              let info = ret.data;
              this.userInfo =
                info.indexOf("-") === -1 ? info + "-暂无部门信息" : info;
              let len = this.userList.length;
              for (let i = 0; i < len; i++) {
                let user = this.userList[i];
                if (userId == user.userId) {
                  user.userInfo = info;
                  break;
                }
              }
              this.showTip = true;
            } else {
              this.$Message.error({
                content: "用户部门信息获取失败！",
                duration: 3,
                closable: false,
              });
            }
          },
          (resp) => {
            this.$Message.error({
              content: "获取用户部门信息时出现异常！",
              duration: 3,
              closable: false,
            });
          }
        );
      } else {
        this.showTip = true;
        this.userInfo = userInfo;
      }
    },
    /* 全选复选框处理 */
    handleCheckAll() {
      this.checkAll = this.indeterminate ? false : !this.checkAll;
      this.indeterminate = false;
      if (this.checkAll) {
        this.userList.forEach((item) => {
          this.selectUsers.push(item);
          this.checkAllGroup.push(item.userId);
        });
      } else {
        this.selectUsers = [];
        this.checkAllGroup = [];
      }
    },
    /* 列表中复选框处理 */
    checkAllGroupChange(data) {
      this.selectUsers = [];
      if (data.length === this.userList.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (data.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
      data.forEach((id) => {
        this.userList.forEach((user) => {
          if (id == user.userId) {
            this.selectUsers.push(user);
          }
        });
      });
    },
    /*机构列表翻页查询*/
    queryOrgPageData(param) {
      this.pageNo = param.pageno;
      this.userLoading = true;
      this.getOrganList();
    },
    /*用户列表翻页查询*/
    queryUserPageData(param) {
      this.pageNo = param.pageno;
      this.userLoading = true;
      this.indeterminate = false;
      this.checkAll = false;
      this.getDepUsers();
    },
    getIndex(pageno, pagesize, index) {
      index = PageUtils.getPageIndex(pageno, pagesize, index);
      return index;
    },
  },
  filters: {
    getDeleteName(name) {
      if (!name) {
        return name;
      }
      let depName = name.split("-");
      return depName[depName.length - 1];
    },
    formatYearDate(time) {
      var date = new Date(time);
      return formatDate(date, "yyyy-MM-dd");
    },
    getIndex(pageno, pagesize, index) {
      index = PageUtils.getPageIndex(pageno, pagesize, index);
      return index;
    },
    getFinalName(name) {
      if (name && name.indexOf("-") != -1) {
        let names = name.split("-");
        return names[names.length - 1];
      }
      return name;
    },
    filterFirstName(user) {
      let name = user.organName ? user.organName : "";
      return name.indexOf("-") === -1
        ? name
        : name.substring(name.indexOf("-") + 1, name.length);
    },
    filterOrganName(name) {
      if (name) {
        return name.indexOf("（") != -1
          ? name.substring(0, name.indexOf("（"))
          : name;
      }
    },
  },
  components: {
    Tree,
    PageBar,
    NodataPage,
  },
  beforeMount() {
    this.organId = sessionStorage.getItem("organId");
    this.hasSuperPerm = this.hasPermission("/config/organ"); //是否拥有超级权限-/super改为/config/organ
  },
  mounted() {
    gl.organ = this;
    if (!gl.develop && !this.hasPermission("/sys/organ")) {
      return;
    }
    this.initSocket(); //初始化ukey相关的websocket

    this.hasSuperPerm = true;
    // if (this.roleList.length == 0 && this.hasPermission("/sysmng"))
    this.getRoleList();
    if (this.hasSuperPerm) {
      this.isSingle = false;
      this.getDepList();
      this.getOrganList();
    } else {
      this.getDepList();
      this.getDepUsers();
    }
    this.getLog("机构配置/机构目录", '查看机构目录/查看机构目录');
  },
};
</script>

<style scoped lang="less">
.blue {
  color: #3172eb;
}
.red {
  color: red;
}
.w70 {
  width: 70px;
  display: inline-flex;
  justify-content: flex-end;
}
.input-item {
  margin-top: 10px;

  .inline {
    display: inline-flex;
    align-items: center;
    justify-content: flex-start;
  }
}
.flex {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;

  .left {
    width: 270px;
    max-height: 640px;
    display: flex;
    margin-top: 10px;
    flex-direction: column;

    .tree {
      width: 270px;
      min-height: 250px;
      max-height: 600px;
      display: flex;
      padding: 5px 10px;
      justify-content: flex-start;
      position: relative;

      /deep/.ivu-tree-title {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .head {
      height: 40px;
      display: flex;
      padding: 0px 10px;
      align-items: center;
      background: #dbe0e6;
      justify-content: flex-end;

      .text {
        margin-right: 31%;

        &.super {
          cursor: pointer;
        }
      }

      .plus {
        width: 13px;
        cursor: pointer;
      }
    }
  }

  .right {
    width: 78%;
    padding: 0px 20px;

    .flex-between {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .hl {
        .bold;
        width: 36%;
        display: inline-flex;
        align-items: center;

        .tn-width {
          max-width: 80%;
          .ellipsis;
        }
      }
      .hr {
        width: 700px;
        display: inline-flex;
        align-items: center;
        justify-content: flex-end;

        /deep/.ivu-btn {
          color: #3172eb;
          background-color: #fff;
        }
        /deep/.ivu-btn > .ivu-icon {
          font-weight: bold;
        }
      }
    }

    .relative {
      position: relative;
    }

    .olist {
      margin-top: 10px;
      max-height: 700px;

      ul:nth-child(even) {
        background: rgba(232, 234, 236, 0.5);
      }

      .ul {
        height: 40px;
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: center;

        li {
          height: 40px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
        }

        > li:nth-child(1) {
          width: 100px;
        }
        > li:nth-child(2) {
          flex: 1;
          .ellipsis;
          padding: 0px 10px;
        }
        > li:nth-child(3) {
          width: 100px;
        }
        > li:nth-child(4) {
          width: 180px;
          border-right: 0px solid #ddd;
        }

        &.bgc {
          font-weight: bold;
          background: #dbe0e6;
        }

        &.page {
          height: 60px;
          background: white;
        }
      }
    }

    .ulist {
      margin-top: 10px;
      max-height: 700px;

      ul:nth-child(even) {
        background: rgba(232, 234, 236, 0.5);
      }

      .ul {
        height: 40px;
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: center;

        /deep/.ivu-checkbox-wrapper {
          margin-right: 0px;
        }

        li {
          height: 40px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #ddd;
          border-bottom: 1px solid #ddd;
        }

        > li:nth-child(1) {
          width: 50px;
        }
        > li:nth-child(2) {
          width: 80px;
        }
        > li:nth-child(3) {
          flex: 1;
        }
        > li:nth-child(4) {
          width: 120px;
        }
        > li:nth-child(5) {
          width: 120px;
        }
        > li:nth-child(6) {
          width: 165px;
        }
        > li:nth-child(7) {
          width: 340px;
          border-right: 0px solid #ddd;
        }

        &.bgc {
          font-weight: bold;
          background: #dbe0e6;
        }

        &.page {
          height: 60px;
          background: white;
        }
      }
    }
  }

  .user-info {
    width: 100%;
    padding: 0px 5px;
    text-align: center;
    position: relative;

    .tooltip {
      top: 20px;
      left: 50%;
      min-width: 360px;
      text-align: left;
      position: absolute;

      .info {
        color: #696969;
        font-size: 14px;
        background: #fff;
        min-width: 100px;
        padding: 0px 5px;
        border: 1px solid #696969;
      }
    }
  }

  .bold {
    margin: 0px 3px;
    font-size: 16px;
    font-weight: bold;
  }

  .m10 {
    margin: 0px 10px;
  }
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.pad10 {
  font-size: 14px;
  padding: 5px 10px;
}

.name {
  display: inline-block;
  vertical-align: text-top;
  max-width: 280px;
  color: #0000ff;
  .ellipsis;
}

/*部门变更对话框样式*/
.dep {
  display: flex;
  flex-direction: column;
  justify-content: center;

  .up {
    font-size: 14px;
  }

  .down {
    width: 100%;
    display: flex;
    position: relative;
    justify-content: center;
  }
}

.datanull {
  display: flex;
  align-items: center;
  justify-content: center;

  &.organ {
    height: 600px;
    background: #fff;
  }
}

.remove {
  cursor: pointer;
  &:hover {
    color: aqua;
  }
}

.acquire {
  position: absolute;
  right: 44px;
  cursor: pointer;
  color: #2294e2;
  top: 12px;
}
</style>
