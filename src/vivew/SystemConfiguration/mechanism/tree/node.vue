<template>
  <ul :class="classes" >
    <li>
      <span :class="arrowClasses" @click="handleExpand">
        <Icon v-if="showArrow" type="arrow-right-b"></Icon>
        <i v-else class="text-node"></i>
        <Icon v-if="showLoading" type="load-c" class="ivu-load-loop"></Icon>
      </span>
      <Checkbox
        v-if="showCheckbox"
        :value="data.checked"
        :indeterminate="data.indeterminate"
        :disabled="data.disabled || data.disableCheckbox"
        @click.native.prevent="handleCheck"
      >
      </Checkbox>
      <Render
        v-if="data.render"
        :render="data.render"
        :data="data"
        :node="node"
      ></Render>
      <Render
        v-else-if="isParentRender"
        :render="parentRender"
        :data="data"
        :node="node"
      ></Render>
      
      <Poptip
        :popperClass="'tree-menu-pop-box'"
        v-else-if="
          needRightClick && Array.isArray(data.events) && data.events.length > 0
        "
        transfer
        v-model="visibal"
        placement="right-start"
      >
        <span
          :class="titleClasses"
          @click="handleSelect"
          :title="data.organName"
          v-if="data.title.indexOf('（')  >=  0" >{{ data.organName }}</span>
        <span
          :class="titleClasses"
          @click="handleSelect"
          :title="data.title"
          v-else
          >{{ data.title }}</span>
        <span :class="titleClasses" v-if="data.parentId == 0" style="margin-left: 0px;padding: 0px;">（{{ data.userNum }}）</span>
        <div class="context-menu-box" slot="content">
          <div
            v-for="(e, index) in data.events"
            class="context-menu-item"
            :key="index"
          >
            <template v-if="Array.isArray(e.children) && e.children.length > 0">
              <Poptip trigger="hover" placement="right-start">
                <div class="menu-list-item has-child-menu">
                  <span>{{ e.title }}</span>
                </div>
                <div slot="content">
                  <ul class="second-menu-box">
                    <li
                      class="menu-list-item"
                      v-for="(c, index) in e.children"
                      :key="index"
                      @click="editChildMenu(data, c)"
                    >
                      <span>{{ c.title }}</span>
                    </li>
                  </ul>
                </div>
              </Poptip>
            </template>
            <div v-else class="menu-list-item" @click="editOrgan(data, e)">
              <i class="context-menu-icon" :class="e.iconName"></i>
              <span>{{ e.title }}</span>
            </div>
          </div>
        </div>
      </Poptip>
      <span
        v-else
        :title="data.title | removeUserNum"
        :class="titleClasses"
        @click="handleSelect"
        style="
            cursor: default;
            text-align: center;
            width: 270px;
            background-color: rgb(243, 244, 245);
            line-height: 30px;
            color: rgb(170, 170, 170);
            position: relative;
            max-width: 270px;
            margin-left: -22px;
        "
        >{{ data.title }}</span
      >
      <template v-if="data.expand">
        <Tree-node
          v-for="(item, i) in children"
          :key="i"
          :data="item"
          :multiple="multiple"
          :show-checkbox="showCheckbox"
          :needRightClick="needRightClick"
          :children-key="childrenKey"
        >
        </Tree-node>
      </template>
    </li>
  </ul>
</template>
<script>
import Checkbox from "iview/src/components/checkbox/checkbox.vue";
import Icon from "iview/src/components/icon/icon.vue";
import Render from "./render";
import Emitter from "iview/src/mixins/emitter";
import { findComponentUpward } from "iview/src/utils/assist";
import Poptip from "./poptip.vue";
const prefixCls = "ivu-tree";
export default {
  name: "TreeNode",
  mixins: [Emitter],
  components: { Checkbox, Icon, Render, Poptip },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    childrenKey: {
      type: String,
      default: "children",
    },
    showCheckbox: {
      type: Boolean,
      default: false,
    },
    needRightClick: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      prefixCls: prefixCls,
      visibal: false,
    };
  },
  computed: {
    classes() {
      return [`${prefixCls}-children`];
    },
    selectedCls() {
      return [
        {
          [`${prefixCls}-node-selected`]: this.data.selected,
        },
      ];
    },
    arrowClasses() {
      return [
        `${prefixCls}-arrow`,
        {
          [`${prefixCls}-arrow-disabled`]: this.data.disabled,
          [`${prefixCls}-arrow-open`]: this.data.expand,
        },
      ];
    },
    titleClasses() {
      return [
        `${prefixCls}-title`,
        {
          [`${prefixCls}-title-selected`]: this.data.selected,
        },
      ];
    },
    showArrow() {
      return (
        (this.data[this.childrenKey] && this.data[this.childrenKey].length) ||
        ("loading" in this.data && !this.data.loading)
      );
    },
    showLoading() {
      return "loading" in this.data && this.data.loading;
    },
    isParentRender() {
      const Tree = findComponentUpward(this, "Tree");
      return Tree && Tree.render;
    },
    parentRender() {
      const Tree = findComponentUpward(this, "Tree");
      if (Tree && Tree.render) {
        return Tree.render;
      } else {
        return null;
      }
    },
    node() {
      const Tree = findComponentUpward(this, "Tree");
      if (Tree) {
        // 将所有的 node（即flatState）和当前 node 都传递
        return [
          Tree.flatState,
          Tree.flatState.find((item) => item.nodeKey === this.data.nodeKey),
        ];
      } else {
        return [];
      }
    },
    children() {
      return this.data[this.childrenKey];
    },
  },
  methods: {
    handleExpand() {
      const item = this.data;
      if (item.disabled) return;

      // async loading
      if (item[this.childrenKey].length === 0) {
        const tree = findComponentUpward(this, "Tree");
        if (tree && tree.loadData) {
          this.$set(this.data, "loading", true);
          tree.loadData(item, (children) => {
            this.$set(this.data, "loading", false);
            if (children.length) {
              this.$set(this.data, this.childrenKey, children);
              this.$nextTick(() => this.handleExpand());
            }
          });
          return;
        }
      }

      if (item[this.childrenKey] && item[this.childrenKey].length) {
        this.$set(this.data, "expand", !this.data.expand);
        this.dispatch("Tree", "toggle-expand", this.data);
      }
    },
    handleSelect() {
      if (this.data.disabled) return;
      this.dispatch("Tree", "on-selected", this.data.nodeKey);
    },
    handleCheck() {
      if (this.data.disabled) return;
      const changes = {
        checked: !this.data.checked && !this.data.indeterminate,
        nodeKey: this.data.nodeKey,
      };
      this.dispatch("Tree", "on-check", changes);
    },
    //右键菜单点击事件，通过dispatch分发到Tree组件进行监听,返回当前节点数据和触发的事件名
    editOrgan(node, event) {
      this.visibal = false;
      this.dispatch("Tree", "handle-organ", { node, event });
    },
    //点击右键二级菜单，dispatch分发到Tree，返回当前节点数据和触发的二级menu事件名
    editChildMenu(node, event) {
      this.visibal = false;
      this.dispatch("Tree", "handle-child-menu", { node, event });
    },
  },
  filters: {
    removeUserNum(info) {
      if (info) {
        return info.indexOf("（") == -1
          ? info
          : info.substring(0, info.indexOf("（"));
      }
    },
  },
  mounted() {
    gl.node = this;
  }
};
</script>
<style lang="less" scoped>
.second-menu-box {
  li {
    cursor: pointer;
    font-size: 14px;
    padding: 5px 12px;
    color: #000;
    background: #fff;
    &:hover {
      background: #3172eb;
      color: #fff;
    }
  }
}

.ivu-icon-arrow-right-b:before {
  content: "\F330";
  display: block;
  height: 13px;
  width: 13px;
  border: 1px solid #979c9f;
  color: #979c9f;
  border-radius: 1px;
  line-height: 12px;
  font-size: 10px;
}
.ivu-tree-arrow-open {
  i {
    transform: unset;
  }
  .ivu-icon-arrow-right-b {
    &:before {
      content: "\F418";
    }
  }
}
.ivu-tree-title-selected,
.ivu-tree-title-selected:hover {
  background: #dff3fe;
}

.text-node {
  display: inline-block;
  width: 7px;
  height: 7px;
  background: #8b9094;
  border-radius: 50%;
  position: relative;
  top: -2px;
}
.ivu-tree-title {
  max-width: 170px;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 4px;
  margin-left: 4px;
}
.context-menu-box {
  .context-menu-item {
    display: flex;
    cursor: pointer;
    font-size: 14px;
    align-items: center;
    &:hover {
      background: #3172eb;
      color: #fff;
    }
    .menu-list-item {
      padding: 5px 12px;
      width: 100%;
    }
    /deep/ .ivu-poptip {
      width: 100%;
      .ivu-poptip-rel {
        width: 100%;
        .menu-list-item {
          padding: 5px 12px;
          width: 100%;
        }
      }
    }
  }
}
.tree-menu-pop-box {
  .has-child-menu {
    position: relative;
    &:hover {
      &:after {
        border-left-color: #fff;
      }
    }
    &:after {
      content: "";
      display: block;
      position: absolute;
      top: 10px;
      right: 10px;
      border: 5px solid transparent;
      border-left-color: #000;
    }
  }
}
</style>
<style lang="less">
.tree-menu-pop-box {
  .ivu-poptip-body {
    padding: 5px 0;
  }
  &.ivu-poptip-popper[x-placement^="right"] .ivu-poptip-arrow:after {
    left: 0;
  }
}
.ivu-tree {
  ul {
    li {
      span {
        vertical-align: middle;
      }
    }
  }
  & > .ivu-tree-children {
    & > li {
      & > .ivu-tree-children {
        & > li {
          & > .ivu-poptip {
            .ivu-tree-title {
              font-size: 14px;
              font-weight: bold;
            }
          }
          & > .ivu-tree-title {
            font-size: 14px;
            font-weight: bold;
          }
        }
      }
      & > .ivu-poptip {
        .ivu-tree-title {
          font-size: 16px;
          font-weight: bold;
        }
      }
      & > .ivu-tree-title {
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
}
</style>
