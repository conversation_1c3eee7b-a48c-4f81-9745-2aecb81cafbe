<template>
	<div class="outer">
		<div class="pageBar">
			<Page :total="total" show-elevator :current="current" :page-size="pageSize" size="small" @on-change="change"></Page>
		</div>
		<div class="pageBar pageTotal">
			<span>共{{total%pageSize>0?parseInt(total/pageSize)+1:parseInt(total/pageSize)}}页</span>
		</div>
	</div>
</template>
<script>
	export default{

		data(){
			return {
			};
		},
		methods:{
			 change: function (selectedPage) {
	            this.$emit('refreshPage',{pageno:selectedPage});
	        }
		},
		props: {
            current: {
                type: Number,
                default: 1
            },
            total: {
                type: Number,
                default: 0
            },
            pageSize: {
                type: Number,
                default: 10
            }
		}
	}
</script>

<style lang="less" scoped >
	/**.outer{
		padding:10px;
		background-color: #FFFFFF;
		text-align: center;
		Button{
			background-color: #eeedf5;
			width: 46px;
			height: 20px;
			margin-left: 5px;
		}
	}*/
	.outer{
		padding:10px;
		background-color: #FFFFFF;
		text-align:center;
	}
	.pageBar{
		display: inline-block !important;
		vertical-align: middle
	}
	.pageTotal{
		height: 24px;
		line-height: 24px;
		margin-left:29px;
  }

  .ivu-page /deep/ li.ivu-page-item-active{
    border:1px solid #429EFE;
  }

</style>
