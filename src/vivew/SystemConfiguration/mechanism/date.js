const moment = require('moment');
export function formatDate(date, fmt) {
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    let o = {
    	'y+': date.getFullYear(),
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds()
    };
    for (let k in o) {
        if (new RegExp(`(${k})`).test(fmt)) {
            let str = o[k] + '';
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : padLeftZero(str));
        }
    }
    return fmt;
};

function padLeftZero(str) {
    return ('00' + str).substr(str.length);
}

//与当前时间比较，今天：以hh:mm显示；不是今天：以MM-dd显示
export function compareDate(longMsgPublishTime){
	var beginDate = new Date();
	beginDate.setHours(0);
	beginDate.setMinutes(0);
	beginDate.setSeconds(0)
	beginDate.setMilliseconds(0);
	
	var endDate = new Date();
	endDate.setHours(23);
	endDate.setMinutes(59);
	endDate.setSeconds(59);
	endDate.setMilliseconds(999);
	if(beginDate > longMsgPublishTime){
		return formatDate(new Date(longMsgPublishTime), 'MM-dd');
	}else if(endDate < longMsgPublishTime){
		return formatDate(new Date(longMsgPublishTime), 'MM-dd');
	}else if( longMsgPublishTime >= beginDate && longMsgPublishTime <= endDate){
		return formatDate(new Date(longMsgPublishTime), 'hh:mm');
	}
};

//获取当前周时间范围
export function getCurrentWeek(){
    var timeRange = new Array(); 

    var curMonday = moment().week(moment().week()).startOf('week') + (24*60*60*1000);
    var curSunday = moment().week(moment().week()).endOf('week') + (24*60*60*1000);
    
    timeRange.push(curMonday); //本周起始时间   
    timeRange.push(curSunday); //本周终止时间   
    
    return timeRange; 
}

//获取上前周时间范围
export function getPreviousWeek(){
	//起止日期数组   
    var timeRange = new Array(); 
    
    var preMonday = moment().week(moment().week() - 1).startOf('week') + (24*60*60*1000);
    var preSunday = moment().week(moment().week() - 1).endOf('week') + (24*60*60*1000);

    //添加至数组   
    timeRange.push(preMonday); 
    timeRange.push(preSunday); 

    return timeRange; 
}

//获取当天时间范围
export function getToday(){
	//起止日期数组   
    var timeRange = new Array(); 
    
    var dayOfStart = moment().startOf('day').toDate();
    var dayOfEnd = moment().toDate();

    //添加至数组   
    timeRange.push(dayOfStart.getTime()); 
    timeRange.push(dayOfEnd.getTime()); 

    return timeRange; 
}

//获取近三日时间范围
export function getRecentThreeDays(){
	//起止日期数组   
    var timeRange = new Array(); 
    
    var startDay = moment().add(-3,'day').toDate();
    var endDay = moment().toDate();

    //添加至数组   
    timeRange.push(startDay.getTime()); 
    timeRange.push(endDay.getTime()); 

    return timeRange; 
}

//获取近一月时间范围
export function getRecentMonth(){
	//起止日期数组   
    var timeRange = new Array(); 
    
    var startDay = moment().add(-30,'day').toDate();
    var endDay = moment().toDate();

    //添加至数组   
    timeRange.push(startDay.getTime()); 
    timeRange.push(endDay.getTime()); 

    return timeRange; 
}
