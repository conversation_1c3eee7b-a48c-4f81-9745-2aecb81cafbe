<template>
  <div class="waring_frame">
    <ClassificationFrame title="用户角色">
      <list
        v-for="(item, index) in roleList"
        :key="index"
        :itemData="item"
        :index = "index"
        :activeId="roleId"
      />
    </ClassificationFrame>
    <ClassificationFrame title="岗位配置">
      <list
        v-for="(item, index) in configList"
        :key="index"
        :itemData="item"
        :index = "index"
        :activeId="configId"
        :showRight="true"
      />
    </ClassificationFrame>
  </div>
</template>

<script>
import ClassificationFrame from "./components/ClassificationFrame";
import list from "./components/list.vue";
export default {
  name: "",
  //import 引入组件
  components: {
    ClassificationFrame,
    list,
  },
  data() {
    return {
      roleId: "",
      configId: "",
      roleList:[],
      configList:[]
    };
  },
  watch: {
    roleId(val) {
      this.getConfigData(val);
    },
  },
  methods: {
    getRoleList() {
      let getURL = gl.serverURL + "/sys/power/roleList";
      this.$http.get(getURL).then((resp) => {
        let ret = resp.body;
        if (ret.status === 0) {
          if (ret.data&&ret.data.length>0){
            for (const role of ret.data) {
              let tmp = {
                id: role.roleId,
                name: role.roleName
              }
              this.roleList.push(tmp);
            }
            this.roleId = ret.data[0].roleId;
          }
        } else {
          this.$Message.error({
            content: ret.message,
            duration: 3,
            closable: false,
          });
        }
      });
    },
    getConfigData(roleId) {
      if (!roleId){
        this.$Message.error("请先选择角色");
      }
      this.configList = [];
      let param = {
        roleId:roleId
      }
      let url = gl.serverURL + "/config/station/list";
      this.$http.get(url,{params:param}).then((res) => {
        let ret = res.body;
        if (ret.status === 0) {
          this.configList = ret.data;
          if (this.configList&&this.configList.length>0){
            this.configId = this.configList[0].id;
          }
        } else {
          this.$Message.error({
            content: ret.message,
            duration: 3,
            closable: false,
          });
        }
      });
    }
  },
  mounted() {
    this.getRoleList();
  },
};
</script>

<style lang='less' scoped>
.waring_frame {
  padding: 30px 50px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
}
</style>
