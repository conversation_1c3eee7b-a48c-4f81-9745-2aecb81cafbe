<template>
  <div class="list_frame" @click="active(itemData.id)">
    <div
      class="list_item"
      :style="{
        background:
          activeId === itemData.id
            ? '#ECF7FF'
            : editId === itemData.id
            ? '#fff'
            : '#f5f7f9'
      }"
      @mouseover.stop="mouseover(itemData.id)"
      @mouseleave="mouseout"
    >
      <div class="list_left">
        <span class="text" v-if="editId !== itemData.id" :style="{color: activeId === itemData.id?'#1C7FF1':'#626772'}">{{
          itemData.name
        }}</span>
        <input type="text" class="list_input" v-model="name" v-else />
      </div>
      <div class="list_right">
        <div class="operate" v-if="showRight">
          <!-- 在这文字按钮上写点击事件必须加stop  例如  @click.stop -->
          <template v-if="hoverId == itemData.id && editId !== itemData.id">
            <span class="btn" @click.stop="move('上')" v-if="hasPermission('/config/station/addOrEdit')">上移</span>
            <span class="btn" @click.stop="move()" v-if="hasPermission('/config/station/addOrEdit')">下移</span>
            <span class="btn" @click.stop="edit(itemData)" v-if="hasPermission('/config/station/addOrEdit')">编辑</span>
            <Poptip confirm title="是否删除该信息?" @on-ok="del(itemData.id)" v-if="hasPermission('/config/station/delete')">
              <span class="btn" >删除</span>
            </Poptip>

          </template>
          <template v-if="editId === itemData.id">
            <span class="btn" @click.stop="edit('')">取消</span>
            <span class="btn" @click.stop="save()">保存</span>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["index","itemData", "activeId","showRight"],
  //import 引入组件
  components: {},
  data() {
    return {
      hoverId: null,
      name:'',
      editId:''
    };
  },
  methods: {
    mouseover(id) {
      this.hoverId = id;
    },
    mouseout() {
      this.hoverId = null;
    },
    edit(item){
      this.editId = item.id;
      this.name = item.name
    },

    save() {
      let param = {
        roleId: this.itemData.roleId,
        sortNum:this.itemData.sortNum,
        name: this.name,
        id: this.itemData.id
      };
      this.$http.post(gl.serverURL + "/config/station/addOrEdit",param).then((res) => {
        let data = res.body.data;
        if (data&&data>0){
          return this.$Message.error("名称不能重复!");
        }else {
          this.$Message.success("修改成功!");
          this.edit('');
          this.$parent.$parent.getConfigData(this.itemData.roleId);
        }
      }).catch(() => {
        this.$Message.error("修改失败！");
      });
    },
    active(id) {
      if (this.$parent.title == "用户角色") {
        this.$parent.$parent.roleId = id;
      } else if (this.$parent.title == "岗位配置") {
        this.$parent.$parent.configId = id;
      }
    },
    del(id){
      this.$http.get(gl.serverURL + "/config/station/delete?ids="+id).then((res) => {
        let data = res.body.data;
        if (data&&data>0){
          this.$Message.success("删除成功!");
          this.$parent.$parent.getConfigData(this.itemData.roleId);
        }else {
          this.$Message.error("删除失败!");
        }
      });
    },

    move(up){
      let data = this.$parent.$parent.configList;
      let id2 = '';
      if (up){
        id2 = data[this.index-1]?data[this.index-1].id:'';
      }else {
        id2 = data[this.index+1]?data[this.index+1].id:'';
      }
      if (!id2){
        this.$Message.error("没有可以上移或下移的了!");
        return ;
      }
      let param = {
        id1: this.itemData.id,
        id2: id2
      }
      this.$http.get(gl.serverURL + "/config/station/moveNode",{params:param}).then((res) => {
        let status = res.body.status;
        if (status==0){
          this.$Message.success("移动成功!");
          this.$parent.$parent.getConfigData(this.itemData.roleId);
        }else {
          this.$Message.error("移动失败!");
        }
      });
    }
  },
  mounted() {},
};
</script>

<style lang='less' scoped>
.list_frame {
  margin-bottom: 5px;
  .list_item {
    width: 780px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    .list_left {
      .text {
        padding-left: 10px;
        color: #626772;
        text-align: justify;
        line-height: 20px;
        font-weight: 400;
      }
      .list_input {
        height: 40px;
        width: 414px;
        background: #ffffff;
        border: 1px solid #dfe3ed;
        padding-left: 10px;
        font-size: 14px;
        color: #626772;
      }
    }
    .list_right {
      .operate {
        padding-right: 10px;
        display: flex;
        justify-content: right;
        .btn {
          text-decoration: underline;
          display: inline-block;
          width: 38px;
          text-align: right;
          color: #1c7ff1;
          font-weight: 600;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
