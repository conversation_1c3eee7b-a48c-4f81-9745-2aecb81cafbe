<template>
  <div class="frame_frame">
    <div class="frame_header">
      <div class="title">{{ title }}</div>
      <div class="opting" @click="addStart = true" v-show="title == '岗位配置'&&$parent.roleId&&hasPermission('/config/station/addOrEdit')">新增</div>
    </div>
    <div class="frame_contnent">
      <slot />
      <div class="add" v-if="addStart">
        <input type="text" class="list_input" v-model="name"/>
        <span>
          <span class="btn" @click="cancel">取消</span>
          <span class="btn" @click="add">保存</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["title"],
  //import 引入组件
  components: {},
  data() {
    return {
      addStart:false,
      name: ''
    };
  },
  methods: {
    add(){
      if (!this.name){
        return this.$Message.warning("名称不能为空!");
        return ;
      }
      let sortNum = this.getLastSortNum(this.$parent.configList)
      let param = {
        roleId: this.$parent.roleId,
        sortNum:sortNum,
        name: this.name
      };
      this.$http.post(gl.serverURL + "/config/station/addOrEdit",param).then((res) => {
        let data = res.body.data;
        if (data&&data>0){
          return this.$Message.error("名称不能重复!");
        }else {
          this.$Message.success("新建成功!");
          this.addStart = false;
          this.name = '';
          this.$parent.getConfigData(this.$parent.roleId);
        }
      }).catch(() => {
        this.$Message.error("新增失败！");
      });
    },

    getLastSortNum( list){
      if (list&&list.length>0){
        return list[list.length-1].sortNum +1;
      }else {
        return 1;
      }
    },

    cancel(){
      this.addStart = false
    }
  },
  mounted() {},
};
</script>

<style lang='less' scoped>
.frame_frame {
  background: #fff;
  border: 1px solid #dfe3ed;
  height: 740px;
  width: 810px;
  margin: -1px;
  overflow-y: auto;
  overflow-x: hidden;
  .frame_header {
    background: #fafafa;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    border-bottom: 1px solid #dfe3ed;

    .title {
      font-size: 16px;
      color: #383f4f;
      font-weight: 600;
    }
    .opting {
      font-size: 14px;
      color: #1c7ff1;
      text-align: justify;
      line-height: 20px;
      font-weight: 600;
      text-decoration: underline;
      cursor: pointer;
    }
  }
  .frame_contnent {
    padding: 20px;
  }
}
.list_input {
  height: 40px;
  width: 686px;
  background: #ffffff;
  border: 1px solid #dfe3ed;
  padding-left: 10px;
  font-size: 14px;
  color: #626772;
}
.btn {
          text-decoration: underline;
          display: inline-block;
          width: 38px;
          font-size: 14px;
          text-align: right;
          color: #1c7ff1;
          font-weight: 600;
          cursor: pointer;
        }
</style>
