<template>
  <transition name="confirm-fade">
    <div v-if="isShowConfirm" class="my-confirm" @click.stop="clickFun('clickCancel')">
      <div class="confirm-content-wrap" @click.stop>
        <div>
          <h3  class="my-confirm-title">{{ titleText }}</h3>
          <p v-if=" eventName!='事件名' && roleName =='角色名'" class="my-confirm-content ">{{ content }}<span style="color: red;font-weight: bold;" class="contentCloumn" :title="eventName">{{eventName | ellipsis}}</span>
            <span v-if="issueType==1 || issueType==2">
              信息？
            </span>
            <span v-else>
              事件？
            </span>
          </p>
          <p v-if=" roleName!='角色名' && eventName =='事件名' " class="my-confirm-content">{{ content }}<span >{{roleName}}</span>角色？</p>
          <p v-if="roleName=='角色名' && eventName =='事件名'" class="my-confirm-content">{{ content }}</p>
        </div>
            <div class="my-operation">
              <div v-if="type==='confirm'" class="my-cancel-btn" @click="clickFun('clickCancel')" >
                <button class="ivu-btn ivu-btn-text" style="margin-right:20px;border:none;">{{ cancelText }}</button>
              </div>
              <div class="confirm-btn" @click="clickFun('clickConfirm')">
                <button class="ivu-btn ivu-btn-text"  style="color:white;margin-right:30px;background: #429EFE;">{{ confirmText }}</button>
              </div>
            </div>

      </div>
    </div>
  </transition>
</template>

<script type="text/ecmascript-6">
export default {
  data () {
    return {
      isShowConfirm: false, // 用于控制整个窗口的显示/隐藏
      titleText: '操作提示', // 提示框标题
      eventName: '事件名',
      roleName: '角色名',
      content: 'Say Something ...', // 提示框的内容
      cancelText: '取消', // 取消按钮的文字
      confirmText: '确认', // 确认按钮的文字
      type: 'confirm', // 表明弹框的类型：confirm - 确认弹窗（有取消按钮）；alert - 通知弹框（没有取消按钮）
      outerData: null, // 用于记录外部传进来的数据，也可以给外部监听userBehavior，事件的函数提供判断到底是哪个事件触发的
      issueType:'下发类型'//用于标记中央下发类型（1 信息 其他为事件）
    }
  },
  methods: {
    show (content, config) {
      this.content = content || 'Say Something ...'

      if (Object.prototype.toString.call(config) === '[object Object]') {
        // 确保用户传递的是一个对象
        this.titleText = config.titleText || '操作提示'
        this.eventName = config.eventName || '事件名'
        this.roleName = config.roleName || '角色名'
        this.cancelText = config.cancelText || '取消'
        this.confirmText = config.confirmText || '确认'
        this.type = config.type || 'confirm'
        this.outerData = config.data || null
        this.issueType=config.issueType || '中央下发类型（1 信息 其他为事件）'
      }

      this.isShowConfirm = true
    },
    hidden () {
      this.isShowConfirm = false
      this.titleText = '操作提示'
      this.eventName ='事件名'
      this.roleName = '角色名'
      this.cancelText = '取消'
      this.confirmText = '确认'
      this.type = 'confirm'
      this.outerData = null
      this.issueType ='中央下发类型（1 信息 其他为事件）'
    },
    clickFun (type) {
      if(type=='clickCancel'){

      }else{
         this.$emit('userBehavior', type, this.outerData)
      }

      this.hidden()
    }
  },
  filters:{
    name:"xx",
    ellipsis (value) {
      if (!value) return ''
      if (value.length > 28) {
        return value.slice(0,28) + '...'
      }
      return value
    },
  }

}
</script>

<style scoped>
    /* 默认按钮样式*/
    button.ivu-btn{
      width:  60px;
      height: 30px;
      line-height: 14px;
      /* padding: 3px 10px; */
    }

    button.ivu-btn-text {
      border:1px #89898E solid;
      border-radius:0;


    }
    .contentCloumn{
        word-break:break-all;
        word-wrap:break-word
    }
  .my-confirm {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 15000;
    /* 这里防止当用户长按屏幕，出现的黑色背景色块，以及 iPhone 横平时字体的缩放问题 */
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }

  /* 进入和出去的动画 */
  /* .confirm-fade-enter-active {
    animation: opacity 0.3s;
  }
  .confirm-fade-enter-active .confirm-content-wrap {
    animation: scale 0.3s;
  }
  .confirm-fade-leave-active {
    animation: outOpacity 0.2s;
  } */

  /* 包裹层容器样式 */
  .confirm-content-wrap {
    position: absolute;
    top: 28%;
    left: 0;
    right: 0;
    width: 350px;
    height: 220px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 3px;
    z-index: 15000;
    user-select: none;
    box-shadow: 0 3px 7px 3px  rgba(27, 31, 27, 0.2);    /*垂直位置 水平位置 模糊或清晰度 宽度范围 RGB颜色值，透明度*/
    -moz-box-shadow: 0 3px 7px 3px rgba(27, 31, 27, 0.2);    /*兼容FF*/
    -webkit-box-shadow: 0 3px 7px 3px rgba(27, 31, 27, 0.2);    /*兼容Chrome、Safari、Opera12+*/
  }

  /* 顶部标题部分 */
  .my-confirm-title {
    border-top: 3px solid #429EFE;
    height:40px;
    border-bottom: 1px solid #E9EDF1;
    padding-top: 8px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: #000000;
  }

  /* 中间内容部分 */
  .my-confirm-content {
    padding: 0 15px;
    padding-top: 30px;
    margin-left: 30px;
    margin-bottom: 66px;
    /* text-align: center; */
    font-size: 16px;
    color: #666;
    line-height: 1.5;
  }

  /* 底部按钮样式 */
  .my-operation {
    display: flex;
    float:right;
    margin-bottom:30px;
    position: absolute;
    right: 0px;
    bottom:0px;
    /* border-top: 1px solid #eee; */
  }
  .my-operation .my-cancel-btn, .confirm-btn {
    flex: 1;
  }

  .my-operation .my-cancel-btn {

    border:none;
  }

  .my-operation .my-btn-text {
    text-align: center;
    font-size: 16px;
    margin: 14px 0;
    padding: 6px 0;
  }

  /* 其他修饰样式 */
  .my-border-right {
    border-right: 1px solid #eee;
  }

  /* 进来的动画 */
  @keyframes opacity {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes scale {
    0% {
      transform: scale(0);
    }
    60% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }

  /* 出去的动画 */
  @keyframes outOpacity {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
</style>
