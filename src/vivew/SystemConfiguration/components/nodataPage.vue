<template>
	<div>
		<!--<div class="bcg"><span class="nodata-msg">没有找到相关数据......</span></div>-->
		<div :class="classVal"></div>
		<div :class="classVal+'-msg'">没有找到相关数据......</div>
	</div>

</template>

<script>
	import '../mechanism/index.less'
	export default {
		data() {
			return {};
		},
		props: {
			classVal:{
				type:String,
				default:"nodata1"
			}
		}
	}
</script>

<style scoped>

	.nodata1-msg{
		display: inline-block;
		color: black;
		width: 100%;
		/*margin-top: 33%;*/
		font-size: 13px;
		text-align:center;
	}
	.nodata1{
		height:130px;
		background:url(./nodata.png) no-repeat;
	    background-position:50% 70%;
	    background-size: auto 75px;
	}
	.nodata2-msg{
		display: inline-block;
		color: black;
		width: 100%;
		height:100px;
		/*margin-top: 33%;*/
		font-size: 15px;
		text-align:center;
	}
	.nodata2{
		height:200px;
		text-align:center;
		background:url(./nodata.png) no-repeat;
	    background-position:50% 85%;
	    background-size: auto 55%;
	}
	.nodata3-msg{
		display: inline-block;
		color: black;
		width: 100%;
		/*margin-top: 33%;*/
		font-size: 13px;
		text-align:center;
	}
	.nodata3{
		height:110px;
		background:url(./nodata.png) no-repeat;
	    background-position:50% 70%;
	    background-size: auto 75px;
	}

	.nodata4-msg{
		display: inline-block;
		color: black;
		width: 100%;
		/*height:100px;*/
		/*margin-top: 33%;*/
		font-size: 15px;
		text-align:center;
	}
	.nodata4{
		height:230px;
		text-align:center;
		background:url(./nodata.png) no-repeat;
	    background-position:50% 80%;
	    background-size: auto 55%;
	}
	.nodata5-msg{
		display: inline-block;
		color: black;
		width: 100%;
		/*margin-top: 33%;*/
		font-size: 13px;
		text-align:center;
	}
	.nodata5{
		height:80px;
		background:url(./nodata.png) no-repeat;
	    background-position:50% 70%;
	    background-size: auto 55%;
	}
	.nodata6-msg{
		display: inline-block;
		color: black;
		width: 100%;
		/*margin-top: 33%;*/
		font-size: 13px;
		text-align:center;
	}
	.nodata6{
		height:330px;
		background:url(./nodata.png) no-repeat;
	    background-position:50% 70%;
	    background-size: auto 55%;
	}
	.nodata7{
		height:65px;
		background:url(./nodata.png) no-repeat;
	    background-position:50% 70%;
	    background-size: auto 55%;
		margin-top: -15px;
	}
	.nodata7-msg{
		display: inline-block;
		color: black;
		width: 100%;
		font-size: 13px;
		text-align:center;
	}
  	.nodata8-msg{
		display: inline-block;
		color: black;
		width: 100%;
		/*height:100px;*/
		/*margin-top: 33%;*/
		font-size: 15px;
		text-align:center;
	}
	.nodata8{
    margin-top:175px;
		height:230px;
		text-align:center;
		background:url(./nodata.png) no-repeat;
	    background-position:50% 80%;
	    background-size: auto 55%;
	}
</style>
