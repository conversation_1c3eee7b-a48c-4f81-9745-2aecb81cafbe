<template>
    <!-- 账号添加 -->
    <div class="index addnewAccount">
        <div class="content">
            {{data}}
        </div>
    </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
export default {
    data() {
        // 这里存放数据
        return { };
    },
    // import 引入的组件需要注入到对象中才能使用
    components: {},
    props: {
        data: {
            type: String,
        },
    },
    created() {
    },

    // 销毁前清除定时器
    beforeDestroy() {
    },
    // 方法集合
    methods: {
    },
    // 监控 data 中的数据变化
    watch: {
    },
    //过滤器
    filters: {},
    // 生命周期 - 挂载完成（可以访问 DOM 元素）
    mounted() {},
};
</script>
<style scoped lang="less">
.content {
    font-size: 1.1rem;
}
</style>
