<template>
    <div>
        <div class="table-container">
          <div class="table-header">
            <div class="cell">序号</div>
            <div class="cell">反馈时间</div>
            <div class="cell">反馈单位</div>
            <div class="cell">反馈人</div>
            <div class="cell">功能</div>
            <div class="cell">问题和意见</div>
            <div class="cell">图片</div>
          </div>
          
          <div v-if="!loading && total !== 0" class="table-body">
            <div 
              class="table-row" 
              v-for="(item, index) in feedbackData" 
              :key="index"
            >
              <div class="cellitem serial">{{ index + 1 }}</div>
              <div class="cellitem timestamp">{{ moment(item.insertTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
              <div class="cellitem unit">{{ item.organName }}</div>
              <div class="cellitem person">
                <span>{{ item.userName }}</span>
              </div>
              <div class="cellitem function-cell">{{ dealType(item.type) }}</div>
              <div class="cellitem problem-cell" style="display:flex;justify-content:center">
                <div  ref="suggest" class="text-wrapper">
                  <span :ref="'text_' + index" class="text-content">{{ item.suggest }} </span>
                </div>
                <span :ref="'btn_' + index" v-if="showMoreBtn[index]" class="view-more" @click="seeAll(item.suggest)">查看全部</span> 
              </div>
              <div class="cellitem">
                <img :src="dealPath(item.imageStrs)" style="width:100px;height:50px"  alt="" @click="openImagePreview(item)">
                <ImgPreview
                    v-if="ImgPreviewStatus"
                    :defaultId="CarouselId"
                    :imgList="dealPicList(pictureFiles)"
                    @close="ImgPreviewStatus = false"
                />
              </div>
            </div>
          </div>
          <div class="pagewrap">
            <Page :total="total" :current="pageNo" :pageSize="10 " @on-chang="pageChange"/>
          </div>
          <nodata-page
                :classVal="'nodata2'"
                v-if="!loading && total == 0"
            ></nodata-page>
        <Spin v-if="loading">
            <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            <div>Loading</div>
        </Spin>
          
          <!-- <div class="scroll-info">滚动查看更多数据</div> -->
        </div>
        <Modal
            v-model="modal"
            title="问题和意见">
            <seeMore :data="alltxt"/>
            <template #footer>
                <Button size="large" long @click="modal = false">关闭</Button>
            </template>
        </Modal>
    </div>
</template>

<script>
import NodataPage from "../components/nodataPage.vue";
import moment from 'moment';
import ImgPreview from "@/components/imgPreview";
import seeMore from './components/seeMore.vue'
export default {
    components: {NodataPage, ImgPreview, seeMore},
    data() {
        return {
            timer:null,
            showMoreBtn: [], // 存储每条数据是否需要显示更多按钮
            modal:false,
            alltxt:'',
            pictureFiles:[],
            CarouselId:0,
            ImgPreviewStatus: false,
            loading: true,
            total: 0,
            pageNo:1,
            feedbackData: [],
            typelist:[
                {name:'热搜榜单',value:1},
                {name:'涉济舆情',value:2},
                {name:'舆情提示单',value:3},
                {name:'舆情事件',value:4},
                {name:'辟谣信息',value:5},
                {name:'其他',value:6},
            ],
            preview: false,
            urlList: [
                'https://file.iviewui.com/images/image-demo-10.jpg',
                'https://file.iviewui.com/images/image-demo-11.jpg',
                'https://file.iviewui.com/images/image-demo-12.jpg',
                'https://file.iviewui.com/images/image-demo-13.jpg',
                'https://file.iviewui.com/images/image-demo-14.jpg',
                'https://file.iviewui.com/images/image-demo-15.jpg',
                'https://file.iviewui.com/images/image-demo-16.jpg',
                'https://file.iviewui.com/images/image-demo-17.jpg',
                'https://file.iviewui.com/images/image-demo-18.jpg',
                'https://file.iviewui.com/images/image-demo-19.jpg',
                'https://file.iviewui.com/images/image-demo-20.jpg'
            ]
        }
    },
    methods: {
        moment,
        seeAll(val) {
          this.modal = true
          this.alltxt = val
        },
        dealPicList(val) {
          let arr = []
          if(val) {
            let dealarr = val.split(';')
            dealarr.forEach(item=> {
              let dealitem = `data:image/png;base64,${item}`
              arr.push(dealitem)
            })
            return arr
          }
          
        },
        dealPath(val) {
          if(val) {
            return `data:image/png;base64,${val.split(';')[0]}`
          }
        },
        openImagePreview(val) {
            this.ImgPreviewStatus= true
            this.pictureFiles = val.imageStrs
        },
        imagePre() {
            console.log(this.$ImagePreview);
            this.$ImagePreview.show({
                previewList: this.urlList
            });
        },
        pageChange(val) {
            this.pageNo = val
            this.getList()
        },
        dealType(val) {
            let str = ''
            this.typelist.forEach(item => {
                if(item.value == val) {
                    str = item.name
                }
            })
            return str

        },
        getCount() {
            let url = gl.serverURL + '/feedback/count'
            this.$http.get(url).then(res=> {
                if(res.body.status === 0) {
                    if(res.body.data !== 0) {
                        this.total = res.body.data
                        this.getList()
                    }
                }
            })
        },
        getList() {
            let params = {
                pageNo:this.pageNo,
                pageSize:10
            }
            let url = gl.serverURL + '/feedback/list'
            this.$http.get(url,{params}).then(res=> {
                if(res.body.status === 0) {
                    this.loading = false
                    this.feedbackData = res.body.data
                    console.log(this.feedbackData);
                    this.$nextTick(()=>{
                      this.feedbackData.forEach((_, index) => {
                        const textEl = this.$refs[`text_${index}`][0];
                        this.$set(this.showMoreBtn, index, 
                          textEl.scrollWidth > textEl.clientWidth
                        );
                      });
                      this.loading = false
                    })
                    
                }
            })
        },
        
    },
    mounted() {
        this.getCount()
    },
    beforeDestroy() {
      clearTimeout(this.timer)
    }

}
</script>

<style>
.pagewrap {
    display: flex;
    justify-content: center;
}
.container {
      width: 100%;
      max-width: 1800px;
      padding: 20px;
    }
    
    .card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      overflow: hidden;
    }
    
    .header {
      background: linear-gradient(90deg, #2c3e50, #4a6491);
      color: white;
      padding: 20px;
      text-align: center;
    }
    
    .header h1 {
      font-size: 2.2rem;
      margin-bottom: 10px;
    }
    
    .header p {
      font-size: 1.1rem;
      opacity: 0.9;
    }
    
    .table-container {
      width: 1720px;
      height: 810px;
      overflow: hidden;
      position: relative;
    }
    
    .table-header {
      background: #f8f9fa;
      border-bottom: 2px solid #dee2e6;
      display: grid;
      grid-template-columns: 80px 180px 200px 150px 200px 570px 300px;
      padding: 15px 10px;
      font-weight: bold;
      position: sticky;
      top: 0;
      z-index: 10;
    }
    
    .table-body {
        margin-bottom: 30px;
      /* height: calc(810px - 50px); */
      overflow-y: auto;
      background: #fff;
    }
    
    .table-row {
      height: 71px;
      display: grid;
      grid-template-columns: 80px 180px 200px 150px 200px 570px 300px;
      padding: 0px 10px;
      border-bottom: 1px solid #eee;
      transition: all 0.3s ease;
    }
    
    .cell {
      padding: 8px 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      /* overflow: hidden;
      text-overflow: ellipsis; */
    }
    .cellitem {
      padding: 8px 10px;
      display: inline-block;
      text-align: center;
      line-height: 50px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      
      
    }
    .content-wrapper {
        flex: 1;
        min-width: 0; /* 关键：允许内容收缩 */
        overflow: hidden;
    }
    
    .text-content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .text-wrapper {
        flex: 1;
        min-width: 0;
        overflow: hidden;
        display: contents;
    }
    .view-more {
        flex-shrink: 0; /* 禁止收缩 */
        color: #1890ff;
        cursor: pointer;
        margin-left: 4px;
    }
    .seeall{
      cursor: pointer;
      text-decoration: underline;
      color: rgb(28, 127, 241);    
    }
    
    .serial {
      justify-content: center;
    }
    
    .img-container {
      display: flex;
      gap: 10px;
    }
    
    .img-preview {
      width: 80px;
      height: 60px;
      border-radius: 6px;
      background: #f0f0f0;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      cursor: pointer;
      transition: transform 0.3s ease;
      border: 1px solid #e0e0e0;
    }
    
    .img-preview img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .no-image {
      color: #95a5a6;
      font-size: 0.9rem;
    }
    
    .problem-cell {
      font-size: 0.95rem;
      /* line-height: 1.5; */
    }
    
    .function-cell {
      font-weight: 500;
      color: #2c3e50;
    }
    
    .timestamp {
      color: #7f8c8d;
      font-size: 0.95rem;
    }
    
    .unit {
      font-weight: 500;
    }
    
    .person {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #3498db;
      display: flex;
      justify-content: center;
      align-items: center;
      color: white;
      font-weight: bold;
    }
    
    
    .scroll-info {
      position: absolute;
      bottom: 10px;
      right: 10px;
      background: rgba(0,0,0,0.7);
      color: white;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      z-index: 5;
    }
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.modal-container {
  background: #fff;
  border-radius: 5px;
  /* max-width: 500px; */
  transform: scale(0.7);
  animation: zoomIn 0.3s forwards; /* 打开动画 */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.7); /* 添加 translate */
}


.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}
.ivu-modal-header {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ddd;
  border-radius: 0px;
  background: linear-gradient(to right, #333173, #316cdf);
  
}
.ivu-modal {
  width: 1000px!important;
}
.ivu-modal-header .ivu-modal-header p, .ivu-modal-header .ivu-modal-header-inner {
  font-size: 24px;
  color: #fff;
  font-weight: 600;
}
.ivu-modal-body {
  padding: 16px 50px;
}
.ivu-modal-footer {
  border-radius: 0px;
}
.ivu-spin {
  margin: 100px 0;
}
</style>