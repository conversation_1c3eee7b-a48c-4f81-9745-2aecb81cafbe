<template>
  <div class="statistical">
    <div class="header">
      <div class="item">
        <div class="label">账号：</div>
        <div class="val">
          <Input v-model="account" style="width: 130px" />
        </div>
      </div>
      <div class="item">
        <div class="label">部门：</div>
        <div class="val">
          <Select v-model="organName" clearable style="width: 130px">
            <Option
              v-for="item in $parent.OrganList"
              :value="item"
              :key="item"
              >{{ item }}</Option
            >
          </Select>
        </div>
      </div>
      <div class="item">
        <div class="label">系统：</div>
        <div class="val">
          <Select v-model="sysCode" clearable style="width: 130px">
            <Option
              v-for="(val, key, index) in filterLists"
              :value="key"
              :key="index"
              >{{ val }}</Option
            >
          </Select>
        </div>
      </div>
      <div class="item">
        <div class="label">操作模块：</div>
        <div class="val">
          <Select v-model="module" clearable style="width: 130px">
            <Option
              v-for="(val, key, index) in LogFilterList[sysCode].children"
              :value="key"
              :key="index"
              >{{ val.name }}</Option
            >
          </Select>
        </div>
      </div>
      <div class="item">
        <div class="label">操作内容：</div>
        <div class="val">
          <Select v-model="content" clearable style="width: 130px">
            <Option
              v-for="(val, key, index) in contentList"
              :value="key"
              :key="index"
              >{{ val.name }}</Option
            >
          </Select>
        </div>
      </div>
      <div class="item">
        <div class="label">操作时间：</div>
        <div class="val">
          <DatePicker
            type="datetimerange"
            placeholder="请选择起止日期"
            style="width: 300px"
            @on-change="getTimer"
            v-model="timer"
          ></DatePicker>
        </div>
      </div>

      <span class="btn" @click.stop="getInfo()">查询</span>
      <span class="btn" @click.stop="exportExcel()">导出</span>
    </div>
    <!-- sysCode -->
    <div class="title" v-if="TheHierarchy.length > 0 && listData.length > 0">
      <span
        >{{
          (TheHierarchy[TheHierarchy.length - 1].account
            ? TheHierarchy[TheHierarchy.length - 1].account + "-"
            : "") +
          (filterLists[TheHierarchy[TheHierarchy.length - 1].sysCode]
            ? filterLists[TheHierarchy[TheHierarchy.length - 1].sysCode] + ""
            : "全部") +
          (TheHierarchy[TheHierarchy.length - 1].module
            ? "-" + TheHierarchy[TheHierarchy.length - 1].module
            : "")
        }}使用情况统计（{{
          moment(TheHierarchy[TheHierarchy.length - 1].startTime).format(
            "YYYY-MM-DD HH:mm:ss"
          )
        }}~{{
          moment(TheHierarchy[TheHierarchy.length - 1].endTime).format(
            "YYYY-MM-DD HH:mm:ss"
          )
        }}）</span
      >
      <span
        v-show="
          TheHierarchy.length > 1 &&
          TheHierarchy.length < 4 &&
          TheHierarchy[TheHierarchy.length - 2]
        "
        class="getBack"
        @click="getBack()"
      >
        返回
      </span>
    </div>
    <XZTable
      v-if="TheHierarchy.length > 0 && !loading"
      :rules="rules"
      :listData="listData"
      :explore="explore"
      :TheHierarchyNum="TheHierarchy.length - 1"
      :searchSysCode="searchSysCode"
    />
    <nodata-page
      :classVal="'nodata2'"
      v-if="!loading && listData.length == 0"
    ></nodata-page>
    <Spin v-if="loading">
      <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
  </div>
</template>

<script>
import XZTable from "../componetns/XZTable";
import moment from "moment";
import LogFilterList from "../theLog/filter";
import NodataPage from "../../components/nodataPage.vue";
const rules = {
  maxColumn: 9,
  headList: [
    { text: "账号", key: "userName" },
    { text: "系统", key: "groupName" },
    { text: "登录次数", key: "logNum" },
    { text: "操作次数", key: "statNum" },
  ],
  headList1: [
    { text: "账号", key: "userName" },
    { text: "模块", key: "groupName" },
    { text: "操作次数", key: "statNum" },
  ],
  headList2: [
    { text: "账号", key: "userName" },
    { text: "操作内容", key: "groupName" },
    { text: "操作次数", key: "statNum" },
  ],
  headList3: [],
};
const filterLists = {
  全部: "全部",
  trs_yq: "监测分析",
  guide_leader: "指挥联动",
  guide_union: "舆情报送",
  trs_jb: "西藏举报",
  positive_publicize: "正面宣传",
  trs_wp: "网评引导",
  trs_sd: "属地管理",
  knoledge_mng: "知识管理",
  trs_citys: "七地市",
  trs_provinces: "四省系统",
  trs_lv: "领导视图",
};
export default {
  data() {
    return {
      rules,
      filterLists,
      LogFilterList: LogFilterList.sysCodeList,
      account: "",
      timer: ["", ""],
      module: "全部",
      contentList: [],
      sysCode: "全部",
      content: "全部",
      listData: [],
      TheHierarchy: [],
      loading: false,
      exportUrl: "",
      exportParam: {},
      organName: "全部",
      searchSysCode: "",
    };
  },
  components: { XZTable, NodataPage },
  created() {
    window.explore = this.explore;
  },
  watch: {
    TheHierarchy(v) {
      console.log(v);
    },
    sysCode(val) {
      console.log(val);
      if (val) {
        this.module = "全部";
        this.content = "全部";
        this.contentList =
          this.LogFilterList[this.sysCode].children[val].children;
      } else {
        this.$nextTick(() => (this.sysCode = "全部"));
      }
    },
    module(val) {
      if (val) {
        this.content = "全部";
        this.contentList =
          this.LogFilterList[this.sysCode].children[val].children;
      } else {
        this.$nextTick(() => {
          this.module = "全部";
          this.content = "全部";
          this.contentList =
            this.LogFilterList[this.sysCode].children[val].children;
        });
      }
    },
  },
  mounted() {
    this.contentList =
      this.LogFilterList[this.sysCode].children[this.module].children;
  },
  methods: {
    moment,
    getTimer(data) {
      if (data[1].slice(-8) === "00:00:00") {
        data[1] = data[1].slice(0, -8) + "23:59:59";
        this.timer = data;
      }
    },
    getInfo() {
      if (this.timer[0] == "") {
        this.$Message.error("请选择时间后重试");
        return false;
      }
      this.TheHierarchy = [];
      if (this.account !== "") {
        this.loading = true;
        this.getStatByAccount();
      } else {
        this.loading = true;
        this.listData = [];
        this.getStatBySys();
      }
    },
    exportExcel() {
      if (!this.exportUrl) {
        this.$Message.error("请先点击查询");
        return;
      }
      this.exportUtil.exportPost(
        gl.serverURL + this.exportUrl,
        this.exportParam
      );
    },
    explore(data) {
      this.loading = true;
      let params = JSON.parse(
        JSON.stringify(this.TheHierarchy[this.TheHierarchy.length - 1])
      );
      if (this.filterLists[data]) {
        params.sysCode = data;
        this.searchSysCode = data;
      } else {
        params.module = data;
      }
      this.getStatByAccount(params);
      this.TheHierarchy.push(params);
    },
    getBack() {
      this.loading = true;
      this.TheHierarchy.pop();
      // console.log(this.TheHierarchy);
      this.getStatByAccount(this.TheHierarchy[this.TheHierarchy.length - 1]);
    },
    getStatByAccount(pas) {
      let params;
      if (pas) {
        params = pas;
      } else {
        params = this.getAccountParam();
        if (params.module && params.module != "") {
          this.TheHierarchy[2] = params;
        } else if (params.sysCode && params.sysCode != "") {
          this.TheHierarchy[1] = params;
        } else {
          this.TheHierarchy.push(params);
        }
      }
      this.exportUrl = "";
      this.listData = [];
      this.$http
        .get(gl.serverURL + "/behavior/statByAccount", {
          params: params,
        })
        .then((res) => {
          if (res.body.data) {
            this.listData = res.body.data;
          }
          this.loading = false;
          this.exportUrl = "/behavior/exportByAccount";
          this.exportParam = params;
        })
        .catch((err) => {
          this.$Message.error("服务器出错");
          this.loading = false;
        });
    },

    getAccountParam() {
      let params = {
        account: this.account,
        endTime: this.moment(this.timer[1]).format("YYYY-MM-DD HH:mm:ss"),
        startTime: this.moment(this.timer[0]).format("YYYY-MM-DD HH:mm:ss"),
        sysCode: !this.sysCode || this.sysCode == "全部" ? "" : this.sysCode,
        organName:
          !this.organName || this.organName == "全部" ? "" : this.organName,
        content: !this.content || this.content == "全部" ? "" : this.content,
        module: !this.module || this.module == "全部" ? "" : this.module,
      };
      return params;
    },

    getStatBySys() {
      let params = this.getSysParams();
      this.exportUrl = "";
      this.$http
        .get(gl.serverURL + "/behavior/statBySys", {
          params: params,
        })
        .then((res) => {
          let arr = res.body.data;
          this.exportUrl = "/behavior/exportBySys";
          this.exportParam = params;
          if (arr) {
            let data = [...arr.data, arr.total];
            let headList = [];
            let list = [];
            arr.title.forEach((i, index) => {
              let headObj = {
                text: i,
                key: index,
              };
              headList.push(headObj);
            });
            this.TheHierarchy[3] = params;
            this.rules.headList3 = headList;
            data.forEach((i, index) => {
              let obj = {};
              headList.forEach((j) => {
                obj[j.key] = i[j.key];
              });
              list.push(obj);
            });
            this.listData = list;
          }
          this.loading = false;
        })
        .catch((err) => {
          this.$Message.error("服务器出错");
          this.loading = false;
        });
    },

    getSysParams() {
      let params = {
        endTime: this.moment(this.timer[1]).format("YYYY-MM-DD HH:mm:ss"),
        startTime: this.moment(this.timer[0]).format("YYYY-MM-DD HH:mm:ss"),
        sysCode: this.sysCode == "全部" ? "" : this.sysCode,
        organName:
          !this.organName || this.organName == "全部" ? "" : this.organName,
        content: !this.content || this.content == "全部" ? "" : this.content,
        module: !this.module || this.module == "全部" ? "" : this.module,
      };
      return params;
    },
  },
};
</script>

<style scoped lang="less">
.statistical {
  width: 1720px;
  margin: 30px auto;
  // height: calc(~"100vh - 240px");
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  position: relative;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // width: 70%;
    .item {
      display: flex;
      align-items: center;
      .label {
      }
      .val {
      }
    }
    .btn {
      display: inline-block;
      text-align: right;
      color: #fff;
      font-weight: 600;
      cursor: pointer;
      background-color: #1c7ff1;
      padding: 5px 14px;
      border-radius: 10px;
    }
  }
  .title {
    font-weight: 600;
    margin: 20px 0 0 0;
    display: flex;
    justify-content: space-between;
    .getBack {
      color: #02a7f0;
      font-weight: 400;
      text-decoration: underline;
      font-size: 14px;
      margin-right: 50px;
      cursor: pointer;
    }
  }
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
