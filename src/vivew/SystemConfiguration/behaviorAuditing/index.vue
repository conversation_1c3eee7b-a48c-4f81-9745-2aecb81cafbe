<template>
  <div>
    <div class="nav">
      <template v-for="val in navlist">
        <div
          :key="val.path"
          :class="['item', val.path == $route.path ? 'active' : '']"
          v-if="hasPermission(val.config)"
          @click="
            () => {
              $router.push({ path: val.path });
            }
          "
        >
          {{ val.name }}
        </div>
      </template>
    </div>
    <router-view v-if="num != 2" />
  </div>
</template>

<script>
const navlist = [
  {
    name: "操作日志",
    path: "/SystemConfiguration/behaviorAuditing/theLog",
    config: "/behavior/list",
  },
  /*{
    name: "统计分析",
    path: "/SystemConfiguration/behaviorAuditing/statistical",
    config: "/behavior/statBySys",
  },*/
];

export default {
  data() {
    return {
      navlist,
      activeId: 0,
      num: 0,
      OrganList: [],
    };
  },
  created() {},
  mounted() {
    this.num = 0;
    for (let index = 0; index < this.navlist.length; index++) {
      if (this.hasPermission(this.navlist[index].config)) {
        this.$router.push({ path: this.navlist[index].path });
        break;
      } else {
        this.num++;
      }
    }
    // console.log(this.num);
    this.getOrganList();
  },
  methods: {
    getOrganList() {
      this.OrganList.push({
        value: null,
        name: "全部",
      });
      let getURL = gl.serverURL + "/sys/list/allDep";
      this.$http
        .get(getURL, {
          params: { organId: 0 },
        })
        .then((res) => {
          let data = res.body;
          Object.keys(data.data).forEach((key) => {
            if(key == 1){
              // const organIds = []
              // data.data[key].forEach((i) => {
              //   if (i.children && i.children.length > 0) {
              //     i.children.forEach((j) => {
              //       organIds.push(j.organId)
              //     });
              //   } else {
              //     organIds.push(i.organId)
              //   }
              // });
              this.OrganList.push({
                name: '市委网信办',
                value: 221
              })
            }else{
              data.data[key].forEach((i) => {
                if (i.children && i.children.length > 0) {
                  i.children.forEach((j) => {
                    this.OrganList.push(
                    {
                      name: j.organName.slice(j.organName.indexOf("-") + 1),
                      value: j.organId,
                    }
                    );
                  });
                } else {
                  this.OrganList.push({
                    name: i.organName,
                    value: i.organId,
                  });
                }
              });
            }
           
          });
        })
        .catch((err) => {
          this.$Message.error("服务器出错");
          debugger;
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped lang="less">
.nav {
  display: flex;
  width: 1720;
  margin: 0 auto;
  .item {
    margin: 0 30px;
    color: #333;
    background-color: #fff;
    width: 120px;
    text-align: center;
    line-height: 40px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
  }
  .active {
    background: #dbebfd;
    color: #2d8cf0;
  }
}
</style>
