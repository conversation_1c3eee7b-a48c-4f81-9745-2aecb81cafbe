<template>
  <div class="XZTable" v-if="listData.length > 0">
    <div class="header">
      <div class="item" :class="item === 1 ? 'noClass' : item === 9 ? 'contentClass' :  item === 4 ? 'timeClass' : 'otherClass'
        " v-for="item in rules.maxColumn" :key="item">
        {{ disposeTitle(item) }}
      </div>
    </div>
    <div class="content">
      <div class="line" v-for="(item, lines) in listData" :key="lines">
        <div class="item ellipsis" v-for="index in rules.maxColumn" :class="index === 1
          ? 'noClass'
          : index === 9
            ? 'contentClass' :
             index === 4 ? 'timeClass' : 'otherClass'
          " :key="index" :title="disposeVTitle(item, index - 1)" v-html="disposeContent(item, index - 1)"></div>
      </div>
    </div>
  </div>
</template>

<script>
const filterLists = {
  全部: "全部",
  trs_home: "门户",
  trs_jccz: "监测处置系统",
  trs_sdzb: "哨点直报系统",
  guide_leader: "指挥联动",
  trs_wx: '网宣系统',
  sys_code_wa: '网安系统',
  sys_code_wp: '网评引导系统',
  trs_sdt: '山东通',
  sys_wjcs: '文件传输',
  sw_jbxt: '举报系统'

};
const noChil = [
  "监测预警",
  "信息处理",
  "配置管理", //监测分析和7地市
  "我的信息", //舆情报送
  "自定义模块", //领导视图
  "网评账号",
  "知识库管理",
  "任务管理",
  "系统设置", //网评引导
  "阵地分析",
  "热点分析",
  "指令落实巡查",
]; //正面宣传
import moment from "moment";
export default {
  props: ["rules", "listData", "explore", "TheHierarchyNum", "searchSysCode"],
  data() {
    return { filterLists, noChil };
  },
  created() { },
  mounted() { },
  methods: {
    moment,
    disposeTitle(index) {
      let i =
        "headList" + (this.TheHierarchyNum > 0 ? this.TheHierarchyNum : "");
      let name = this.rules[i][index - 1] ? this.rules[i][index - 1].text : "";
      return name;
    },
    disposeContent(data, index) {
      let i =
        "headList" + (this.TheHierarchyNum > 0 ? this.TheHierarchyNum : "");
      if (this.rules[i][index]) {
        let key = this.rules[i][index].key;
        if (key == "systemNo") {
          return this.filterLists[data[key]];
        }
        //处理时间格式
        if (key == "publishTime") {
          return moment(data[key]).format("YYYY-MM-DD HH:mm:ss");
        }
        //可点击样式
        // console.log(
        //   key,
        //   this.noChil,
        //   data[key],
        //   Object.keys(this.filterLists),
        //   key == "groupName",
        //   this.noChil.includes(data[key]),
        //   Object.keys(this.filterLists).indexOf(data[key]) > -1,
        //   this.TheHierarchyNum < 3
        // );
        if (
          key == "groupName" &&
          data.statNum > 0 &&
          (this.noChil.includes(data[key]) ||
            Object.keys(this.filterLists).indexOf(data[key]) > -1) &&
          this.TheHierarchyNum < 3
        ) {
          if (data[key] == "配置管理" && "trs_yq" != this.searchSysCode) {
            return data[key];
          } else {
            return `<span onclick="explore('${data[key]
              }')" style="color: #02a7f0;cursor: pointer;" >${Object.keys(this.filterLists).indexOf(data[key]) == -1
                ? data[key]
                : this.filterLists[data[key]]
              }</span>`;
          }
        }
        //总计样式
        if (
          data.groupName == "总计" ||
          Object.values(data).indexOf("合计") != -1
        ) {
          return `<span style="font-weight: 600;" >${data[key]}</span>`;
        }
        return Object.keys(this.filterLists).indexOf(data[key]) == -1
          ? data[key]
          : this.filterLists[data[key]];
      }
    },

    disposeVTitle(data, index) {
      let i =
        "headList" + (this.TheHierarchyNum > 0 ? this.TheHierarchyNum : "");
      if (this.rules[i][index]) {
        let key = this.rules[i][index].key;
        if (key == "sysCode") {
          return this.filterLists[data[key]];
        }
        //处理时间格式
        if (key == "publishTime") {
          return moment(data[key]).format("YYYY-MM-DD HH:mm:ss");
        }
        return Object.keys(this.filterLists).indexOf(data[key]) == -1
          ? data[key]
          : this.filterLists[data[key]];
      }
    },
  },
};
</script>

<style scoped lang="less">
.XZTable {
  border-left: 1px solid #dbdcde;
  border-top: 1px solid #dbdcde;
  margin: 20px 0 50px 0;

  .item {
    min-height: 32px;
    border-right: 1px solid #dbdcde;
    border-bottom: 1px solid #dbdcde;
    line-height: 32px;
    text-align: center;
    padding: 0 20px;

    .active {}
  }

  .header {
    display: flex;

    .item {
      background-color: #e8eaf4;
      line-height: 40px;
      text-align: center;
      font-weight: 600;
      font-size: 18px;
    }
  }

  .content {
    .line {
      display: flex;
    }
  }

  .noClass {
    width: 80px;
    flex-basis: 80px;
    flex-shrink: 0;
  }

  .otherClass {
    flex: 1;
  }

  .contentClass {
    flex: 4;
  }
  .timeClass{
    flex: 1.6;
  }
}
</style>
