<template>
  <div class="theLog">
    <div class="header">
      <div class="item">
        <div class="label">机构：</div>
        <div class="val">
          <Select v-model="organName" style="width: 130px">
            <Option v-for="item in $parent.OrganList" :value="item.value" :key="item.value">{{
              item.name
            }}</Option>
          </Select>
        </div>
      </div>
      <div class="item">
        <div class="label">姓名：</div>
        <div class="val">
          <Input v-model="account" style="width: 150px" />
        </div>
      </div>
      <div class="item">
        <div class="label">关键词：</div>
        <div class="val">
          <Input placeholder="请输入关键词" v-model="keyWord" style="width: 200px" />
        </div>
      </div>
      <div class="item">
        <div class="label">操作时间：</div>
        <div class="val">
          <DatePicker
            type="datetimerange"
            placeholder="请选择起止日期"
            format="yyyy-MM-dd HH:mm:ss"
            style="width: 280px"
            @on-change="getTimer"
            v-model="timer"
          ></DatePicker>
        </div>
      </div>
      <div class="item">
        <div class="label">系统：</div>
        <div class="val">
          <Select v-model="sysCode" style="width: 130px">
            <Option 
              v-for="(val, key, index) in filterLists" 
              :value="key" 
              :key="index" 
              >{{ val.name }}</Option>
          </Select>
        </div>
      </div>
      <!-- <div class="item">
        <div class="label">一级模块：</div>
        <div class="val">
          <Select v-model="firstModule" style="width: 130px">
            <Option
              v-for="(val, key, index) in filterLists[sysCode].children"
              :value="key"
              :key="index"
              >{{ val.name }}</Option
            >
          </Select>
        </div>
      </div>
      <div class="item">
        <div class="label">二级模块：</div>
        <div class="val">
          <Select v-model="secondModule" style="width: 130px">
            <Option
              v-for="(val, key, index) in filterLists[sysCode].children[firstModule].children"
              :value="key"
              :key="index"
              >{{ val.name }}</Option
            >
          </Select>
        </div>
      </div>
      <div class="item">
        <div class="label">操作：</div>
        <div class="val">
          <Select v-model="operate" style="width: 130px">
            <Option
              v-for="(val, key, index) in operateList"
              :value="key"
              :key="index"
              >{{ val.name }}</Option
            >
          </Select>
        </div>
      </div> -->
      <div class="item">
        <span class="btn" @click.stop="getInfoCount()">查询</span>
        <span class="btn" @click.stop="exportInfos()">导出</span>
      </div>
    </div>
    <XZTable v-if="!loading && total > 0" :rules="rules" :listData="listData" />
    <Page
      :total="total"
      size="small"
      :current="pageNo"
      :page-size="pageSize"
      @on-change="pageChange"
      v-if="!loading && total > 0"
    />
    <nodata-page
      :classVal="'nodata2'"
      v-if="!loading && total == 0"
    ></nodata-page>
    <Spin v-if="loading">
      <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
  </div>
</template>

<script>
import XZTable from "../componetns/XZTable";
import moment from "moment";
import filterList from "./filter";
import NodataPage from "../../components/nodataPage.vue";
const rules = {
  maxColumn: 10,
  headList: [
    { text: "序号", key: "xh" },
    { text: "姓名", key: "userName" },
    { text: "机构", key: "organName" },
    { text: "时间", key: "publishTime" },
    { text: "系统", key: "sysCode" },
    { text: "一级模块", key: "module1" },
    { text: "二级模块", key: "module2" },
    { text: "操作", key: "mcontent"},
    { text: "操作内容", key: "remark" },
    { text: "登录IP", key: "mip" },
    { text: "登录浏览器", key: "browser" },
  ],
};
export default {
  data() {
    return {
      moment,
      listData: [],
      rules,
      total: 0,
      filterLists: filterList.sysCodeList,
      operateList: [],
      sysCode: "全部",
      firstModule: "全部",
      secondModule: "全部",
      modules: "全部",
      operate: "全部",
      organName: "全部",
      account: "",
      timer: ["", ""],
      pageNo: 1,
      pageSize: 20,
      loading: true,
      exportUrl :'',
      exportParam:{},
      keyWord: ''
    };
  },
  watch: {
    sysCode(val) {
      this.firstModule = "全部";
    },
    firstModule(val) {
      this.secondModule = "全部";
      if (['热搜榜单','舆情事件','舆情提示单'].includes(this.firstModule) && this.sysCode == 'trs_sdt') {
        this.operateList = this.filterLists[this.sysCode].children[this.firstModule].children['全部'].children;
      }
      
    },
    secondModule(val) {
      this.operate = "全部";
      this.operateList = this.filterLists[this.sysCode].children[this.firstModule].children[val].children;
    }
  },
  components: { XZTable, NodataPage },
  created() {},
  mounted() {
    this.getInfoCount();
    this.operateList = this.filterLists[this.sysCode].children[this.firstModule].children[this.secondModule].children;
    
  },
  methods: {
    getTimer(data) {
      if (data[1].slice(-8) === "00:00:00") {
        data[1] = data[1].slice(0, -8) + "23:59:59";
        this.timer = data;
      }
    },
    pageChange(index) {
      this.pageNo = index;
      this.getInfoList();
    },
    reset() {
      this.loading = true;
      this.pageNo = 1;
      this.listData = [];
      this.total = 0;
    },
    getParams() {
      // console.log(this.timer);
      let params = {
        account: this.account,
        keyWord: this.keyWord,
        // content: this.operate,
        endTime: this.timer[1]
          ? moment(this.timer[1]).format("yyyy-MM-DD HH:mm:ss")
          : "",
        startTime: this.timer[0]
          ? moment(this.timer[0]).format("yyyy-MM-DD HH:mm:ss")
          : "",
        // firstModule: this.firstModule,
        // secondModule: this.secondModule,
        organName: this.organName,
        sysCode: this.sysCode,
      };
      Object.keys(params).forEach((i) => {
        if (params[i] == "全部") {
          params[i] = "";
        }
      });
      return params;
    },
    getInfoCount() {
      this.reset();
      this.exportUrl = '';
      let params = this.getParams();
      this.$http
        .get(gl.serverURL + "/behavior/count", { params: params })
        .then((res) => {
          let data = res.body;
          this.exportUrl = '/behavior/exportList';
          this.exportParam = params;
          if (data.data > 0) {
            this.total = data.data;
            this.getInfoList();
          } else {
            this.loading = false;
          }
        });
    },
    getInfoList() {
      this.loading = true;
      let params = this.getParams();
      params.pageNo = this.pageNo;
      params.pageSize = this.pageSize;
      this.$http
        .get(gl.serverURL + "/behavior/list", { params: params })
        .then((res) => {
          let data = res.body;
          this.listData = data.data;
          this.loading = false;
          this.listData.forEach((i, index) => {
            i.xh = (this.pageNo - 1) * this.pageSize + index + 1;
            i.module =
              i.module1 +
              (i.module2 ? "-" + i.module2 : "") +
              (i.module3 ? "-" + i.module3 : "");
          });
        })
        .catch((err) => {
          this.$Message.error("服务器出错");
          this.loading = false;
        });
    },
    exportInfos(){
      if (!this.exportUrl){
        this.$Message.error("请先点击查询");
        return ;
      }
      this.exportUtil.exportPost(gl.serverURL+this.exportUrl,this.exportParam);
    }
  },
};
</script>

<style scoped lang="less">
.theLog {
  width: 1720px;
  margin: 30px auto;
  // height: calc(~"100vh - 240px");
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  position: relative;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    .item {
      display: flex;
      align-items: center;
      .label {
      }
      .val {
      }
    }
    .btn {
      display: inline-block;
      text-align: right;
      color: #fff;
      font-weight: 600;
      cursor: pointer;
      background-color: #1c7ff1;
      padding: 5px 14px;
      border-radius: 10px;
      margin-right: 10px;
    }
  }
  /deep/.ivu-page {
    position: absolute;
    bottom: 20px;
    width: 100%;
    text-align: center;
  }
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}
/deep/ .ivu-spin {
  margin: 100px 0;
}
</style>
