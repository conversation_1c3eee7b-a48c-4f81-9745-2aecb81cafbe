<template>
  <div class="bepartentFrame">
    <div class="bepartentHeader">
      <div class="headerTitle">
        <svg-icon
          icon-class="门户系统logo"
          style="height: 50px; width: 400px"
        />
      </div>
      <div class="user">
        <span class="userName"> {{ userName }} </span>
        <svg-icon
          icon-class="头部-退出登录"
          style="height: 16px; margin-top: 8px"
          @click.native="out"
        />
      </div>
    </div>
    <XzNav :list="navList" :navHeight="60" />
    <router-view style="width: 1720px; margin: 30px auto"></router-view>
  </div>
</template>
  
<script>
import XzNav from "@/components/XzNav";
const navList = [
  {
    title: "机构配置",
    icon: "导航-机构配置",
    path: "/SystemConfiguration/mechanism",
    permission: "/sys/organ",
  },
  {
    title: "角色权限配置",
    icon: "导航-角色权限配置",
    path: "/SystemConfiguration/role",
    permission: "/sys/power",
  },
  {
    title: "岗位配置",
    icon: "岗位配置",
    path: "/SystemConfiguration/station",
    permission: "/config/station",
  },
  {
    title: "行为审计",
    icon: "行为审计",
    path: "/SystemConfiguration/behaviorAuditing",
    permission: "/behavior",
  },
  {
    title: "意见反馈",
    icon: "意见反馈",
    path: "/SystemConfiguration/feedBack",
    permission: "/behavior",
  },
];
export default {
  name: "",
  //import 引入组件
  components: { XzNav },
  data() {
    return {
      userName: "",
      navList,
    };
  },
  methods: {
    toPath(data) {
      // console.log(data);
      this.$router.push(data);
    },
    out() {
      sessionStorage.removeItem("userName");
      this.userName = "";
      this.$router.push("/home");
    },
  },
  mounted() {
    // console.log(this.$route);
    this.userName = sessionStorage.getItem("userName");
  },
};
</script>
  
<style lang='less' scoped>
.bepartentFrame {
  margin: 0 auto;
  height: 100%;

  .bepartentHeader {
    background-color: #044dba;
    height: 50px;
    padding: 0 100px;
    display: flex;
    justify-content: space-between;
    .headerTitle {
      font-weight: 600;
      color: #fff;
    }
    .user {
      margin-top: 10px;
      min-width: 100px;

      .userName {
        color: #fff;
        line-height: 32px;
        margin-left: 10px;
      }
    }
  }
  // .navFrame {
  //   height: 110px;
  //   background-color: #eee;
  //   padding: 10px 100px;

  //   .navTitle {
  //     font-size: 16px;
  //     color: #000;
  //   }
  //   .navContent {
  //     .navItem {
  //       color: #606266;
  //       writing-mode: horizontal-tb;
  //       display: inline-flex;
  //       white-space: nowrap;
  //       margin-right: 28px;
  //       margin-bottom: 5px;
  //       font-size: 14px;
  //       cursor: pointer;
  //       width: 100px;
  //       float: left;
  //       text-align: center;
  //     }
  //   }
  // }
}
</style>