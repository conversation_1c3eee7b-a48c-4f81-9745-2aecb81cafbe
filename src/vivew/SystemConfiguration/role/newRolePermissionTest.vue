<template>
	<div class="content">
		<!--标签页-->
		<div class="tabs">
			<Tabs type="card" @on-click="switchTab" >
        <TabPane :label="tab.roleName" v-for="(tab, index) in roleList" :key="index" >
					<Icon type="ios-close" v-on:click="deleterole(tab.roleId)" />
				</TabPane>
			   <div class="insert" v-on:click="showInsert" slot="extra">
			     <Icon type="md-add" size="50px"/>
		     </div>
      </Tabs>
		</div>
   
		<!--标签页内容-->
		<div class="shadow tab">
			<div class="left">
				<div class="pad10">该权限所属账号列表：</div>
				<div style="height: 458px; border: 1px solid #d7dde4;">
					<EasyScrollbar :barOption="myBarOption" ref="scrollVue">
						<div class="border">
              <Spin fix size="large" v-show="accountLoading"></Spin>
							<div v-if="!isListNull" class="list">
								<div class="list-item" v-for="(item, index) of accountList" :key="index">
									<div class="pr10" :class="index%2!=0 ? 'bgc':''" :title="item.organName+'-'+item.userName">
										<span style="padding-left: 10px;">{{item.organName | getOrganName}}-{{item.userName}}</span>
									</div>
								</div>
							</div>
							<div v-else class="list null">
		        		<nodata-page :classVal="'nodata1'"></nodata-page>
		        	</div>
						</div>
					</EasyScrollbar>
				</div>
			</div>
			<div class="right">
				<div class="pad10">功能权限：</div>
				<!-- <div class="image" v-if="roleImgPath">
					<Spin fix size="large" v-show="imgLoading"></Spin>
					<img v-show="roleImgPath" :src="ftpImgPath+roleImgPath"/>
				</div> -->
				<Tree :data="permissionData" v-if="permissionData" ref="tree" show-checkbox></Tree>
				<div v-else class="image datanull">
					<nodata-page :classVal="'nodata4'"></nodata-page>
				</div>
				<!-- 暂时去除图片上传功能 -->
				<!-- <div class="upload">
					<Upload name="file" :max-size="10240" :on-exceeded-size="handleMaxSize"
                  :show-upload-list="false" :action="uploadURL +''+ activeTab"
                  :format="['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']"
                  :on-success="handleSuccess" :on-error="upLoadError"
                  :on-format-error="handleFormatError">
			        <Button icon="ios-cloud-upload-outline">上传图片</Button>
			    </Upload>
				</div> -->
				<div>
					 <Button type="primary" @click="savePower()">保存</Button>
				</div>
			</div>
		</div>
		<Modal
        v-model="showInsertModel"
        title="增加角色"
				draggable 
        @on-ok="insertRole"
        @on-cancel="goback">

        <p style="float: left; margin-left: 80px; margin-top: 2px;">请输入角色名</p>
				<input class="insertMo" v-model="roleName"  type="text"/>
    </Modal>
	</div>
	
</template>

<script>
import NodataPage from '@/components/common/nodata/nodataPage.vue';
export default{
	data(){
		return {
			roleName:"",
      organId: 1,
      hasSuperPerm: false, //是否拥有超管权限
      showInsertModel:false,
			//顶部按钮相关属性
			activeTab: 1,//选中的分组的序号
			accountLoading: false,
			isListNull: false,
			accountList: [],
			roleList: [],
			roleImgPath: '',
      imgLoading: false,
      ftpImgPath: gl.ftpRoleImgURL,
      uploadURL: gl.serverURL + '/sysmng/rolesys/role/uploadImage?roleId=',
      myBarOption:{
        barWidth:4,           //滚动条宽度
        zIndex:"auto",        //滚动条z-Index
        railColor:"#eee",     //导轨颜色
        barMarginRight:0,     //垂直滚动条距离整个容器右侧距离单位（px）
        barMaginBottom:0,     //水平滚动条距离底部距离单位（px)
        barOpacityMin:0.3,      //滚动条非激活状态下的透明度
        barColor:"#959595",   //滚动条颜色
        autohidemode:true,     //自动隐藏模式
        horizrailenabled:true,//是否显示水平滚动条
			},
			permissionData:[],
			// checkPowerArr:[],//勾选的权限
			// permissionData: [{"title":"属地态势","expand":false,"checked":true , "children":null},{"title":"信息预警","expand":false,"children":[{"title":"本地人物","expand":false,"children":[{"title":"删除","expand":false,"children":null},{"title":"人物列表","expand":false,"children":null}]},{"title":"本地敏感","expand":false,"children":[{"title":"删除","expand":false,"children":null},{"title":"敏感列表","expand":false,"children":null}]},{"title":"本地热点","expand":false,"children":[{"title":"删除","expand":false,"children":null},{"title":"本地推荐","expand":false,"children":null}]},{"title":"专项信息","expand":false,"children":null}]},{"title":"事件分析","expand":false,"children":[{"title":"事件监测","expand":false,"children":[{"title":"列表","expand":false,"children":null}]},{"title":"事件管理","expand":false,"children":[{"title":"列表","expand":false,"children":null}]}]},{"title":"综合搜索","expand":false,"children":null},{"title":"配置管理","expand":false,"children":[{"title":"机构配置","expand":false,"children":[{"title":"机构列表","expand":false,"children":null},{"title":"部门列表","expand":false,"children":null}]},{"title":"分类配置","expand":false,"children":null},{"title":"预警配置","expand":false,"children":[{"title":"修改自动推荐","expand":false,"children":null}]},{"title":"属地配置","expand":false,"children":null},{"title":"地方预警配置","expand":false,"children":null},{"title":"地方属地配置","expand":false,"children":null}]},{"title":"收藏","expand":false,"children":[{"title":"信息列表","expand":false,"children":null}]},{"title":"系统管理","expand":false,"children":[{"title":"系统日志管理","expand":false,"children":[{"title":"操作日志","expand":false,"children":null},{"title":"资源监测","expand":false,"children":null},{"title":"系统使用分析","expand":false,"children":null}]},{"title":"用户反馈","expand":false,"children":null},{"title":"系统更新日志","expand":false,"children":null}]}]
		}
	},
	methods:{
		// handleTabRemove(name){
		// 	alert(name)
		// 	this[name]=true
		// },
		deleterole(roleId){
			if(this.accountList.length===0){
		    let getUrl = gl.serverURL + '/sysmng/rolesys/role/deleRole';
					let params = {
        roleId: roleId
      };
			this.$http.get(getUrl, {params:params}).then( resp => {
				let ret = resp.body;
				let status = ret.status;
				if(status == 0){
				 this.getRoleList();
				// this.activeTab=this.roleList[0].roleId;
				 //this.switchTab('0');
				}
			}, resp => {
				this.$Message.error({content: '删除角色异常', duration: 3, closable: true});
			});
			}else{
			}
			
		},
		insertRole(){
			this.showInsertModel=false;
			for(let i=0;i<this.roleList.length;i++){
        if(this.roleName===this.roleList[i].roleName){
						this.$Message.error({content: '已有该名称的角色', duration: 3, closable: true});
					return
				}
			}
				let getUrl = gl.serverURL + '/sysmng/rolesys/role/insertRole';
					let params = {
        roleName: this.roleName
      };
			this.$http.get(getUrl, {params:params}).then( resp => {
				let ret = resp.body;
				let status = ret.status;
				if(status == 0){
         this.getRoleList()
				}
			}, resp => {
				this.$Message.error({content: '添加角色异常', duration: 3, closable: true});
			});
		},
		goback(){
			this.showInsertModel=false;
		},
		showInsert(){
			this.showInsertModel=true;
		},
		/*角色列表*/
		getRoleList(){
			let getUrl = gl.serverURL + '/sysmng/rolesys/role/list';
			this.$http.get(getUrl).then( resp => {
				let ret = resp.body;
				let status = ret.status;
				if(status == 0){
          this.roleList = ret.data;
          this.roleList.forEach((role, index)=>{
            role.index = index;
					})
					if(this.activeTab==1){
					this.activeTab = this.roleList.length > 0 ? this.roleList[0].roleId : 1;
					}
					if(this.roleList.length > 0){
						this.getAccountList();
						this.getAllPowers();//获取权限列表
					}
				}
			}, resp => {
				this.$Message.error({content: '获取角色列表时出现异常！！', duration: 3, closable: true});
			});
		},
		//获取权限列表
		getAllPowers(){
			debugger
			let getUrl = gl.serverURL + '/sysmng/rolesys/role/getAllPermission';
			let queryParams = {
        roleId: this.activeTab
      };
			this.$http.get(getUrl, {params: queryParams}).then(response =>{
				debugger
				let result = response.body;
				let status = result.status;
				if(status == 0){
					this.permissionData = result.data;
				}
			},response => {
				this.$Message.error({content: '获取权限列表出现异常！！', duration: 3, closable: true});
			});

		},
		//保存角色对应勾选的权限
		savePower(){
			debugger
			let url = gl.serverURL + '/sysmng/rolesys/role/savePermission';
			let checkPowerArr=this.$refs.tree.getCheckedNodes();//获取选中的节点
			let powerIds = '';
			if(checkPowerArr){
				for(let i = 0 ; i < checkPowerArr.length ; i++){
					powerIds += checkPowerArr[i].powerId +",";
				}
				let queryParams = {
					roleId: this.activeTab,
					powerIds:powerIds
				};
				this.$http.get(url,{params: queryParams}).then(response => {
					debugger
					let resule = response.body;
					let status = resule.status;
					if(status == 0){
						this.$Message.success({content: '保存成功！',duration: 3,closable:true});
					}
				},response => {
					this.$Message.error({content: '保存失败！', duration: 3, closable: true});
				})
			}
			
		},
		
		/*切换角色标签页，参数name为索引值，默认从0开始*/
		switchTab(index){
      for(let i=0; i<this.roleList.length; i++){
        if(index === this.roleList[i].index){
          this.activeTab = this.roleList[i].roleId;
          break;
        }
      }
			this.getAccountList();
			//获取权限树
			this.getAllPowers();
		},
		/*获取当前角色所属账号列表*/
		getAccountList(){
			this.imgLoading = true;
			this.accountLoading = true;
			let url = gl.serverURL + '/sysmng/rolesys/role/account';
			let queryParams = {
        roleId: this.activeTab
      };
			this.accountList = [];
			this.$http.get(url, {params: queryParams}).then( resp => {
				let ret = resp.body;
				let status = ret.status;
				this.imgLoading = false;
				this.accountLoading = false;
				if(status == 0){
					this.roleImgPath = ret.data.url;
					this.accountList = ret.data.list;
					this.isListNull = this.accountList.length > 0 ? false : true;
				}else{
					this.$Message.error({content: '获取当前权限所属账号列表失败！！', duration: 3, closable: true});
				}
			}, resp => {
				this.$Message.error({content: '获取当前权限所属账号列表时出现异常！！', duration: 3, closable: true});
			}).finally(()=>{
				//切换角色后，控制列表滚动条的显示
				this.$nextTick(()=>{
					this.$refs.scrollVue.update();
				})
			});
		},
		/*上传成功后更新图片*/
		getPermissionImg(){
			this.imgLoading = true;
			let imgURL = gl.serverURL + '/sysmng/rolesys/role/img';
			this.$http.get(imgURL, {params: {roleId: this.activeTab}}).then( resp => {
				let ret = resp.body;
				let status = ret.status;
				this.imgLoading = false;
				if(status == 0){
					this.roleImgPath = ret.data;
				}
			});
		},
		/*文件上传成功是的钩子*/
		handleSuccess(event, file, fileList){
			this.getPermissionImg();
			this.$Message.success({content: '上传成功！', duration: 3, closable: true});
		},
		/*文件上传失败是的钩子*/
		upLoadError(error, file, fileList){
			this.$Message.error({content: '上传失败！', duration: 3, closable: true});
		},
		/*文件格式验证失败时的钩子*/
    handleFormatError (file) {
      this.$Message.error({content: '您上传的文件格式有误', duration: 5, closable: true});
    },
    /*文件大小超限时的钩子*/
    handleMaxSize (file) {
      this.$Message.error({content: '接受的文件最大不能超过10M ！', duration: 5, closable: true});
    },
  },
  filters: {
    getOrganName(depName){
      return depName.indexOf('-') == -1 ? depName : depName.split('-')[0];
    }
  },
	components:{
		NodataPage
  },
  beforeMount() {
    this.organId = sessionStorage.getItem("organId");
    this.hasSuperPerm = this.hasPermission("/super"); //是否拥有超级权限
  },
	mounted(){
		gl.role = this;
		this.getRoleList();
	}
}
</script>

<style lang="less" scoped>
  .insertMo{
		    float: right;
    width: 200px;
    margin-right: 80px;
	}
	.tab{
		display: flex;
		cursor: default;
		min-height: 420px;
		padding: 20px 20px;
		flex-direction: row;
		background-color: white;

		.left{
			width: 15%;

			.border{
				height: 458px;
        position: relative;

				.list{
          //width: 100%;
					padding: 3px 0px;
					display:inline-block;

					&.null{
						// left: 21%;
            // padding-top: 50%;
            width:100%;
            position: absolute;
            left:50%;
            top:50%;
            transform: translate(-50%,-50%);
					}

					.list-item{
						height: 30px;
						display: flex;
						align-items: center;
						white-space: nowrap;

						.bgc{
							flex:1;
							height: 30px;
							display: flex;
							align-items: center;
							// background: #e8eaf4;
						}
					}
				}
			}
		}

		.pr10{
			padding-right: 10px;
		}

		.pad10{
			padding-bottom: 10px ;
		}

		.right{
			width: 85%;
			display: flex;
			padding: 0px 40px;
			flex-direction: column;

			.image{
				/*width: 100%;
				min-height: 460px;*/
				position: relative;
				margin-bottom: 15px;

				&.datanull{
					padding-top: 5%;
					/*border: 1px solid #d7dde4;*/
				}

				/*img{
					width: 100%;
				}*/
			}

			.upload{
				display: flex;
				justify-content: flex-end;
			}
		}
	}
</style>
<style lang="less">
	.tabs{
		.ivu-tabs-bar{
			margin-bottom: 0px;
		}

		.ivu-tabs-tab-focused{
			border-top: 2px solid #3172EB !important;
		}
	}
</style>
