<template>
  <div class="content spacing">
    <!--标签页-->
    <div class="tabs">
      <Tabs type="card" v-model="choose" @on-click="switchTab">
        <TabPane
          v-for="item in roleList"
          :name="item.roleName"
          :key="item.roleId"
          :label="item.roleName"
        >
        </TabPane>
        <div class="insert" @click="showDeatils" slot="extra"  v-if="hasPermission('/sys/power')">
          <i class="icon1"></i>角色管理
        </div>
      </Tabs>
    </div>

    <!--标签页内容-->
    <div class="shadow tab">
      <div class="left">
        <div class="pad10">该权限所属账号列表：</div>
        <div style="height: 458px; border: 1px solid #d7dde4">
          <EasyScrollbar :barOption="myBarOption" ref="scrollVue">
            <div class="border">
              <Spin fix size="large" v-show="accountLoading"></Spin>
              <div v-if="!isListNull" class="list">
                <div
                  class="list-item"
                  v-for="(item, index) of accountList"
                  :key="index"
                >
                  <div
                    v-if="item != null"
                    class="pr10"
                    :class="index % 2 != 0 ? 'bgc' : ''"
                    :title="item.userAccount"
                  >
                    <span style="padding-left: 10px">{{
                      item.userAccount
                    }}</span>
                  </div>
                </div>
              </div>
              <div v-else class="list null">
                <nodata-page :classVal="'nodata1'"></nodata-page>
              </div>
            </div>
          </EasyScrollbar>
        </div>
      </div>
      <div class="right">
        <div class="pad10">功能权限：</div>
        <!-- <div class="image" v-if="roleImgPath">
					<Spin fix size="large" v-show="imgLoading"></Spin>
					<img v-show="roleImgPath" :src="ftpImgPath+roleImgPath"/>
				</div> -->
        <spin v-if="permissionLoading" style="margin-left: 18px"></spin>
        <!-- <CustomTree
          :data="permissionData"
          v-if="!permissionLoading"
          show-checkbox
          ref="tree"
        ></CustomTree> -->
        <Tree
          :data="permissionData"
          v-if="!permissionLoading"
          show-checkbox
          ref="tree"
        ></Tree>
        <!-- <div v-else class="image datanull">
					<nodata-page :classVal="'nodata4'"></nodata-page>
				</div> -->
        <!-- 暂时去除图片上传功能 -->
        <!-- <div class="upload">
					<Upload name="file" :max-size="10240" :on-exceeded-size="handleMaxSize"
                  :show-upload-list="false" :action="uploadURL +''+ activeTab"
                  :format="['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']"
                  :on-success="handleSuccess" :on-error="upLoadError"
                  :on-format-error="handleFormatError">
			        <Button icon="ios-cloud-upload-outline">上传图片</Button>
			    </Upload>
				</div> -->
        <div style="margin-left: 5px; margin-top: 8px" v-if="hasPermission('/sys/power/roleRel')">
          <Button type="primary" @click="savePower()">保存</Button>
        </div>
      </div>
    </div>
    <Modal
      v-model="showDeatil"
      title="角色管理"
      width="450px"
      @on-ok="insertRole"
      @on-cancel="goback"
    >
      <!-- <div class="header">
					角色管理
				</div>
		 -->
      <div class="showRoledetail" v-for="(p, index) in upRoleList" :key="index">
        <div class="spanclas">
          <span>{{ index + 1 }}</span>
        </div>
        <div class="insertMo">
          <span
            :title="p.roleName"
            v-if="editRole == p.roleId ? false : true"
            >{{ p.roleName }}</span
          >
          <input v-else v-model="p.roleName" type="text" />
        </div>
        <div class="clickclss" v-if="editRole == p.roleId ? false : true">
          <i class="edit" v-on:click="editRoles(p)" style="cursor: pointer"  v-if="hasPermission('/sys/power/roleUpdate')"></i>
          <i
            class="deleAll"
            v-on:click="isDelete(p)"
            style="cursor: pointer"
            v-if="hasPermission('/sys/power/roleDel')"
          ></i>
          <!-- <Icon type="ios-trash-outline" size="20" v-on:click="isDelete(p)" /> -->
        </div>
        <div class="clickclss" v-else>
          <div
            style="float: left; font-size: 14px; cursor: pointer"
            class="tt"
            v-on:click="backRole()"
          >
            取消
          </div>
          <div
            style="float: right; font-size: 14px; cursor: pointer"
            class="pp"
            v-on:click="updateRoles(p)"
          >
            保存
          </div>
        </div>
      </div>
      <div class="centerpic" v-if="hasPermission('/sys/power/roleAdd')">
        <div>
          <i class="icon2" v-on:click="addRoles()" style="cursor: pointer"></i>
        </div>
        <!-- <Icon type="md-add-circle" size="24" v-on:click="addRoles()" /> -->
      </div>
      <div slot="footer">
        <Button size="large" long @click="goback">关闭</Button>
      </div>
    </Modal>
    <Modal
      v-model="deleteOk"
      title="删除确认"
      @on-ok="deleaccount"
      @on-cancel="noDelete"
    >
      <p>确认删除{{ this.role.roleName }}角色吗</p>
    </Modal>

    <confirm ref="myConfirm1" @userBehavior="deleaccount"></confirm>
  </div>
</template>

<script>
import NodataPage from "../components/nodataPage.vue";
import Confirm from "../components/Message.vue";
import CustomTree from "../mechanism/tree";

export default {
  data() {
    return {
      choose: "",
      role: { roleId: "", roleName: "" },
      deleAccoList: [],
      roleName: "",
      organId: 1,
      editRole: -1,
      deleteOk: false,
      showDeatil: false,
      hasSuperPerm: false, //是否拥有超管权限
      showInsertModel: false,
      //顶部按钮相关属性
      activeTab: 1, //选中的分组的序号
      activeName: "",
      accountLoading: false,
      isListNull: false,
      accountList: [],
      roleList: [],
      upRoleList: [],
      roleImgPath: "",
      imgLoading: false,
      ftpImgPath: gl.ftpRoleImgURL,
      uploadURL: gl.serverURL + "/sysmng/rolesys/role/uploadImage?roleId=",
      myBarOption: {
        barWidth: 4, //滚动条宽度
        zIndex: "auto", //滚动条z-Index
        railColor: "#eee", //导轨颜色
        barMarginRight: 0, //垂直滚动条距离整个容器右侧距离单位（px）
        barMaginBottom: 0, //水平滚动条距离底部距离单位（px)
        barOpacityMin: 0.3, //滚动条非激活状态下的透明度
        barColor: "#959595", //滚动条颜色
        autohidemode: true, //自动隐藏模式
        horizrailenabled: true, //是否显示水平滚动条
      },
      permissionData: [],
      permissionLoading: true,
      selectedNode: [],
    };
  },
  methods: {
    noDelete() {
      this.deleteOk = false;
    },
    isDelete(role) {
      /* this.deleteOk=true */
      this.role.roleId = role.roleId;
      this.role.roleName = role.roleName;

      this.$refs.myConfirm1.show("确认删除", {
        type: "confirm",
        confirmText: "确定",
        cancelText: "取消",
        roleName: '“'+this.role.roleName+'”',
        titleText: "删除确认",
        data: null,
      });
    },
    addRoles() {
      if (this.editRole == -1) {
        let count = this.upRoleList.length;
        if (this.upRoleList[count - 1].roleId == "") {
          this.$Message.error({
            content: "请先保存上一个角色",
            duration: 3,
            closable: true,
          });
          return;
        }
        let role = { index: count, roleId: "", roleName: "" };
        this.upRoleList.push(role);
        this.editRole = role.roleId;
      } else {
        this.$Message.error({
          content: "请先保存或取消修改的角色",
          duration: 3,
          closable: true,
        });
        return;
      }
      // }else{

      // }
    },
    //修改或增加角色
    updateRoles(role) {
      let count = 0;
      for (let i = 0; i < this.upRoleList.length; i++) {
        if (role.roleName === this.upRoleList[i].roleName) {
          count = count + 1;
          if (count > 1) {
            this.$Message.error({
              content: "已有该名称的角色",
              duration: 3,
              closable: true,
            });
            return;
          }
        }
      }
      if (role.roleName == "") {
        this.$Message.error({
          content: "角色名不能为空",
          duration: 3,
          closable: true,
        });
        return;
      }
      if (role.roleId == "") {
        let getUrl = gl.serverURL + "/sys/power/roleAdd";
        let params = {
          roleName: role.roleName,
        };
        this.$http.post(getUrl, params).then(
          (resp) => {
            let ret = resp.body;
            let status = ret.status;
            if (status == 0) {
              this.$Message.success({
              content: ret.message,
              duration: 3,
              closable: true,
            });
              this.getRoleList();
              // this.showDeatils()
            }
          },
          (resp) => {
            this.$Message.error({
              content: "添加角色异常",
              duration: 3,
              closable: true,
            });
          }
        );
      } else {
        let getUrl = gl.serverURL + "/sys/power/roleUpdate";
        let params = {
          roleId: role.roleId,
          roleName: role.roleName,
        };
        this.$http.post(getUrl, params).then(
          (resp) => {
            let ret = resp.body;
            let status = ret.status;
            if (status == 0) {
              this.$Message.success({
                content: "修改角色成功",
                duration: 3,
                closable: true,
              });
            }
          },
          (resp) => {
            this.$Message.error({
              content: "删除角色异常",
              duration: 3,
              closable: true,
            });
          }
        );
      }
      this.editRole = -1;
    },
    backRole() {
      this.editRole = -1;
      this.getRoleList();
    },
    editRoles(role) {
      if (this.editRole == -1) {
        this.editRole = role.roleId;
      } else {
        this.$Message.error({
          content: "请先保存或取消修改的角色",
          duration: 3,
          closable: true,
        });
      }
    },
    showDeatils() {
      this.showDeatil = true;
      this.editRole = -1;
      this.upRoleList = [];
      let list = this.roleList;
      //  for(let i=0;i<list.length;i++){

      // 	 this.upRoleList.push(list[i]);
      //  }
      this.getRoleList();
      //this.upRoleList=list
    },
    deleaccount() {
      let url = gl.serverURL + "/sys/power/roleUser?roleId=" + this.role.roleId;
      let queryParams = {
        roleId: this.role.roleId,
      };
      // console.log(queryParams);
      this.deleAccoList = [];
      this.$http.get(url).then(
        (resp) => {
          let ret = resp.body;
          let status = ret.status;
          if (status == 0) {
            this.deleAccoList = ret.data;
            // console.log(this.deleAccoList);
            this.getRoleList();
            this.deleterole();
          } else {
            this.$Message.error({
              content: "获取权限所属账号列表失败！！",
              duration: 3,
              closable: true,
            });
          }
        },
        (resp) => {
          this.$Message.error({
            content: "获取权限所属账号列表时出现异常！！",
            duration: 3,
            closable: true,
          });
        }
      );
    },
    deleterole() {
      // if (this.deleAccoList.length == 0) {
      let getUrl = gl.serverURL + "/sys/power/roleDel";
      let params = {
        roleId: this.role.roleId,
      };
      this.$http.post(getUrl, params).then(
        (resp) => {
          let ret = resp.body;
          let status = ret.status;
          if (status == 0) {
            this.deleteOk = false;
            this.$Message.success({
                content: ret.message,
                duration: 3,
                closable: true,
              });
            this.getRoleList();
            for (let i = 0; i < this.upRoleList.length; i++) {
              if (this.upRoleList[i].roleId == this.role.roleId) {
                this.upRoleList.splice(i, 1);
                if (i != 0) {
                  this.choose = this.upRoleList[0].roleName;
                  this.switchTab(this.upRoleList[0].roleName);
                } else {
                  this.choose = this.upRoleList[1].roleName;
                  this.switchTab(this.upRoleList[1].roleName);
                }
              }
            }
          }else{
             this.$Message.error({
                content: ret.message,
                duration: 3,
                closable: true,
              });   
          }
        },
        (resp) => {
          this.$Message.error({
            content: "删除角色异常",
            duration: 3,
            closable: true,
          });
        }
      );
      // } else {
      //   this.$Message.error({
      //     content: "请删除角色下账号再试",
      //     duration: 3,
      //     closable: true,
      //   });
      // }
    },
    insertRole() {
      this.showInsertModel = false;
      this.getRoleList();
    },
    goback() {
      this.showDeatil = false;
      this.getRoleList();
    },
    showInsert() {
      this.showInsertModel = true;
    },
    /*角色列表*/
    getRoleList() {
      let getUrl = gl.serverURL + "/sys/power/roleList";
      this.$http.get(getUrl).then(
        (resp) => {
          let ret = resp.body;
          let status = ret.status;
          if (status == 0) {
            this.roleList = JSON.parse(JSON.stringify(ret.data));
            this.editRole = -1;
            this.upRoleList = [];
            let list = ret.data;
            for (let i = 0; i < list.length; i++) {
              this.upRoleList.push(list[i]);
            }
            this.roleList.forEach((role, index) => {
              role.index = index;
            });
            if (this.activeTab == 1) {
              this.activeTab = this.roleList.length > 0 ? this.roleList[0].roleId : 1;
              this.activeName = this.roleList.length > 0 ? this.roleList[0].roleName : "1";
            }
            if (this.roleList.length > 0) {
              this.getAccountList();
              this.getAllPowers(); //获取权限列表
            }
          }
        },
        (resp) => {
          this.$Message.error({
            content: "获取角色列表时出现异常！！",
            duration: 3,
            closable: true,
          });
        }
      );
    },
    /*切换角色标签页，参数name为索引值，默认从0开始*/
    switchTab(index) {
      // console.log(index)
      for (let i = 0; i < this.roleList.length; i++) {
        if (index === this.roleList[i].roleName) {
          this.activeTab = this.roleList[i].roleId;
          this.activeName = this.roleList[i].roleName;
          break;
        }
      }
      this.getAccountList();
      //获取权限树
      this.getAllPowers();
    },
    /*获取当前角色所属账号列表*/
    getAccountList() {
      this.imgLoading = true;
      this.accountLoading = true;
      let url = gl.serverURL + "/sys/power/roleUser";
      let queryParams = {
        roleId: this.activeTab,
      };
      this.accountList = [];
      this.$http
        .get(url, { params: queryParams })
        .then(
          (resp) => {
            let ret = resp.body;
            let status = ret.status;
            this.imgLoading = false;
            this.accountLoading = false;
            if (status == 0) {
              // this.roleImgPath = ret.data.url;
              this.accountList = ret.data;
              this.isListNull = this.accountList.length > 0 ? false : true;
            } else {
              this.$Message.error({
                content: "获取当前权限所属账号列表失败！！",
                duration: 3,
                closable: true,
              });
            }
          },
          (resp) => {
            this.$Message.error({
              content: "获取当前权限所属账号列表时出现异常！！",
              duration: 3,
              closable: true,
            });
          }
        )
        .finally(() => {
          //切换角色后，控制列表滚动条的显示
          this.$nextTick(() => {
            this.$refs.scrollVue.update();
          });
        });
      this.getLog("角色权限配置", '查看角色/'+this.activeName);
    },

    //获取权限列表
    getAllPowers() {
      let getUrl = gl.serverURL + "/sys/power/tree";
      let queryParams = {
        roleId: this.activeTab,
      };
      this.$http.get(getUrl, { params: queryParams }).then(
        (response) => {
          let result = response.body;
          let status = result.status;
          if (status == 0) {
            this.permissionLoading = false;
            // this.permissionData = result.data;
            this.disposeTreeData(result.data);
          }
        },
        (response) => {
          this.$Message.error({
            content: "获取权限列表出现异常！！",
            duration: 3,
            closable: true,
          });
        }
      );
    },
    disposeTreeData(val) {
      const dispose = function (value) {
        value.forEach((i) => {
          if (i.children) {
            // console.log(dispose(i.children))
            i.checked = dispose(i.children);
          }
          if (!i.checked) {
            return i.checked;
          }else{
            return i.checked
          }
        });
      };
      dispose(val);
      this.permissionData = val;
    },
    familyTree(arr1, id) {
      var temp = [];
      var forFn = function (arr, id) {
        for (var i = 0; i < arr.length; i++) {
          var item = arr[i];
          if (item.powerId === id) {
            temp.push(item);
            forFn(arr1, item.parentId);
            break;
          } else {
            if (item.children) {
              forFn(item.children, id);
            }
          }
        }
      };
      forFn(arr1, id);
      return temp;
    },
    handleCheckedNodes(e) {
      return e;
    },
    //保存角色对应勾选的权限
    savePower() {
      let url = gl.serverURL + "/sys/power/roleRel";
      let checkPowerArr = this.$refs.tree.getCheckedNodes(); //获取选中的节点
      // console.log(checkPowerArr);
      // return false;
      let powerIds = [];
      let powerIdArr = [];
      if (checkPowerArr) {
        for (let i = 0; i < checkPowerArr.length; i++) {
          powerIdArr.push(checkPowerArr[i].powerId);
          powerIdArr.push(checkPowerArr[i].parentId);
        }
        let allArr = [];
        powerIdArr.map((i) => {
          let terr = this.familyTree(this.permissionData, i);
          allArr = [...allArr, ...terr];
        });
        allArr.forEach((i) => {
          // console.log(i);
          powerIds.push(i.powerId);
        });
        // console.log(powerIds);
        let queryParams = {
          roleId: this.activeTab,
          selectedPowerIds: [...new Set(powerIds)].toString(),
        };
        this.$http.post(url, queryParams).then(
          (response) => {
            let resule = response.body;
            let status = resule.status;
            if (status == 0) {
              this.$Message.success({
                content: "保存成功！",
                duration: 3,
                closable: true,
              });
            }
          },
          (response) => {
            this.$Message.error({
              content: "保存失败！",
              duration: 3,
              closable: true,
            });
          }
        );
      }
    },

    /*上传成功后更新图片*/
    getPermissionImg() {
      this.imgLoading = true;
      let imgURL = gl.serverURL + "/sysmng/rolesys/role/img";
      this.$http
        .get(imgURL, { params: { roleId: this.activeTab } })
        .then((resp) => {
          let ret = resp.body;
          let status = ret.status;
          this.imgLoading = false;
          if (status == 0) {
            this.roleImgPath = ret.data;
          }
        });
    },
    /*文件上传成功是的钩子*/
    handleSuccess(event, file, fileList) {
      this.getPermissionImg();
      this.$Message.success({
        content: "上传成功！",
        duration: 3,
        closable: true,
      });
    },
    /*文件上传失败是的钩子*/
    upLoadError(error, file, fileList) {
      this.$Message.error({
        content: "上传失败！",
        duration: 3,
        closable: true,
      });
    },
    /*文件格式验证失败时的钩子*/
    handleFormatError(file) {
      this.$Message.error({
        content: "您上传的文件格式有误",
        duration: 5,
        closable: true,
      });
    },
    /*文件大小超限时的钩子*/
    handleMaxSize(file) {
      this.$Message.error({
        content: "接受的文件最大不能超过10M ！",
        duration: 5,
        closable: true,
      });
    },
  },
  filters: {
    getOrganName(depName) {
      if (depName) {
        return depName.indexOf("-") == -1 ? depName : depName.split("-")[0];
      }
    },
  },
  components: {
    NodataPage,
    Confirm,
    CustomTree,
  },
  beforeMount() {
    this.organId = sessionStorage.getItem("organId");
    this.hasSuperPerm = this.hasPermission("/super"); //是否拥有超级权限
  },
  mounted() {
    gl.role = this;
    this.getRoleList();
  },
};
</script>

<style lang="less" scoped>
.tt:hover {
  color: rgb(21, 143, 41);
}
.pp:hover {
  color: rgb(21, 143, 41);
}
.ivu-modal-body {
  padding: 0%;
  font-size: 12px;
  line-height: 1.5;
}
.insert {
  cursor: pointer;
  width: 100px;
  margin-top: 7px;
  position: relative;
  text-align: center;
  margin-right: 20px;
}

.header {
  border-top: 3px solid #3172eb;
  padding-bottom: 10px;
  font-size: 16px;
  text-align: center;
  border-bottom: 1px solid #eee;
}
.spanclas {
  margin-top: 3px;
  width: 30px;
  border-right: 1px solid #eee;
  text-align: center;
  font-size: 14px;
}
.insertMo {
  color: rgb(143, 138, 138);
  flex-grow: 1;
  font-size: 14px;
  text-align: left;
  width: 170px;
  border-right: 1px solid #eee;
  margin-top: 3px;
  overflow: hidden;
  text-overflow: ellipsis;
  //margin-right: 80px;
}
.insertMo1 {
  font-size: 14px;
  text-align: center;
  margin-left: 50px;
  margin-right: 50px;
  width: 170px;
  border-right: 1px solid #eee;
  overflow: hidden;
  text-overflow: ellipsis;
}
.edit {
  background-image: url(../../../assets/img/edit_a.png);
  display: block;
  margin-top: 3px;
  margin-left: 16px;
  float: left;
  width: 14px;
  height: 15px;
  margin-right: 10px;
}
.deleAll {
  background-image: url(../../../assets/img/empty.png);
  display: block;
  margin-top: 3px;

  float: left;
  width: 14px;
  height: 15px;
  margin-right: 10px;
}
.icon2 {
  background-image: url(../../../assets/img/insert.png);
  width: 16px !important;
  height: 16px !important;
  padding-bottom: 3px;
  padding-right: 20px;
  top: 50%;
  left: -13px;
  height: 14px;
  font-size: 15px;
  // position: absolute;
  // transform:translate(-50%,-50%);
}
.icon1 {
  background-image: url(../../../assets/img/classify.png);
  width: 16px !important;
  top: 50%;
  left: 6px;
  height: 14px;
  position: absolute;
  transform: translate(-50%, -50%);
}
.centerpic {
  text-align: center;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  height: 40px;
}
.clickclss {
  width: 70px;
  float: right;
  text-align: center;
}
.showRoledetail {
  display: flex;
  width: 100%;
  border-bottom: 1px solid #eee;
  padding: 0px;
  height: 40px;
  align-items: center;
}

.tab {
  display: flex;
  cursor: default;
  min-height: 420px;
  padding: 20px 20px;
  flex-direction: row;
  background-color: white;

  .left {
    width: 15%;
    .border {
      height: 458px;
      position: relative;
      .list {
        //width: 100%;
        padding: 3px 0px;
        display: inline-block;
        &.null {
          // left: 21%;
          // padding-top: 50%;
          width: 100%;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }

        .list-item {
          height: 30px;
          display: flex;
          align-items: center;
          white-space: nowrap;

          .bgc {
            flex: 1;
            height: 30px;
            display: flex;
            align-items: center;
            // background: #e8eaf4;
          }
        }
      }
    }
  }

  .pr10 {
    padding-right: 10px;
  }

  .pad10 {
    padding-bottom: 10px;
  }

  .right {
    width: 85%;
    display: flex;
    padding: 0px 40px;
    flex-direction: column;

    .image {
      /*width: 100%;
				min-height: 460px;*/
      position: relative;
      margin-bottom: 15px;

      &.datanull {
        padding-top: 5%;
        /*border: 1px solid #d7dde4;*/
      }

      /*img{
					width: 100%;
				}*/
    }

    .upload {
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style lang="less">
.tabs {
  .ivu-tabs-bar {
    margin-bottom: 0px;
  }

  .ivu-tabs-tab-focused {
    border-top: 2px solid #3172eb !important;
  }
}
</style>
