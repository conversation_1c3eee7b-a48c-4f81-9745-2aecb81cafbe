<template>

  <div class="layout spacing">

    <header class="header">
      <div class="header-con">
        <!-- <div class="logo-box" v-if="organId==1">北京市互联网内容管理系统</div> -->
        <!-- <div class="logo-box">{{area}}互联网内容管理系统</div> -->
        <div class="logo-box">省级互联网内容管理系统</div>
      </div>
    </header>
    <div class="layout-content">
      <div class="top-tab-box">
        <div class="top-tab-con">
          <span class="top-tab-name">密码修改</span>
        </div>
      </div>
      <div class="shadow">
        <div class="event" style="padding: 30px 20px;height:100%;position: relative;">
          <div style='margin-bottom: 16px;min-height: 28px;'>
          	<span>用户名：</span>
          	<span>{{userName}}</span>
          </div>
          <div class="clear"></div> 
          <div style='margin-bottom: 16px;min-height: 28px;'>
          	<span>原密码：</span>
          	<input  v-model="oldpassword" type="password"  maxlength="18" 
          		style="margin-left:17px;" />
          </div>
          <div class="clear"></div> 
          <div style='margin-bottom: 16px;min-height: 28px;'>
          	<span>新密码：</span>
          	<input v-model="newpassword" type="password"  
          		@change="checkPassWord(newpassword)" style="margin-left:17px;"/>
            <span style="margin-left:10px;">(请输入6-18位密码,必须包含数字和字母)</span>
          </div>
          <div class="clear"></div> 
          <div style='margin-bottom: 16px;min-height: 28px;'>
          	<span>确认密码：</span>
          	<input v-model="confirmpassword" type="password" 
          		 @change="checkPassWord(confirmpassword)" style="margin-left:4px;"/>
          </div>
          <div class="clear"></div> 
        </div>
        <div class="footer">
          <span>
          	<Button type="ghost" style="margin-right: 20px;color:#000"
          	  @click='reset()'>重置</Button>
          </span>
          <span style="margin-left:20px;">
          	<Button type="primary" @click='confirmEdit()'>确定</Button></span>
        </div>
      </div>
      <div style="height: 50px;line-height:50px;font-size:14px;text-align: center;color: #909090;">
        	研制单位：中央网信办网络数据与技术局
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    data() {

    	return {
			oldpassword:'',
			newpassword:'',
			confirmpassword:'',
		};
    },
    computed:{
   		userName(){
	    	return sessionStorage.getItem("realName");
	  	},
      	area(){
        	return sessionStorage.getItem("area");
      	},
      	organId(){
        	return sessionStorage.getItem("organId");
      	}
    },
    watch: {},
    methods: {
			checkPassWord(password) {//密码必须包含数字和字母
					if(!this.oldpassword){
						this.$Message.error({content:"请先输入原密码!",duration:3,closable:true});
				    	return false;
					}
			    let str = password;
			    if (str == null || (str!=null && (str.length<6 || str.length>18))) {
			        this.$Message.error({content:"请输入6-18位密码,必须包含数字和字母!",duration:3,closable:true});
			    	return false;
			    }
			    var regHZ=new RegExp(/[\u4E00-\u9FA5]/);
			    if(regHZ.test(str)){
			    	this.$Message.error({content:"密码中不能包含汉字!",duration:3,closable:true});
			    	return false;
			    }
			    var reg = new RegExp(/^(?![^a-zA-Z]+$)(?!\D+$)/);
			    if (reg.test(str)){
			    	return true;
			    }else{
			    	this.$Message.error({content:"请输入6-18位密码,必须包含数字和字母!",duration:3,closable:true});
			    	return false;
			    }
			},
			//验证两个密码是否一致
			checkPassEqual(){
				if(this.newpassword!=null && this.confirmpassword!=null && this.newpassword!=this.confirmpassword){
					this.$Message.error({content:"确认密码与新密码不一致!",duration:3,closable:true});
					return false;
				}else{
					return true;
				}
			},
			reset(){
				this.oldpassword='';
				this.newpassword='';
				this.confirmpassword='';
			},
			//先比较新密码与确认密码一致再提交到后台，比较旧密码输入无误再修改用户密码,
			
			confirmEdit(){
				if(this.checkPassEqual()){
					if(this.checkPassWord(this.newpassword)){
						let params = {
							newpassword:this.newpassword,
							oldpassword:this.oldpassword
						};
						let url = gl.serverURL+"/login/editPassWord";
        		this.$http.post(url,{newpassword:params.newpassword.trim(),oldpassword:params.oldpassword.trim()},{emulateJSON:true}).then(response => {
        			 let result = response.body;
			          let status = result.status;
			          let content=result.message;
			          if(status == 0){
			          	this.$Message.success({content:content,duration:3,closable:true});
			          }else if(status == 102){
			          	 this.$Message.error({content:content,duration:3,closable:true});
			          }else if(status == 100){
			          	this.$Message.error({content:content,duration:3,closable:true});
			          } else{
			          	this.$Message.error({content:"密码修改操作失败，请联系系统管理员!",duration:3,closable:true});
			          }
        			}, response => {
        				this.$Message.error({content:"密码修改操作失败，请联系系统管理员!",duration:3,closable:true});
                	}
        		);
					}
				}
			},
		},
    mounted(){
		gl.passwordEdit = this;
	}
  }
</script>
<style scoped lang="less">
  .header{
    background:#3663B6;
  }
  .help-icon-box{
    position:relative;
    margin-right:50px;
  }
  .header-con {
    display:flex;
    width:94%;
		/*width: @width;*/
		/*min-width: @min-width;
		max-width: @max-width;*/
    margin: 0 auto;
    height:60px;
    flex-flow:row nowrap;
    align-items: center;
  }
  .logo-box{
    flex:none;
    font-size:24px;
    font-weight: bold;
    color:#fff;
  }


  .layout-content {
  	width:94%;
    /*width: @width;
    min-width: @min-width;
    max-width: @max-width;*/
    min-height: 300px;
    margin: 0 auto 20px;
    margin-top: 20px;
    background-color: transparent;
    overflow: inherit;
  }
  .top-tab-con{
    border-bottom:1px solid #d7dde4;
    margin-bottom:16px;
  }
  .top-tab-name{
    display: inline-block;
    position: relative;
    top:1px;
    height: 100%;
    padding: 8px 16px;
    margin-right: 16px;
    box-sizing: border-box;
    cursor: pointer;
    text-decoration: none;
    color: #429EFE;
    border-bottom: 2px solid #3172eb;
  }
  .footer {
    padding-bottom: 20px;
    text-align: left;
    width: 50%;
    margin-left:80px;
    line-height: 50px;
  }
</style>
