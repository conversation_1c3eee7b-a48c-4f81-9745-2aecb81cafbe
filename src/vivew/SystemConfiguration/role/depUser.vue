<template>
	<div class="content">
		<!-- 模态框 -->
		<div>
			<!-- 新建机构模态框 -->
			<Modal v-model="addOrganModal" :title="addTitle" @on-ok="addOrgan"
        :loading="addOrganLoading" @on-cancel="newOrganName = ''; addOrganModal=false;">
				{{!selectPId ? (addTitle === '新建机构' ? '机构' : '部门') : '部门'}}名称：
				<span v-if="selectPId || (selectPId==0 && addTitle==='新建一级部门')">
					<Input v-model="newOrganName" placeholder="请输入部门名称" style="width: 85%"/>
				</span>
				<span v-else>
					<Input v-model="newOrganName" placeholder="请输入机构名称" style="width: 85%"/>
				</span>
			</Modal>

			<!-- 修改部门构模态框 -->
			<Modal v-model="editDepModal" :title="editTitle" @on-ok="validateEditName"
        :loading="editDepLoading" @on-cancel="newDepName = ''; editDepModal=false;">
				{{!selectPId ? '机构' : '部门'}}名称：
				<span v-if="!selectPId">
					<Input v-model="newDepName" placeholder="请输入机构名称" style="width: 85%"/>
				</span>
				<span v-else>
					<Input v-model="newDepName" placeholder="请输入部门名称" style="width: 85%"/>
				</span>
			</Modal>

			<!-- 添加账号模态框 -->
			<Modal v-model="addAccountModal" title="新建账号" @on-ok="validateNull"
        :loading="addLoading" @on-cancel="newUserAccount = ''; addAccountModal=false;">
				<p class="pad10">
					<span>
            在<span :title="selectDepName" class="name ellipsis">{{selectDepName}}</span>下新建账号：
          </span>
				</p>
				<p class="pad10">用户名：
					<Input v-model="newUserAccount" placeholder="请输入用户名" style="width: 85%"/>
				</p>
			</Modal>

			<!-- 部门变更模态框 -->
			<Modal v-model="editModal" :mask-closable="false" :closable="false" title="部门变更">
				<div class="dep">
					<div class="up">
						将<span :title="selectedUserName" class="name">{{selectedUserName}}</span>所属部门变更为：
					</div>
					<div class="down">
						<Spin fix size="large" v-show="depModalLoading"></Spin>
						<Tree :data="depModalList" empty-text="''" @handle-organ="handleOrgan" @on-select-change="choseDep"></Tree>
					</div>
				</div>
				<div slot="footer">
          <Button size="large" @click="editModal=false; selectedUserName=''">取消</Button>
          <Button size="large" type="primary" @click="editConfirm()">确认</Button>
        </div>
			</Modal>

			<Modal v-model="confirmModal" title="部门变更确认" @on-ok="changeDep" @on-cancel="confirmModal = false;">
				<center>
					是否确认将用户
					<span :title="selectedUserName" class="name">{{selectedUserName}}</span>
					所属部门变更为
					<span :title="selectedDepName" class="name">{{selectedDepName | filterOrganName}}</span>
				</center>
			</Modal>

			<Modal v-model="deleteModal" :title="deleteTitle" @on-ok="deleteDep" @on-cancel="deleteModal = false;">
				<center>
					是否确认删除
					<span :title="selectDepName" class="name">{{selectDepName | getDeleteName}}</span>
					{{!selectPId ? '机构' : '部门'}}
				</center>
			</Modal>
		</div>

		<!-- 实体 -->
		<div class="flex">
			<!-- 机构目录 -->
			<div class="left shadow">
				<div v-if="hasSuperPerm" class="head">
					<span class="text super bold" @click="showOrganList">机构目录</span>
					<span class="plus bold" title="添加新机构" @click="addNewOrgan">+</span>
				</div>
				<div v-else class="head">
					<span class="text bold">机构目录</span>
					<span class="plus bold"></span>
				</div>
				<EasyScrollbar :barOption="myBarOption" ref="scrollVue">
					<div v-if="!isDepListNull" class="tree">
						<Spin fix size="large" v-show="depLoading"></Spin>
						<Tree ref="leftTree" :data="depList" empty-text @handle-organ="handleOrgan" @on-select-change="selectMenu"></Tree>
					</div>
					<div v-else class="datanull organ">
						<nodata-page :classVal="'nodata2'"></nodata-page>
					</div>
				</EasyScrollbar>
			</div>

			<!-- 机构列表 -->
			<div v-if="hasSuperPerm && !isSingle" class="right">
				<!-- 标题 -->
				<div class="flex-between">
					<div class="hl">机构列表</div>
					<div class="hr">
						共有机构<span class="bold">{{organCount}}</span>个，
						共有用户<span class="bold">{{userCount}}</span>个
					</div>
				</div>

				<!-- 列表 -->
				<div class="olist shadow">
					<ul class="ul bgc">
						<li>序号</li>
						<li>机构名称</li>
						<li>用户数</li>
						<li>创建日期</li>
					</ul>
					<div class="relative" v-if="!isOrganListNull">
						<Spin fix size="large" v-show="organLoading"></Spin>
						<ul class="ul" v-for="(item, index) of organList" :key="index">
							<li>{{pageNo, pageSize, index | getIndex}}</li>
							<li><span class="ellipsis" :title="item.organName">{{item.organName}}</span></li>
							<li>{{item.userCount}}</li>
							<li>{{item.createTime | formatYearDate}}</li>
						</ul>
						<ul class="ul page">
							<Page-bar @refreshPage="queryOrgPageData" :total="organCount" :page-size="pageSize" :current="pageNo"></Page-bar>
						</ul>
					</div>
					<div v-else class="datanull">
						<nodata-page :classVal="'nodata2'"></nodata-page>
					</div>
				</div>
			</div>

			<!-- 用户列表 -->
			<div v-else class="right">
				<!-- 标题 -->
				<div class="flex-between">
					<div class="hl">
						<span class="tn-width" :title="selectDepName | getFinalName">{{selectDepName | getFinalName}}</span>
						<span>用户列表</span>
					</div>
					<div class="hr">
						<span class="m10">
							<Button icon="md-add" @click="clickAddBtn">新建账号</Button>
						</span>
						<Button icon="md-swap" @click="openModal">部门变更</Button>
						<div class="m10">
							<Input v-model="searchWord" @on-search="searchDepUsers" search placeholder="输入用户名或用户名" style="width: 250px;"/>
						</div>
            共有用户<span class="bold">{{count}}</span>个
					</div>
				</div>

				<!-- 列表 -->
				<div class="ulist shadow">
					<ul class="ul bgc">
						<li><Checkbox :indeterminate="indeterminate" :value="checkAll" @click.prevent.native="handleCheckAll"></Checkbox></li>
						<li>序号</li>
						<li>姓名</li>
						<li>用户名</li>
						<li>部门</li>
						<li>用户角色</li>
					</ul>
					<div class="relative" v-if="!isUserListNull">
						<Spin fix size="large" v-show="userLoading"></Spin>
						<CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
							<ul class="ul" v-for="(item, index) of userList" :key="index">
								<li><Checkbox :label="item.userId"></Checkbox></li>
								<li>{{pageNo, pageSize, index | getIndex}}</li>
								<li>
									<div class="user-info">
										<div class="ellipsis" @mouseenter="getUserInfo(index, item.userId, item.userInfo)" @mouseleave="showTip=false">{{item.userName}}</div>
										<div v-show="showTip && index == activedRow" class="tooltip">
											<span class="info">{{userInfo}}</span>
										</div>
									</div>
								</li>
								<li><span class="ellipsis" style="white-space:pre;" :title="item.userAccount">{{item.userAccount}}</span></li>
								<li><span class="ellipsis" :title="item.organName">{{item | filterFirstName}}</span></li>
								<li>{{item.roleName}}</li>
							</ul>
						</CheckboxGroup>
						<ul class="ul page">
							<Page-bar @refreshPage="queryUserPageData" :total="count" :page-size="pageSize" :current="pageNo"></Page-bar>
						</ul>
					</div>
					<div v-else class="datanull">
						<nodata-page :classVal="'nodata2'"></nodata-page>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import { formatDate } from "@/util/date.js";
	import Tree from "@/components/common/tree";
	import PageUtils from "@/assets/js/page/pageUtil.js";
	import PageBar from "@/components/common/pageBar.vue";
	import NodataPage from "@/components/common/nodata/nodataPage.vue";
	export default {
		data() {
			return {
				hasSuperPerm: false, //是否拥有超管权限
				selectNode: {}, //当前选择的节点

				organId: 1, //当前用户所属机构的organId,从session中得到
				isSingle: true, //左侧目录树是否选中某个机构/部门

        /*当前选中的机构*/
        activeOrgan: {},
				selectDepName: "", //左侧目录树选中那个部门的部门名称
				selectDepCode: "", //当前选中的机构/部门层级编码
				selectPId: 0, //右键时选中的部门的父级部门id
				selectNodeId: 0, //右键时选中的当前节点的organId
				selectDepTitle: "", //当前选中的部门标题（名称）

				/*新建账户*/
				newUserAccount: "",
				addAccountModal: false,

        //添加机构对话框
				addTitle: "", //添加对话框标题显示内容
				addLoading: true,
				newOrganName: "",
        isAddOrgan: false,
				addOrganModal: false,
				addOrganLoading: true,

				//修改和删除部门对话框绑定值
				editTitle: "", //修改对话框标题显示内容
				deleteTitle: "", //删除对话框标题显示内容
				newDepName: "",
				editLoading: true,
				deleteModal: false,
				editDepModal: false,
				editDepLoading: true,

				//部门变更对话框
				selectUsers: [], //已选中的用户的userId
				editModal: false,
				selectedOrganId: 1, //部门变更时选中的用户所在的机构id
				selectedDepName: "", //部门变更时选中的变更后的部门名称
				selectedUserName: "", //部门变更时选中的用户名

				//部门变更确认对话框
				confirmModal: false,
				editedDepCode: "", //部门变更时选中的机构/部门层级编码

				/*机构列表绑定数据*/
				depList: [],
				userCount: 0,
				organList: [],
				organCount: 0,
				depModalList: [],
				depLoading: false,
				organLoading: false,
				isDepListNull: false,
				isOrganListNull: false,
				depModalLoading: false,

				/*用户列表绑定数据*/
				count: 0,
				pageNo: 1,
				orgName: "",
				userList: [],
				userInfo: "",
				pageSize: 15,
				activedRow: 1,
				searchWord: "",
				showTip: false,
				checkAll: false,
				checkAllGroup: [],
				userLoading: false,
				indeterminate: false,
				isUserListNull: false,

				/*其它*/
				myBarOption: {
					barWidth: 3, //滚动条宽度
					zIndex: "auto", //滚动条z-Index
					railColor: "#eee", //导轨颜色
					barMarginRight: 0, //垂直滚动条距离整个容器右侧距离单位（px）
					barMaginBottom: 0, //水平滚动条距离底部距离单位（px)
					barOpacityMin: 0.3, //滚动条非激活状态下的透明度
					barColor: "#959595", //滚动条颜色
					autohidemode: true, //自动隐藏模式
					horizrailenabled: true //是否显示水平滚动条
				}
			};
		},
		methods: {
			/*点击部门变更确认按钮*/
			editConfirm(){
				if(!this.selectedDepName){
					this.$Message.error({content: "请选择要变更的部门！", duration: 3, closable: false});
					return;
				}
				this.confirmModal = true;
			},
			/*点击机构目录后的+号*/
			addNewOrgan() {
        this.isAddOrgan = true;
				this.selectDepCode = "";
        this.addOrganModal = true;
				this.newOrganName = '';
				this.addTitle = "新建机构";
				this.selectPId = 0;
			},
			/*右键菜单单击事件*/
			handleOrgan(obj) {
        let method = obj.event.funName;
				switch (method) {
          case "addOrgan":
            this.isAddOrgan = false;
						this.addOrganModal = true;
						this.newOrganName = '';
						this.addTitle = obj.event.title;
						break;
					case "editOrgan":
						this.editDepModal = true;
						this.editTitle = obj.event.title;
            this.newDepName = this.$options.filters.getFinalName(obj.node.organName);
						break;
					case "delOrgan":
						this.deleteModal = true;
						this.deleteTitle = obj.event.title;
						break;
        }
        this.activeOrgan = obj.node;
				this.selectPId = obj.node.parentId;
				this.selectNodeId = obj.node.organId;
				this.selectDepTitle = obj.node.title;
				this.selectDepCode = obj.node.depCode;
				this.selectDepName = obj.node.organName;
			},
			/* 新建、修改、删除机构后重新加载机构列表 */
			reloadOrganList() {
				if (this.hasSuperPerm && !this.isSingle) {
					this.getOrganList();
				}
			},
			/*修改机构名称时验证修改后的机构名*/
			validateEditName() {
        let re=/[-]/;
        let str = this.newDepName.trim();
				if(re.test(str)){
					this.$Message.error({content: (this.selectPId === 0 ? '机构' : '部门')+"名称不能含有 - 符号！", duration: 3, closable: false});
					this.editDepLoading = false;
					this.$nextTick(() => {
						this.editDepLoading = true;
					});
					return;
        }

				if (!str) {
					setTimeout(() => {
						this.editDepLoading = false;
						this.$nextTick(() => {
							this.editDepLoading = true;
						});
						this.newDepName = "";
						this.$Message.error({content: (this.selectPId === 0 ? '机构' : '部门')+"名称不能为空！", duration: 3, closable: false});
          }, 200);
          return ;
        }

        if(str.length >= 30){
          setTimeout(() => {
						this.editDepLoading = false;
						this.$nextTick(() => {
							this.editDepLoading = true;
						});
            this.$Message.error({content: "名称不要超过30个字符！", duration: 3, closable: false});
          }, 200);
          return;
        }

        this.editDepName();
			},
			/*修改机构名称*/
			editDepName() {
				let patchParams = {
					organId: this.selectNodeId,
					organName: this.newDepName,
					depCode: this.selectDepCode
				};
				let patchURL = gl.serverURL + "/sysmng/sysrole/organ/update";
				this.$http.patch(patchURL, patchParams, { emulateJSON: true }).then(resp => {
          let ret = resp.body;
          let status = ret.status;
          this.editDepLoading = false;
          if (status == 0) {
            this.$Message.success({content: ret.message, duration: 3, closable: false});
            this.handleUserOrganInfo(this.selectDepName, this.selectDepCode, this.newDepName);
            this.handleOrganNameLocal(this.depList, this.selectNodeId, this.newDepName);
            this.editDepModal = false;
            this.reloadOrganList();
          } else {
            this.$Message.error({ content: ret.message, duration: 3, closable: false });
          }
        });
      },
      /* 递归处理免刷新更新部门列表信息 */
      handleOrganNameLocal(depArr, selectOrganId, depName){
        let depTempList = depArr;
        for(let i=0; i<depTempList.length; i++){
          let organ = depTempList[i];
          if(organ.organId === selectOrganId){
            organ.title = organ.depLevel == 1 ? (depName + "（"+organ.userNum+"）") : depName;
            organ.organName = depName;
            this.depList = depTempList;
            return;
          }
          if(organ.children){
            this.handleOrganNameLocal(organ.children, selectOrganId, depName);
          }
        }
        this.depList = depTempList;
      },
      /* 免刷新更新用户列表信息 */
      handleUserOrganInfo(selectName, depCode, depName){
        let nameArr = [];
        if(selectName.indexOf('-') != -1){
          nameArr = selectName.split('-');
          nameArr[nameArr.length-1] = depName;
        }else{
          nameArr.push(depName);
        }

        for(let i=0; i<this.userList.length; i++){
          if(this.userList[i].depCode === depCode){
            this.userList[i].organName = nameArr.join('-');
          }
        }
      },
			/*删除机构*/
			deleteDep() {
				let delParams = {
					parentId: this.selectPId,
					depCode: this.selectDepCode
				};
				let delURL = gl.serverURL + "/sysmng/sysrole/organ/delete";
				this.$http.delete(delURL, { params: delParams }).then(resp => {
					let ret = resp.body;
					let status = ret.status;
					this.deleteModal = false;
					if (status == 0) {
						let showName = "";
						if (this.selectDepCode.indexOf(this.organId) != -1 && this.selectDepName.indexOf("-") != -1) {
							let arr = this.selectDepName.split("-");
							for (let i = 0; i < arr.length; i++) {
								if (i == 0) {
									showName = arr[i];
								} else {
									if (i != arr.length - 1) {
										showName = showName + "-" + arr[i];
									}
								}
							}
							this.selectDepName = showName;
						}
						this.$Message.success({ content: ret.message, duration: 3, closable: false });
            this.removeOrganWithoutRefresh(this.depList, this.selectNodeId);
						this.reloadOrganList();
					} else {
						this.$Message.error({content: ret.message, duration: 3, closable: false});
					}
				});
      },
      /* 删除部门时免刷新更新机构列表 */
      removeOrganWithoutRefresh(orgList, organId){
        let orgTempList = orgList;
        for(let i=0; i<orgTempList.length; i++){
          if(orgTempList[i].organId === organId){
            orgTempList.splice(i, 1);
            this.depList = orgTempList;
            return;
          }

          if(orgTempList[i].children){
            this.removeOrganWithoutRefresh(orgTempList[i].children, organId);
          }
        }
        this.depList = orgTempList;
      },
			/*点击某一节点*/
			selectMenu(node) {
				this.isSingle = true;
				this.selectNode = node;
				if (node.length > 0) {
          this.activeOrgan = node[0];
					this.selectDepCode = node[0].depCode;
					this.selectDepName = node[0].organName;
					this.selectDepTitle = node[0].organName;
					this.organId = node[0].depCode.split("#")[1];
          this.pageNo = node.length > 0 ? 1 : this.pageNo;
          this.searchWord = node.length > 0 ? "" : this.searchWord;
					this.getDepUsers();
				}else{
          this.activeOrgan = null;
        }
				this.checkAll = false;
				this.selectUsers = [];
				this.checkAllGroup = [];
				this.indeterminate = false;
				this.selectedUserName = "";
			},
			/*查询左侧机构树*/
			getDepList() {
				this.depList = [];
				this.depLoading = true;
				let getURL = this.hasSuperPerm ? gl.serverURL + "/super/list/allDep" : gl.serverURL + "/sysmng/sysrole/organ/list/curDep";
				this.$http.get(getURL, {params: {organId: this.hasSuperPerm ? 0 : this.organId}}).then( resp => {
          let ret = resp.body;
          let status = ret.status;
          this.depLoading = false;
          if (status == 0) {
            this.depList = ret.data;
            if (!this.hasSuperPerm) {
              this.depList[0].events[2] = {};
            }
            this.isDepListNull = this.depList.length > 0 ? false : true;
            this.selectDepName = this.selectDepName ? this.selectDepName : this.depList[0].organName;
          }
        }, resp => {
          this.depLoading = false;
          this.$Message.error({ content: "获取机构目录异常！", duration: 3, closable: false });
        });
      },
			/*添加新机构*/
			addOrgan() {
        let re=/[-]/;
        let str = this.newOrganName.trim();
				let msg = this.selectPId === 0 ? '机构' : '部门';
				if(re.test(str)){
					this.$Message.error({content: msg+"名称不能含有 - 符号！", duration: 3, closable: false});
					this.addOrganLoading = false;
					this.addOrganModal = true;
					this.$nextTick(() => {
							this.addOrganLoading = true;
						});
					return;
        }

				if (!str) {
					setTimeout(() => {
						this.addOrganLoading = false;
						this.$nextTick(() => {
							this.addOrganLoading = true;
						});
						this.newOrganName = "";
						this.$Message.error({ content: msg+"名称不能为空！", duration: 3, closable: false });
          }, 200);
          return;
        }

        if(str.length >= 30){
          setTimeout(() => {
						this.addOrganLoading = false;
						this.$nextTick(() => {
							this.addOrganLoading = true;
						});
            this.$Message.error({content: "名称不要超过30个字符！", duration: 3, closable: false});
          }, 200);
          return;
        }

        this.confirmAddOrgan();
			},
			/*确认添加机构*/
			confirmAddOrgan() {
				let postParams = {
					depCode: this.selectDepCode,
					organName: this.newOrganName
				};
				let postURL = this.isAddOrgan ? gl.serverURL + "/super/organ/insert" : gl.serverURL + "/sysmng/sysrole/organ/insert";
				this.$http.post(postURL, postParams, { emulateJSON: true }).then( resp => {
          let ret = resp.body;
          let status = ret.status;
          this.addOrganLoading = false;
          if (status == 0) {
            this.$Message.success({ content: ret.message, duration: 3, closable: false });
            this.addOrganModal = false;
            this.reloadOrganList();
            this.getDepList();
          } else {
            this.$Message.error({ content: ret.message, duration: 3, closable: false });
          }
        }, resp => {
          this.$Message.error({content: "系统服务异常！", duration: 3, closable: false});
        });

      },

			/*点击机构目录事件*/
			showOrganList() {
				this.pageNo = 1;
				this.isSingle = false;
				this.getOrganList();
			},
			/*获取右侧机构列表*/
			getOrganList() {
				this.organList = [];
				let getParams = {
					pageSize: this.pageSize, //每页数据量
					extIndex: this.pageNo //当前页页码
				};
				this.organLoading = true;
				let queryURL = gl.serverURL + "/super/list/organ";
				this.$http.get(queryURL, { params: getParams }).then( resp => {
          let ret = resp.body;
          let status = ret.status;
          this.organLoading = false;
          if (status == 0) {
            if (ret.data) {
              this.userCount = ret.data.userCount;
              this.organCount = ret.data.organCount;
              this.organList = ret.data.organList;
            }
            this.isOrganListNull = this.organList.length > 0 ? false : true;
          }
        }, resp => {
          this.organLoading = false;
          this.$Message.error({ content: "服务请求异常！", duration: 3, closable: false });
        });
			},
			/*点击新建账号按钮*/
			clickAddBtn() {
        this.checkAll = false;
				this.checkAllGroup = [];
        this.indeterminate = false;

				if (!this.activeOrgan) {
					this.$Message.error({content: "请至少选中一个部门再进行操作！", duration: 3, closable: false});
					return;
				}
				this.addAccountModal = true;
			},
			/*验证用户名是否为空*/
			validateNull() {
				if (!this.newUserAccount.trim()) {
					setTimeout(() => {
						this.addLoading = false;
						this.$nextTick(() => {
							this.addLoading = true;
						});
						this.newUserAccount = "";
						this.$Message.error({content: "用户昵称不能为空", duration: 3, closable: false});
          }, 200);
          return;
        }
        let reg = /[\u4E00-\u9FA5\uF900-\uFA2D]/;
        if(reg.test(this.newUserAccount)){
          setTimeout(() => {
						this.addLoading = false;
						this.$nextTick(() => {
							this.addLoading = true;
						});
            this.$Message.error({content: "用户名不能包含中文字符", duration: 3, closable: false});
          }, 200);
          return;
        }
        this.addAccount();
			},
			/*添加新账号按钮点击事件*/
			addAccount() {
				let postParams = {
					depCode: this.activeOrgan.depCode,
					userAccount: this.newUserAccount
				};
				let postURL = gl.serverURL + "/user/insert";
				this.$http.post(postURL, postParams, { emulateJSON: true }).then( resp => {
          let ret = resp.body;
          let status = ret.status;
          this.addLoading = false;
          if (status == 0) {
            this.newUserAccount = "";
            this.addAccountModal = false;
            this.$Message.success({ content: ret.message, duration: 3, closable: true });
            this.getDepUsers(); //刷新当前部门用户列表
            //刷新左侧机构目录中机构名后括号里的数字
            let depLen = this.depList.length;
            for(let i=0; i<depLen-1; i++){
              let organ = this.depList[i];
              if(organ.organId == this.activeOrgan.depCode.split('#')[1]){
                this.depList[i].userNum += 1;
                this.depList[i].title = this.depList[i].organName + "（" + this.depList[i].userNum + "）";
                break;
              }
            }
          } else {
            this.$Message.error({ content: ret.message, duration: 3, closable: false });
          }
        }, resp => {
          this.addLoading = false;
          this.$Message.error({ content: "服务请求异常！", duration: 3, closable: false });
        });
			},
			/*部门变更按钮点击事件*/
			openModal() {
        this.selectedDepName = "";
        if(!this.activeOrgan && !this.activeOrgan.organId){
          this.$Message.error({content: "请先选中一个机构或者部门！", duration: 3, closable: false});
          return;
        }

				if (this.checkAllGroup.length == 0) {
          this.$Message.error({content: "请至少选择一个人员！", duration: 3, closable: false});
					return;
        }

        // 验证所勾选的人员是否含有其他机构的人
        let organIdArray = [];
        for(let i=0; i<this.checkAllGroup.length; i++){
          let id = this.checkAllGroup[i];
          for(let j=0; j<this.userList.length; j++){
            let user = this.userList[j];
            if(id === user.userId){
              organIdArray.push(user.organId);
            }
          }
        }

        let newArray = new Set(organIdArray); // 数组元素去重
        if(newArray.size > 1){
          this.$Message.error({content: "请勾选同机构的人员进行部门变更操作！", duration: 3, closable: false});
          return;
        }

				//定义当前选中的机构id
        this.selectedUserName = "";
        for(let id of newArray){
          this.selectedOrganId = id;
        }

				//将选中的用户的用户名拼起来
				this.selectUsers.forEach((user, index) => {
					this.selectedUserName += index < this.selectUsers.length - 1 ? user.userName + "、" : user.userName;
				});
				this.getModalDepList();
				this.editModal = true;
			},
			/*获取部门变更对话框中的机构目录树*/
			getModalDepList() {
				this.depModalList = [];
				this.depModalLoading = true;
				let getURL = gl.serverURL + "/sysmng/sysrole/organ/list/dep/modal";
				this.$http.get(getURL, { params: { organId: this.selectedOrganId } }).then(resp => {
          let ret = resp.body;
          let status = ret.status;
          this.depModalLoading = false;
          if (status == 0) {
            this.depModalList = ret.data;
          }
        });
			},
			/*部门变更选择部门*/
			choseDep(node) {
				if(node.length>0){
					this.editedDepCode = node[0].depCode;
					this.selectedDepName = node[0].title;
				}else{
					this.selectedDepName = "";
				}
			},
			/*确认部门变更*/
			changeDep() {
				this.editLoading = true;
				let patchParams = {
					depCodeAfter: this.editedDepCode,
					userIds: this.checkAllGroup.join(",")
				};
				let patchURL = gl.serverURL + "/user/update";
				this.$http.patch(patchURL, patchParams, { emulateJSON: true }).then( resp => {
          let ret = resp.body;
          let status = ret.status;
          this.editLoading = false;
          if (status == 0) {
            this.indeterminate = false;
            this.checkAll = false;
            this.getDepUsers();
            this.editModal = false;
            this.selectDepTitle = "";
            this.$Message.success({ content: ret.message, duration: 3, closable: true });
            this.selectedDepName = '';
          } else {
            this.$Message.error({ content: ret.message, duration: 3, closable: false });
          }
        }, resp => {
          this.editLoading = false;
          this.$Message.error({content: "服务请求异常！", duration: 3, closable: false});
        });
			},
			/*搜索部门用户*/
			searchDepUsers() {
        if(!this.searchWord.trim()){
          this.$Message.error({content: "搜索内容不能为空！", duration: 3, closable: false});
          return;
        }
				this.pageNo = 1;
        this.checkAll = false;
				this.checkAllGroup = [];
				this.indeterminate = false;
				this.organId = this.hasSuperPerm ? 0 : this.organId;
				this.selectDepCode = this.hasSuperPerm ? null : this.selectDepCode;
				this.getDepUsers();
			},
			/* 获取右侧部门下的用户列表 */
			getDepUsers() {
				this.userList = [];
				let getParams = {
					extIndex: this.organId, //机构id
					pageSize: this.pageSize, //每页数据量
			        pageNo: this.pageNo,
					extContent: this.selectDepCode,
					searchWord: this.searchWord, //搜索关键词
				//	startRow: (this.pageNo - 1) * this.pageSize //起始偏移位置
				};
				this.userLoading = true;
				let queryURL = gl.serverURL + "/user/list";
				this.$http.get(queryURL, { params: getParams }).then( resp => {
          let ret = resp.body;
          let status = ret.status;
          this.userLoading = false;
          if (status == 0) {
            if (ret.data) {
              this.count = ret.data.count;
              this.userList = ret.data.list;
              this.checkAllGroup = [];
              this.selectUsers = [];
            }
            this.isUserListNull = this.userList.length > 0 ? false : true;
          }
        }, resp => {
          this.userLoading = false;
          this.$Message.error({ content: "服务请求异常！", duration: 3, closable: false });
        });
			},
			/*获取门户同步的用户所述部门信息*/
			getUserInfo(index, userId, userInfo) {
				this.activedRow = index;
				if (!userInfo) {
					let getURL = gl.serverURL + "/user/depInfo";
					this.$http.get(getURL, { params: { userId: userId } }).then( resp => {
            let ret = resp.body;
            let status = ret.status;
            if (status == 0) {
              let info = ret.data;
              this.userInfo = info.indexOf("-") === -1 ? info + "-暂无部门信息" : info;
              let len = this.userList.length;
              for (let i = 0; i < len; i++) {
                let user = this.userList[i];
                if (userId == user.userId) {
                  user.userInfo = info;
                  break;
                }
              }
              this.showTip = true;
            } else {
              this.$Message.error({ content: "用户部门信息获取失败！", duration: 3, closable: false });
            }
          }, resp => {
            this.$Message.error({ content: "获取用户部门信息时出现异常！", duration: 3, closable: false });
          } );
				} else {
					this.showTip = true;
					this.userInfo = userInfo;
				}
			},
			/* 全选复选框处理 */
			handleCheckAll() {
				this.checkAll = this.indeterminate ? false : !this.checkAll;
				this.indeterminate = false;
				if (this.checkAll) {
					this.userList.forEach(item => {
						this.selectUsers.push(item);
						this.checkAllGroup.push(item.userId);
					});
				} else {
					this.selectUsers = [];
					this.checkAllGroup = [];
				}
			},
			/* 列表中复选框处理 */
			checkAllGroupChange(data) {
				this.selectUsers = [];
				if (data.length === this.userList.length) {
					this.indeterminate = false;
					this.checkAll = true;
				} else if (data.length > 0) {
					this.indeterminate = true;
					this.checkAll = false;
				} else {
					this.indeterminate = false;
					this.checkAll = false;
				}
				data.forEach(id => {
					this.userList.forEach(user => {
						if (id == user.userId) {
							this.selectUsers.push(user);
						}
					});
				});
			},
			/*机构列表翻页查询*/
			queryOrgPageData(param) {
				this.pageNo = param.pageno;
				this.userLoading = true;
				this.getOrganList();
			},
			/*用户列表翻页查询*/
			queryUserPageData(param) {
				this.pageNo = param.pageno;
				this.userLoading = true;
				this.indeterminate = false;
				this.checkAll = false;
				this.getDepUsers();
			}
		},
		filters: {
			getDeleteName(name){
				let depName = name.split('-');
				return depName[depName.length - 1];
			},
			formatYearDate(time) {
				var date = new Date(time);
				return formatDate(date, "yyyy-MM-dd");
			},
			getIndex(pageno, pagesize, index) {
				index = PageUtils.getPageIndex(pageno, pagesize, index);
				return index;
			},
			getFinalName(name) {
				if (name && name.indexOf("-") != -1) {
					let names = name.split("-");
					return names[names.length - 1];
				}
				return name;
			},
			filterFirstName(user) {
				let name = user.organName ? user.organName : '';
				return name.indexOf("-") === -1 ? name : name.substring(name.indexOf("-") + 1, name.length);
      },
      filterOrganName(name){
        if(name){
          return name.indexOf('（') != -1 ? name.substring(0, name.indexOf('（')) : name;
        }
      }
		},
		components: {
			Tree,
			PageBar,
			NodataPage
		},
		beforeMount() {
			this.organId = sessionStorage.getItem("organId");
			this.hasSuperPerm = this.hasPermission("/super"); //是否拥有超级权限
		},
		mounted() {
			gl.dep = this;
			if (this.hasSuperPerm) {
				this.isSingle = false;
				this.getDepList();
				this.getOrganList();
			} else {
				this.getDepList();
				this.getDepUsers();
			}
		}
	};
</script>

<style scoped lang="less">
.flex {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: flex-start;

	.left {
		width: 270px;
		max-height: 640px;
		display: flex;
		margin-top: 10px;
		flex-direction: column;

		.tree {
			width: 270px;
			min-height: 250px;
			max-height: 600px;
			display: flex;
			padding: 5px 10px;
      justify-content: flex-start;
      position: relative;

      /deep/.ivu-tree-title {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
		}

		.head {
			height: 40px;
			display: flex;
			padding: 0px 10px;
			align-items: center;
			background: #e8eaf4;
			justify-content: flex-end;

			.text {
				margin-right: 31%;

				&.super {
					cursor: pointer;
				}
			}

			.plus {
				width: 13px;
				cursor: pointer;
			}
		}
	}

	.right {
		width: 78%;
		padding: 0px 20px;

		.flex-between {
			height: 40px;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.hl {
				.bold;
				width: 36%;
				display: inline-flex;
				align-items: center;

				.tn-width {
					max-width: 80%;
					.ellipsis;
				}
			}
			.hr {
				width: 600px;
				display: inline-flex;
				align-items: center;
        justify-content: flex-end;

        /deep/.ivu-btn {
          color: #3172eb;
          background-color: #fff;
        }
        /deep/.ivu-btn > .ivu-icon {
          font-weight: bold;
        }
			}
		}

		.relative {
			position: relative;
		}

		.olist {
			margin-top: 10px;
			max-height: 700px;

			ul:nth-child(even) {
				background: rgba(232, 234, 236, 0.5);
			}

			.ul {
				height: 40px;
				display: flex;
				align-items: center;
				flex-direction: row;
				justify-content: center;

				li {
					height: 40px;
					display: inline-flex;
					align-items: center;
					justify-content: center;
					border-right: 1px solid #ddd;
					border-bottom: 1px solid #ddd;
				}

				> li:nth-child(1) {
					width: 100px;
				}
				> li:nth-child(2) {
          flex: 1;
          .ellipsis;
					padding: 0px 10px;
				}
				> li:nth-child(3) {
					width: 120px;
				}
				> li:nth-child(4) {
					width: 180px;
					border-right: 0px solid #ddd;
				}

				&.bgc {
					font-weight: bold;
					background: #e8eaf4;
				}

				&.page {
					height: 60px;
					background: white;
				}
			}
		}

		.ulist {
			margin-top: 10px;
			max-height: 700px;

			ul:nth-child(even) {
				background: rgba(232, 234, 236, 0.5);
			}

			.ul {
				height: 40px;
				display: flex;
				align-items: center;
				flex-direction: row;
        justify-content: center;

        /deep/.ivu-checkbox-wrapper {
          margin-right: 0px;
        }

				li {
					height: 40px;
					display: inline-flex;
					align-items: center;
					justify-content: center;
					border-right: 1px solid #ddd;
					border-bottom: 1px solid #ddd;
				}

				> li:nth-child(1) {
					width: 50px;
				}
				> li:nth-child(2) {
					width: 80px;
				}
				> li:nth-child(3) {
					width: 160px;
				}
				> li:nth-child(4) {
					width: 200px;
					padding: 0px 10px;
				}
				> li:nth-child(5) {
					padding: 0px 10px;
					width: calc(~"100% - 650px");
				}
				> li:nth-child(6) {
					width: 160px;
					border-right: 0px solid #ddd;
				}

				&.bgc {
					font-weight: bold;
					background: #e8eaf4;
				}

				&.page {
					height: 60px;
					background: white;
				}
			}
		}
	}

	.user-info {
    width: 100%;
    padding: 0px 5px;
		text-align: center;
		position: relative;

		.tooltip {
			top: 20px;
			left: 50%;
			min-width: 360px;
			text-align: left;
			position: absolute;

			.info {
				color: #696969;
				font-size: 14px;
				background: #fff;
				min-width: 100px;
				padding: 0px 5px;
				border: 1px solid #696969;
			}
		}
	}

	.bold {
		margin: 0px 3px;
		font-size: 16px;
		font-weight: bold;
	}

	.m10 {
		margin: 0px 10px;
	}
}

	.ellipsis {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

.pad10 {
	font-size: 14px;
	padding: 10px 0px;
}

.name {
  display: inline-block;
  vertical-align: text-top;
  max-width: 280px;
	color: #0000ff;
  .ellipsis;
}

/*部门变更对话框样式*/
.dep {
	display: flex;
	flex-direction: column;
	justify-content: center;

	.up {
		font-size: 14px;
	}

	.down {
		width: 100%;
		display: flex;
		position: relative;
		justify-content: center;
	}
}

.datanull {
	display: flex;
	align-items: center;
	justify-content: center;

	&.organ {
		height: 600px;
		background: #fff;
	}
}
</style>
