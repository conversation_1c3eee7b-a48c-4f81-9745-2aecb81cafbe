<!-- 入口页 -->
<template>
  <div class="cts">
    <div
      class="remarks"
      style="
        top: 450px;
        width: 2000px;
        display: flex;
        justify-content: space-between;
        align-items: end;
      "
    >
      <span style="font-size: 60px"
        >总带班：{{zongData.userDuties}} {{zongData.userName}}</span
      ><span style="font-size: 40px;"
        >值班电话：{{watchTelephone}}</span
      >
    </div>
    <div class="remarks">
      备注：应急值班值守白天、夜间值班人员交接班时间为上午9:00，下午17:00
    </div>
    <!-- 舆情夜班时间 -->
  
    <div class="remarks" style="top:555px;left:644px;font-size:48px;">
      （{{ (dutyListOne[0] ? moment(dutyListOne[0].watchTime).format("MM月DD日"):'') + '-' + (dutyListOne[dutyListOne.length -1] ? moment(dutyListOne[dutyListOne.length -1].watchTime).format("MM月DD日"):'')}}）
      </div>
    <div class="remarks" style="top:1611px;left:644px;font-size:48px;">
      （{{ (dutyListOne[0] ? moment(dutyListOne[0].watchTime).format("MM月DD日"):'') + '-' + (dutyListOne[dutyListOne.length -1] ? moment(dutyListOne[dutyListOne.length -1].watchTime).format("MM月DD日"):'')}}）
      </div>


    <!-- 舆情夜班职责 -->

    <div class="remarks" style="top:2594px;left:77px;width:2012px;">
      <p>备注：舆情研判值班人员值班时间为上午9：00至下午21：00</p>
      <p>职责：</p>
      <p>1.负责监测研判推送涉济舆情。</p>
      <p>2.遇当日紧急突发舆情，根据领导安排，编辑舆情信息，并按要求报告。</p>
    </div>


    <!-- 值班人员表 -->
    <div class="duty-p">
      <div
        v-for="(t, index) in dutyTime"
        :key="index"
        style="display: flex"
        :style="index == 0 ? 'margin-bottom:30px;' : ''"
      >
        <div class="duty-d" style="width: 502px">
          <span
            :style="index == 0 ? 'margin-bottom:43px;' : 'margin-bottom:29px;'"
            >{{ moment( t.watchTime).format("MM月DD日") }}</span
          >
          <span
            :style="
              index == 0
                ? 'color:#c9eeff;font-size:58px;margin-bottom:52px;'
                : 'font-size:40px;margin-bottom:20px;'
            "
            >{{ index == 0 ?'今日':'明日' }}</span
          >
          <span>{{ getModay(t.watchTime)}}</span>
        </div>
        <div class="duty-d" style="width: 756px">
          <span
            :style="index == 0 ? 'margin-bottom:43px;' : 'margin-bottom:29px;'"
            >{{ t.userLeadAuties }}</span
          >
          <span
            :style="
              index == 0
                ? 'color:#c9eeff;font-size:58px;margin-bottom:52px;'
                : 'font-size:40px;margin-bottom:20px;'
            "
            >{{ t.userLeadName }}</span
          >
          <span>{{t.userLeadTelePhone}}</span>
        </div>
        <div style="display: flex">
          <div class="duty-d" style="width: 380px">
            <span
              :style="
                index == 0 ? 'margin-bottom:43px;' : 'margin-bottom:29px;'
              "
              >白班</span
            >
           <span v-for="(k,i) in t.baiUserList" :key="i" 
           style="display: flex;
             white-space: nowrap;
            align-items: center;
            margin-bottom:30px;
            justify-content: space-around;
            "
            :style="index == 1 ? 'margin-bottom:20px;':index == 0 && i == 0 ? 'margin-bottom:70px;':''"
            >
             <span
              :style=" index == 0 ? 'color:#c9eeff;font-size:40px;':''"
              >{{ k.userBaiName }}</span
            >
            <span>{{k.userBaiTelephone}}</span>
           </span>
          </div>

          <div class="duty-d" style="width: 360px">
            <span
              :style="
                index == 0 ? 'margin-bottom:43px;' : 'margin-bottom:29px;'
              "
              >夜班</span
            >
            <span
              :style="
                index == 0
                  ? 'color:#c9eeff;font-size:40px;margin-bottom:59px;height:76px;line-height:76px;'
                  : 'margin-bottom:25px;height:52px;line-height: 52px;'
              "
              >{{ t.yeUserList[0].userYeName }}</span
            >
            <span>{{ t.yeUserList[0].userYeTelephone }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 值班列表 -->
    <div class="duty-list">
       <div v-for="(d, index) in dutyListOne" :key="index" class="itm">
          <span>{{ moment( d.watchTime).format("MM月DD日") }}</span>
          <span style="width:574px;">{{  getModay(d.watchTime) }}</span>
          <span style="width:458px;">{{ d.userName? d.userName:'' }}</span>
          <span style="width:510px;">{{ d.watchArea ? d.watchArea:''}}</span>
        </div>
    </div>

    <!-- 舆情日历 -->
    <div class="rl">
      <!-- 第一个日历事件时间及名称 -->
      <div v-for="(t, index) in timeList" :key="index">
        <span class="one" :style="{ top: t.nameTop, left: t.nameLeft ,maxWidth:index == 0 ? '440px':''}" >{{
          t.name
        }}</span
        ><span
          class="one"
          :style="{ top: t.timeTop, left: t.timeLeft }"
          style="font-size: 38px"
          >{{  moment( t.time).format("MM月DD日")}}</span
        >
      </div>
    </div>

    <div class="time">{{ moment(nowtime).format("YYYY-MM-DD HH:mm:ss") }}</div>
  </div>
</template>

<script>
import moment from "moment";
export default {
  data() {
    return {
      nowtime: new Date(), //获取当前时间
      timeList: [],
      dutyListOne: [],
      dutyTime: [],
      zongData:{},
      watchTelephone:'',
    };
  },
  //生命周期 - 创建完成（访问当前this实例）
  created() {},
  //方法所在
  methods: {
    moment,
    getModay (time) {
        let times = new Date(time)
        let id = times.getDay()
      let str = "";
      if (id == 0) {
        str = "周日";
      } else if (id == 1) {
        str = "周一";
      } else if (id == 2) {
        str = "周二";
      } else if (id == 3) {
        str = "周三";
      } else if (id == 4) {
        str = "周四";
      } else if (id == 5) {
        str = "周五";
      } else if (id == 6) {
        str = "周六";
      }

      return str;
    },
    // 获取日历事件
    getTimeData() {
      this.$http.get(gl.jcczAPI+"/common/everydayYqCalendar").then((response) => {
        let data = response.body;
        if (data.status == 0) {
          let list = data.data;
          if (list.length > 0) {
            list.forEach((v, i) => {
              if (i == 0) {
                v.timeTop = "65px";
                v.timeLeft = "0px";
                v.nameTop = "320px";
                v.nameLeft = "-20px";
              } else if (i == 1) {
                v.timeTop = "65px";
                v.timeLeft = "602px";
                v.nameTop = "199px";
                v.nameLeft = "565px";
              } else if (i == 2) {
                v.timeTop = "65px";
                v.timeLeft = "1324px";
                v.nameTop = "199px";
                v.nameLeft = "1293px";
              } else if (i == 3) {
                v.timeTop = "379px";
                v.timeLeft = "530px";
                v.nameTop = "497px";
                v.nameLeft = "480px";
              } else if (i == 4) {
                v.timeTop = "379px";
                v.timeLeft = "1336px";
                v.nameTop = "497px";
                v.nameLeft = "1287px";
              }
            });

            this.timeList = list
          }
        } else {
          this.$Message.error({
            content: data.message,
            duration: 3,
            closable: true,
          });
        }
      }).finally(()=>{
          setTimeout(() => this.getTimeData(), 5 * 60 * 1000);
      });
    },

    getDutyData() {
        this.$http.get(gl.jcczAPI+"/common/everydayWatchTime").then((response) => {
        let data = response.body;
        if (data.status == 0) {
             let list = data.data.areaCity
             this.dutyListOne = list
             this.dutyTime = data.data.watchCity.watchUserList
             this.zongData = data.data.watchCity.leadUser
             this.watchTelephone = data.data.watchCity.watchTelephone
        } else {
          this.$Message.error({
            content: data.message,
            duration: 3,
            closable: true,
          });
        }
      }).finally(()=>{
          setTimeout(() => this.getDutyData(), 5 * 60 * 1000);
      });

    },
  },
  beforeDestroy() {
    // 清除定时器，防止内存泄漏
    clearInterval(this.timer);
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {
	  document.title = "24H值班表";
	  
    this.getTimeData();
    this.getDutyData();
    // 动态时间显示
    this.timer = setInterval(() => {
      this.nowtime = new Date();
    }, 1000);
  },
};
</script>
<style lang="less" scoped>
.cts {
  position: relative;
  width: 2160px;
  height: 3840px;

  background: url("../../assets/img/duty.svg") no-repeat center center #1d5287;
  .remarks {
    position: absolute;
    top: 1497px;
    left: 74px;
    font-family: Microsoft YaHei;
    color: #ffffff;
    font-size: 44px;
    -webkit-text-stroke-width: 1px;
    text-stroke-width: 1px;
    -webkit-text-stroke-color: #ffffff;
    text-stroke-color: #ffffff;
  }
  .time {
    position: absolute;
    bottom: 69px;
    left: 740px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #ffffff;
    font-size: 75px;
  }
  .rl {
    position: absolute;
    bottom: 254px;
    left: 162px;
    width: 1900px;
    height: 586px;
    color: #ffffff;
    font-size: 60px;
    .one {
      position: absolute;
      top: 0px;
      left: 0px;
      max-width: 620px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
    }
  }
  .duty-list {
    position: absolute;
    top: 1827px;
    left: 71px;
    height: 770px;
    overflow-y: auto;
    .itm {
      height: 110px;
      line-height: 110px;
      display: flex;
      align-items: center;
      color: #ffff;
      font-size: 40px;
      span {
        width: 504px;
        display: inline-block;
        text-align: center;
        line-height: 52px;
      }
    }
  }

  .duty-p {
    position: absolute;
    top: 900px;
    left: 76px;
    height: 557px;
    color: #ffff;
    font-size: 30px;
    .duty-d {
      display: flex;
      flex-direction: column;
      text-align: center;
    }
  }
}
</style>
