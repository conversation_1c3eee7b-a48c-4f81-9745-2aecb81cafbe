<!-- 入口页 -->
<template>
  <div class="frame">
    <div class="header">
      <!-- 当前时间 -->
      <div style="position: absolute; top: 45px; left: 20px;">
        <p class="time">
          {{ moment(nowtime).format("HH:mm:ss") }}
          <span class="time-one"
            >{{ moment(nowtime).format("yyyy.MM.DD") }}
            <span style="color: #00f0ff; letter-spacing: 3px;">
              &nbsp;{{ getModay(nowtime.getDay()) }}
            </span>
          </span>
        </p>
      </div>
      <!-- 标题 -->
      <div class="title"></div>
      <!-- 配置 暂时隐藏
      <div class="disposition">
        配置
        <svg-icon
          icon-class="配置"
          style="width: 20px; height: 17px; margin-left: 8px"
        />
      </div> -->
    </div>

    <div style="position: relative; margin-top: 40px;">
      <!-- 数量 -->
      <div class="nubmer">
        <!-- 网络舆情中心数据 
        <div class="nubmers rotate" :class="{ rotateShow: showIx === 0 }">
          <NumberList
            name="网络舆情中心"
            nameOne="今日舆情提示单"
            :number="promptCount"
            type="条"
          />
        </div>

        <div class="nubmers rotate" :class="{ rotateShow: showIx === 1 }">
          <NumberList
            name="网络舆情中心"
            nameOne="今日涉济舆情"
            :number="sjyqCount"
            type="条"
          />
        </div> -->

        <div
          :class="{ rotateShow: showIx === 0 }"
          class="nubmers rotate"
          @mouseenter="pauseId = 'showIx'"
          @mouseleave="pauseId = null"
        >
          <NumberList
            :number="sjCount"
            name="网络舆情中心"
            nameOne="24小时涉济信息量"
            type="条"
            @click.native="openPop('1')"
          />
        </div>
        <div
          :class="{ rotateShow: showIx === 1 }"
          class="nubmers rotate"
          @mouseenter="pauseId = 'showIx'"
          @mouseleave="pauseId = null"
        >
          <NumberList
            :number="todaySjCount"
            name="网络舆情中心"
            nameOne="今日涉济信息量"
            type="条"
            @click.native="openPop('3')"
          />
        </div>
        <div
          :class="{ rotateShow: showIx === 2 }"
          class="nubmers rotate"
          @mouseenter="pauseId = 'showIx'"
          @mouseleave="pauseId = null"
        >
          <NumberList
            :number="markingSjCount"
            name="网络舆情中心"
            nameOne="今日涉济重点关注"
            type="条"
            @click.native="openPop('2')"
          />
        </div>
        <!-- 网络宣传中心数据 -->
        <div
          :class="{ rotateShow: showWxIx === 0 }"
          class="nubmers rotate"
          style="left: 612.1px;"
          @mouseenter="pauseId = 'showWxIx'"
          @mouseleave="pauseId = null"
        >
          <a
            v-if="organType != 2"
            href="https://10.61.23.226/jnNetPromotionSys/smartDesign/93880081205862400/"
            target="_blank"
          >
            <NumberList
              :number="wxmtCount"
              name="网络传播中心"
              nameOne="媒体主体"
              type="个"
            />
          </a>
          <NumberList
            v-if="organType == 2"
            :number="wxmtCount"
            name="网络传播中心"
            nameOne="媒体主体"
            type="个"
          />
        </div>

        <div
          :class="{ rotateShow: showWxIx === 1 }"
          class="nubmers rotate"
          style="left: 612.1px;"
          @mouseenter="pauseId = 'showWxIx'"
          @mouseleave="pauseId = null"
        >
          <a
            v-if="organType != 2"
            href="https://10.61.23.226/jnNetPromotionSys/smartDesign/93880081205862400/"
            target="_blank"
          >
            <NumberList
              :number="wxxyCount"
              name="网络传播中心"
              nameOne="媒体信源"
              type="个"
            />
          </a>
          <NumberList
            v-if="organType == 2"
            :number="wxxyCount"
            name="网络传播中心"
            nameOne="媒体信源"
            type="个"
          />
        </div>

        <div
          :class="{ rotateShow: showWxIx === 2 }"
          class="nubmers rotate"
          style="left: 612.1px;"
          @mouseenter="pauseId = 'showWxIx'"
          @mouseleave="pauseId = null"
        >
          <a
            v-if="organType != 2"
            href="https://10.61.23.226/jnNetPromotionSys/smartDesign/93880081205862400/"
            target="_blank"
          >
            <NumberList
              :number="xczlCount"
              name="网络传播中心"
              nameOne="今日新增稿件数"
              type="个"
            />
          </a>
          <NumberList
            v-if="organType == 2"
            :number="xczlCount"
            name="网络传播中心"
            nameOne="今日新增稿件数"
            type="个"
          />
        </div>

        <div
          :class="{ rotateShow: showWxIx === 3 }"
          class="nubmers rotate"
          style="left: 612.1px;"
          @mouseenter="pauseId = 'showWxIx'"
          @mouseleave="pauseId = null"
        >
          <a
            v-if="organType != 2"
            href="https://10.61.23.226/jnNetPromotionSys/smartDesign/93880081205862400/"
            target="_blank"
          >
            <NumberList
              :number="wxjcCount"
              name="网络传播中心"
              nameOne="监测专题"
              type="个"
            />
          </a>
          <NumberList
            v-if="organType == 2"
            :number="wxjcCount"
            name="网络传播中心"
            nameOne="监测专题"
            type="个"
          />
        </div>

        <!-- 网络安全中心-单位数 -->
        <!--<div
          class="nubmers rotate"
          :class="{ rotateShow: showWaIndex === 0 }"
          style="left: 1471.34px"
        >
          <NumberList
            name="网络安全中心"
            nameOne="单位总数"
            :number="wzNumber.unitCount"
            type="个"
            :path="
              goNewPath('https://************/home/<USER>/synthesizeScreen')
            "
          />
        </div>-->

        <!-- 网络安全中心-系统数 -->
        <div
          :class="{ rotateShow: showWaIndex === 0 }"
          class="nubmers rotate"
          style="left: 1041.25px;"
          @mouseenter="pauseId = 'showWaIndex'"
          @mouseleave="pauseId = null"
        >
          <!-- 原先超链接:path="
              goNewPath('https://************/home/<USER>/synthesizeScreen')
            " -->
          <NumberList
            :number="wzNumber.systemCount"
            name="网络安全中心"
            nameOne="系统总数"
            type="个"
          />
        </div>
        <!-- 网络安全中心-网络攻击数 -->
        <div
          :class="{ rotateShow: showWaIndex === 1 }"
          class="nubmers rotate"
          style="left: 1041.25px;"
          @mouseenter="pauseId = 'showWaIndex'"
          @mouseleave="pauseId = null"
        >
          <NumberList
            :number="wzNumber.gjCount"
            name="网络安全中心"
            nameOne="近一月网络攻击数"
            type="个"
          />
        </div>
        <!-- 网络安全中心-安全日志数 -->
        <div
          :class="{ rotateShow: showWaIndex === 2 }"
          class="nubmers rotate"
          style="left: 1041.25px;"
          @mouseenter="pauseId = 'showWaIndex'"
          @mouseleave="pauseId = null"
        >
          <NumberList
            :number="wzNumber.aqrzCount"
            name="网络安全中心"
            nameOne="近一月安全日志数"
            type="个"
          />
        </div>
        <!-- 网络安全中心-安全通报数 -->
        <div
          :class="{ rotateShow: showWaIndex === 3 }"
          class="nubmers rotate"
          style="left: 1041.25px;"
          @mouseenter="pauseId = 'showWaIndex'"
          @mouseleave="pauseId = null"
        >
          <NumberList
            :number="wzNumber.aqtbCount"
            name="网络安全中心"
            nameOne="近一月安全通报数"
            type="个"
          />
        </div>

        <!-- 网络安全中心-ip资产数 -->
        <!--<div
          class="nubmers rotate"
          :class="{ rotateShow: showWaIndex === 2 }"
          style="left: 1471.34px"
        >
          <NumberList
            name="网络安全中心"
            nameOne="IP资产总数"
            :number="wzNumber.ipCount"
            type="个"
            :path="
              goNewPath('https://************/home/<USER>/synthesizeScreen')
            "
          />
        </div>-->

        <!-- 网络安全中心-安全事件数 -->
        <!--<div
          class="nubmers rotate"
          :class="{ rotateShow: showWaIndex === 3 }"
          style="left: 1471.34px"
        >
          <NumberList
            name="网络安全中心"
            nameOne="今日安全事件数"
            :number="wzNumber.aqCount"
            type="个"
            :path="
              goNewPath('https://************/home/<USER>/synthesizeScreen')
            "
          />
        </div>-->

        <!-- 网络安全中心-风险隐患数 -->
        <!--<div
          class="nubmers rotate"
          :class="{ rotateShow: showWaIndex === 4 }"
          style="left: 1471.34px"
        >
          <NumberList
            name="网络安全中心"
            nameOne="今日风险隐患数"
            :number="wzNumber.fxCount"
            type="个"
            :path="
              goNewPath('https://************/home/<USER>/synthesizeScreen')
            "
          />
        </div>-->

        <!--  -->

        <!-- 网络管理中心数据 -->
        <!-- <div
          :class="{ rotateShow: showQix === 0 }"
          class="nubmers rotate"
          style="left: 1471.34px;"
          @mouseenter="pauseId = 'showQix'"
          @mouseleave="pauseId = null"
        >
          <NumberList
            :number="wzNumber.wpzlCount"
            name="网络数字产业中心"
            nameOne=""
            type="个"
          />
        </div> -->
        <!-- 举报平台辟谣平台 -->
        <div
          :class="{ rotateShow: showQix === 0 }"
          class="nubmers rotate"
          style="left: 1471.34px;"
          @mouseenter="pauseId = 'showQix'"
          @mouseleave="pauseId = null"
        >
          <NumberList
            v-if="organType != 2 && isPad"
            :number="jbCount"
            name="网络治理中心"
            nameOne="近7天举报信息"
            path= "http://192.168.20.6:8882"
            type="条"
          />
          <NumberList
            v-if="organType != 2 && !isPad"
            :number="jbCount"
            name="网络治理中心"
            nameOne="近7天举报信息"
            path= "http://10.61.23.149/daping/v2/index.html"
            type="条"
          />
          <!-- <NumberList
            v-if="organType != 2"
            :number="jbCount"
            name="网络治理中心"
            nameOne="近7天举报信息"
            path="http://10.61.23.149/daping/v2/index.html"
            type="条"
          /> -->
          <NumberList
            v-if="organType == 2"
            :number="jbCount"
            name="网络治理中心"
            nameOne="近7天举报信息"
            path=""
            type="条"
          />
        </div>
        <div
          :class="{ rotateShow: showQix === 1 }"
          class="nubmers rotate"
          style="left: 1471.34px;"
          @mouseenter="pauseId = 'showQix'"
          @mouseleave="pauseId = null"
        >
          <NumberList
            v-if="organType != 2 && isPad"
            :number="pyCount"
            name="网络治理中心"
            nameOne="近一月辟谣信息"
            path="http://192.168.20.6:8882"
            type="条"
          />
          <NumberList
            v-if="organType != 2 && !isPad"
            :number="pyCount"
            name="网络治理中心"
            nameOne="近一月辟谣信息"
            path="http://10.61.23.149/daping/v2/index.html"
            type="条"
          />
          <!-- <NumberList
            v-if="organType != 2"
            :number="pyCount"
            name="网络治理中心"
            nameOne="近一月辟谣信息"
            path="http://10.61.23.149/daping/v2/index.html"
            type="条"
          /> -->
          <NumberList
            v-if="organType == 2"
            :number="pyCount"
            name="网络治理中心"
            nameOne="近一月辟谣信息"
            path=""
            type="条"
          />
        </div>
      </div>

      <!-- 中部内容 -->
      <div class="content">
        <!-- 今日涉济重点舆情 -->
        <div class="today">
          <!-- 标题 -->
          <div class="today-header positionNew">
            <span class="today-title">涉济重点舆情</span>
          </div>

          <!-- 列表 -->
          <div class="today-list">
            <MarqueeList
              v-if="dataList.length > 0"
              :items="dataList"
              @goDetail="goPath"
            />
          </div>
        </div>

        <!-- 实时工作动态 -->
        <div class="work">
          <!-- 标题 -->
          <div
            class="today-header positionNew"
            style="top: 485px; left: 285px; padding-left: 126.94px;"
          >
            <span class="today-title">实时工作动态</span>
          </div>

          <!-- 列表 -->
          <div class="work-back positionNew" style="top: 523px;">
            <VerticalScrollList ref="vs" />
          </div>
        </div>

        <!-- 账号信息 -->
        <div class="account">
          <!-- 标题 -->
          <div class="account-header positionNew">
            <div
              class="today-title"
              style="padding-left: 34px; text-align: left;"
            >
              账号信息
            </div>
          </div>

          <div class="account-back">
            <!-- 登录账号及姓名 -->
            <div style="display: flex; margin-bottom: 46px;">
              <div class="backs" style="width: 280px;">
                <div style="text-align: center;">
                  <span
                    ><svg-icon
                      icon-class="门户-登录账号"
                      style="width: 40px; height: 41px;"
                  /></span>
                  <div class="backs-title">当前登录账号：</div>
                </div>
                <span class="backs-nubmer">{{ userAccount }}</span>
              </div>
              <div class="backs">
                <div style="text-align: center; width: 87px;">
                  <span
                    ><svg-icon
                      icon-class="门户-姓名"
                      style="width: 40px; height: 41px;"
                  /></span>
                  <div class="backs-title">姓名：</div>
                </div>
                <span class="backs-nubmer">{{ userName }}</span>
              </div>
            </div>

            <!-- 今日登录次数及累计登录占比 -->
            <div style="display: flex;">
              <div class="backs" style="width: 266px;">
                <div style="text-align: center;">
                  <span
                    ><svg-icon
                      icon-class="门户-今日登录次数"
                      style="width: 40px; height: 41px;"
                  /></span>
                  <div class="backs-title">今日操作次数：</div>
                </div>
                <span class="backs-nubmer">{{ todayLoginNum }}次</span>
              </div>
              <div class="backs" style="">
                <div style="text-align: center; width: 116px;">
                  <span
                    ><svg-icon
                      icon-class="门户-累计登录"
                      style="width: 40px; height: 41px;"
                  /></span>
                  <div class="backs-title">累计操作次数：</div>
                </div>
                <span class="backs-nubmer">{{ loginNum }}次</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 常用功能 -->

        <div class="feature">
          <!--标题 -->
          <div
            class="account-header positionNew"
            style="top: 485px; right: 281px;"
          >
            <div
              class="today-title"
              style="padding-left: 40px; text-align: left;"
            >
              常用系统
            </div>
          </div>

          <div class="feature-back positionNew" style="top: 523px;">
            <div
              v-for="itv in featureList.slice((centerFeature - 1) * 6, centerFeature * 6)"
              :key="itv.id"
              class="feature-backs"
              @click="goPath(itv)"
              v-show="featureListShow"
            >
              <span class="backs-svg">
                <svg-icon
                  :icon-class="itv.iconName"
                  :style="{ width: itv.width, height: itv.height }"
                  class="backs-svgNew"
                />
              </span>
              <span class="backs-name">{{ itv.name }}</span>
              <svg-icon
                class="backs-svgNew"
                icon-class="门户-功能底座"
                style="width: 121.1px; height: 87px;"
              />
            </div>
            <div class="ToggleFeatureBox">
            <div
              v-for="i in Math.ceil(featureList.length / 6)"
              :key="i"
              :class="['item', centerFeature === i ? 'active' : '']"
              @click="centerFeature = i"
            ></div>
          </div>
          </div>
        </div>
      </div>

      <!-- 系统开通账号数及当前在线人数、系统使用top榜-->
      <div class="positionNew systemZong">
        <!--系统开通账号数及当前在线人数  -->
        <div style="display: flex;">
          <div class="system-account">
            <svg-icon
              icon-class="门户-系统账号数"
              style="width: 50px; height: 46px; margin-right: 12px;"
            />
            开通账号数：<span
              class="system-nubmer"
              style="cursor: pointer;"
              @click="JumpDetails"
              >{{ userAccountCount }}</span
            >
          </div>

          <div class="system-account">
            <svg-icon
              icon-class="门户-系统在线人数"
              style="width: 50px; height: 46px; margin-right: 12px;"
            />
            在线账号数：<span class="system-nubmer">{{ userOnLineCount }}</span>
          </div>
        </div>
        <!-- 访问量排行 -->

        <div style="display: flex;">
          <div class="system-account">
            <svg-icon
              icon-class="总访问量"
              style="width: 50px; height: 46px; margin-right: 12px;"
            />
            总访问量：<span
              class="system-nubmer"
              style="cursor: pointer;"
              @click="JumpDetails"
              >{{ interviewCount ? formatNumber(interviewCount) : 0 }}</span
            >
          </div>

          <div class="system-account" style="max-width: 346px; width: auto;">
            <svg-icon
              icon-class="今日访问量"
              style="width: 50px; height: 46px; margin-right: 12px;"
            />
            今日访问量：<span class="system-nubmer">{{
              todayInterviewCount ? todayInterviewCount : 0
            }}</span>
          </div>
        </div>

        <!-- 系统使用top榜单  未接入暂时隐藏
        <div class="system-account" style="width: 582.18px">
          <svg-icon
            icon-class="门户-系统在线人数"
            style="width: 50.18px; height: 46px; margin-right: 12px"
          />
          系统使用 TOP榜：
          <span style="margin-right: 7px">
            <svg-icon
              icon-class="门户-top1"
              style="width: 21.76px; height: 16.32px; margin-right: 7px"
            />张三<span class="system-nubmer">100</span>次</span
          >
          <span style="margin-right: 7px"
            ><svg-icon
              icon-class="门户-top2"
              style="width: 21.76px; height: 16.32px; margin-right: 7px"
            />李四<span class="system-nubmer">90</span>次</span
          >
          <span>
            <svg-icon
              icon-class="门户-top3"
              style="width: 21.76px; height: 16.32px; margin-right: 7px"
            />王二<span class="system-nubmer">80</span>次</span
          >
        </div> -->
      </div>
      <!-- 地图 -->
      <template v-if="centerStatus === 2">
        <div class="circle"></div>
        <div
          class="circle"
          style="animation-delay: 2s; animation-duration: 7s;"
        ></div>
        <div class="earts"></div>
        <div
          v-for="k in bubbingList"
          :key="k.id"
          :class="{ showBubbling: echartsIndex == k.id }"
          :style="{ top: k.top, left: k.left }"
          class="bubbling"
        >
          <svg-icon
            :icon-class="k.name"
            :style="{ width: k.width, height: k.height }"
          />

          <div
            :style="{ left: k.popLeft, top: k.popTop }"
            class="bubbling-modal"
          >
            <div
              :class="{ nodata: !k.echartData || !k.echartData.mpublishTime }"
              :style="
                k.name == '商河县'
                  ? 'top: 150px;position: absolute;left:-40px;'
                  : ''
              "
              class="modals"
              @click="goJcDetails(k.echartData)"
              @mouseenter="onMouseEnter"
              @mouseleave="onMouseLeave"
            >
              <p
                style="
                  font-weight: 600;
                  margin-bottom: 5px;
                  min-width: 300px;
                  max-width: 400px;
                  word-break: break-all;
                  display: -webkit-box;
                  -webkit-line-clamp: 3;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
              >
                {{
                  k.echartData.mtitle
                    ? k.echartData.mtitle
                    : k.echartData.mcontent
                }}
              </p>
              <p
                style="
                  margin-bottom: 5px;
                  max-width: 300px;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                "
              >
                {{ situationJSON[k.echartData.situation] }}-{{
                  k.echartData.uname
                    ? k.echartData.uname
                    : k.echartData.mwebsiteName
                }}
              </p>
              <p>
                时间：{{ moment(k.echartData.mpublishTime).format("HH:mm:ss") }}
              </p>
            </div>
            <div style="width: 189px; height: 94px;">
              <!-- 兼容样式 占位 -->
            </div>
            <!-- <div class="modals-number" :style="k.name == '南部山区'?'margin:-6px auto;':''">
              <span style="color: #ff9d00; font-size: 14px;">{{
                k.number
              }}</span>
            </div> -->
            <div class="modals-yuan"></div>
            <p
              class="float-name"
              style="text-align: center; color: #fff; font-size: 12px;"
            >
              {{ k.name }}
            </p>
          </div>
        </div>
      </template>

      <HardwareInformation v-else class="HardwareInformation" />
      <div class="ToggleBox">
        <div
          v-for="i in 2"
          :key="i"
          :class="['item', centerStatus === i ? 'active' : '']"
          @click="centerStatus = i"
        ></div>
      </div>
      <!-- 底部路由导航 -->
      <div
        v-if="!footFlag"
        class="positionNew"
        style="
          top: 875px;
          display: flex;
          width: 100%;
          flex-wrap: nowrap;
          flex-direction: row;
          justify-content: space-evenly;
        "
      >
        <div
          v-for="item in navList"
          :key="item.id"
          :style="{ width: item.width, top: item.top }"
          class="foot-nav"
          @click="showFooter(item)"
        >
          <span
            :style="{ width: item.width, height: item.height }"
            class="foot-icon"
          >
            <svg-icon
              :icon-class="item.iconName"
              style="width: 100%; height: 100%;"
            />
          </span>
          <span :style="{ top: item.height }" class="foot-text">{{
            item.title
          }}</span>
        </div>
        <div>
          <Poptip confirm title="确定要退出登录吗？" transfer @on-ok="out()">
            <svg-icon
              icon-class="退出"
              style="height: 100%; width: 40px; cursor: pointer;"
            />
          </Poptip>
        </div>
      </div>
      <!-- 点击底部导航之后出现的子级模块 -->
      <div
        v-if="footFlag"
        class="positionNew"
        style="top: 906px; display: flex; align-items: center;"
      >
        <!-- 左侧一级模块 -->
        <div
          class="foot-nav"
          style="margin-left: 305.56px;"
          @click="footFlag = false"
        >
          <span class="foot-icon" style="width: 170px; height: 105px;">
            <svg-icon
              :icon-class="footIconName"
              style="width: 100%; height: 100%;"
            />
          </span>
          <span class="foot-text" style="top: 105px;">{{ footName }}</span>
        </div>

        <!--箭头-->
        <span style="margin: 0px 43.47px 0px 62.77px;">
          <svg-icon
            icon-class="门户-底部箭头"
            style="width: 48.59px; height: 38.72px;"
          />
        </span>
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: 700px;
            max-width: 970px;
          "
        >
          <div
            style="
              display: flex;
              min-width: 750px;
              justify-content: space-around;
            "
          >
            <div
              v-for="item in footList"
              :key="item.sysNo"
              class="foot-nav"
              @click="goPath(item)"
            >
              <span
                class="foot-icon"
                style="
                  width: 124.34px;
                  height: 105px;
                  left: 50%;
                  transform: translateX(-50%);
                "
              >
                <svg-icon
                  icon-class="门户-系统子级"
                  style="width: 100%; height: 100%;"
                />
              </span>
              <span class="foot-text" style="top: 85px;">{{
                item.sysName
              }}</span>
            </div>
          </div>

          <span class="returns" @click="footFlag = false">
            <svg-icon icon-class="returns" style="width: 100%; height: 100%;" />
          </span>
        </div>
      </div>
    </div>
    <form
      id="ticketmessage"
      action=""
      class="btn"
      method="post"
      target="_blank"
    >
      <input id="fortheticketvalue" name="token" type="hidden" value="" />
      <input id="to" name="to" type="hidden" value="" />
    </form>
    <PopWindow v-if="PopWindowModel" :type="popType" @close="close" />
  </div>
</template>

<script>
import situationJSON from "@/assets/json/situation.json";
import PopWindow from "./component/PopWindow";
import moment from "moment";
import $ from "jquery";
import IOdometer from "vue-odometer";
import "odometer/themes/odometer-theme-default.css";

import MarqueeList from "./component/MarqueeList.vue";
import VerticalScrollList from "./component/VerticalScrollList.vue";
import NumberList from "./component/NumberList.vue";
import md from "js-md5";
import HardwareInformation from "@/components/HardwareInformation";

const bubbingList = [
  {
    id: 0,
    width: "188px",
    height: "213px",
    top: "113px",
    left: "870px",
    popTop: "-25px",
    popLeft: "11px",
    name: "商河县",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 1,
    width: "228px",
    height: "231px",
    top: "236px",
    left: "831px",
    popTop: "-10px",
    popLeft: "-8px",
    name: "济阳区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 2,
    width: "107px",
    height: "123px",
    top: "414px",
    left: "819px",
    popTop: "-98px",
    popLeft: "-30px",
    name: "天桥区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 3,
    width: "98px",
    height: "93px",
    top: "461px",
    left: "798px",
    popTop: "-120px",
    popLeft: "-56px",
    name: "槐荫区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 4,
    width: "91px",
    height: "87px",
    top: "476px",
    left: "873px",
    popTop: "-110px",
    popLeft: "-48px",
    name: "历下区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 5,
    width: "182px",
    height: "269px",
    top: "400px",
    left: "850px",
    popTop: "-52px",
    popLeft: "39px",
    name: "历城区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 6,
    width: "235px",
    height: "284px",
    top: "341px",
    left: "938px",
    popTop: "-5px",
    popLeft: "44px",
    name: "章丘区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 7,
    width: "150px",
    height: "113px",
    top: "500px",
    left: "803px",
    popTop: "-107px",
    popLeft: "-26px",
    name: "市中区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 8,
    width: "219px",
    height: "226px",
    top: "486px",
    left: "701px",
    popTop: "-26px",
    popLeft: "1px",
    name: "长清区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 9,
    width: "233px",
    height: "247px",
    top: "554px",
    left: "1000px",
    popTop: "-55px",
    popLeft: "5px",
    name: "莱芜区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 10,
    width: "130px",
    height: "161px",
    top: "674px",
    left: "1126px",
    popTop: "-69px",
    popLeft: "-33px",
    name: "钢城区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 11,
    width: "164px",
    height: "183px",
    top: "627px",
    left: "587px",
    popTop: "-56px",
    popLeft: "-17px",
    name: "平阴县",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 12,
    width: "200px",
    height: "183px",
    top: "330px",
    left: "835px",
    popTop: "-50px",
    popLeft: "-48px",
    name: "起步区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 13,
    width: "260px",
    height: "260px",
    top: "366px",
    left: "785px",
    popTop: "-10px",
    popLeft: "118px",
    name: "高新区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
  {
    id: 14,
    width: "164px",
    height: "183px",
    top: "514px",
    left: "860px",
    popTop: "-56px",
    popLeft: "-17px",
    name: "南部山区",
    number: 0,
    echartData: {}, //冒泡方框数据
  },
];

const CryptoJS = require("crypto-js"); // 引用AES源码js
// const key = CryptoJS.enc.Utf8.parse("ADF2081720D2A3F1"); // 十六位十六进制数作为密钥
// const iv = CryptoJS.enc.Utf8.parse("A2B46F813D55E34C"); //十六位十六进制数作为密钥偏移量

export default {
  components: {
    PopWindow,
    IOdometer,
    MarqueeList,
    VerticalScrollList,
    NumberList,
    HardwareInformation,
  },

  data() {
    return {
      organType: null,
      pauseId: null,
      centerStatus: 1,
      PopWindowModel: false, //弹窗状态
      popType: "1",
      previousETag: null, // 当前页tag版本
      situationJSON,
      bubbingList,
      promptCount: null, //提示单单数量
      sjyqCount: null, // 涉济舆情
      sjCount: null, // 通过字段“CATALOG_AGEA涉济南（含各区县）”统计当日所有舆论场信息总量
      jbCount: null, // 举报数量
      pyCount: null, // 辟谣数量
      userAccountCount: null, // 账号总数
      userOnLineCount: null, // 在线账号总数
      interviewCount: null, // 总访问量
      todayInterviewCount: null, // 今日访问量
      showIx: 0, // 显示的索引
      showWxIx: 0, // 显示的索引
      showWaIndex: 0, //网络安全中心数据显示索引
      showQix: 0,
      echartsIndex: -1, //地图展示图标
      // echartsIndex: 12, //地图展示图标
      promptDataList: [], //提示单数据列表
      webSentimentReportsList: [], // 网情要报列表
      networkBulletinList: [], // 网络通报列表
      xczlCount: null,
      jrmtCount: null,
      wxmtCount: null, //智慧网宣系统媒体主体
      wxxyCount: null, //智慧网宣系统媒体信源
      wxjcCount: null, //智慧网宣系统监测专题

      wpzlCount: null,
      aqzxCount: null,
      dataList: [], //涉济报送数据列表
      userAccount: "", //当前登陆账号
      userName: "", //当前登陆姓名
      footFlag: false, //底部导航展示子模块
      nowtime: new Date(), //获取当前时间
      featureListShow: false, // 功能列表展示
      featureList: [
        {
          id: 1,
          name: "G系统",
          width: "38px",
          height: "38px",
          iconName: "门户-涉济推荐",
          sysURL: "https://10.160.90.36:35008/",
          to: "",
          sysNo: "trs_gxt",
          windowOpen: true,
          moduleNmae: "门户屏/外部系统",
          logContent: "G系统",
          useNum: 0
        },
        {
          id: 2,
          name: "央办监测指挥",
          width: "40px",
          height: "38px",
          iconName: "门户-信息监测",
          sysNo: "trs_ybjczh",
          sysURL: "http://10.136.89.74/",
          to: "/main/monitor/message",
          windowOpen: true,
          moduleNmae: "门户屏/外部系统",
          logContent: "央办监测指挥",
          useNum: 0
        },
        {
          id: 3,
          name: "央办业务系统",
          width: "38px",
          height: "38px",
          iconName: "门户-网情要报",
          sysNo: "trs_ybywxt",
          sysURL: "https://10.161.17.70/",
          to: "",
          windowOpen: true,
          moduleNmae: "门户屏/外部系统",
          logContent: "央办业务系统",
          useNum: 0
        },
        {
          id: 4,
          name: "网信通",
          width: "40px",
          height: "41px",
          iconName: "门户-事件分析",
          sysNo: "trs_wxt",
          sysURL: "http://10.61.23.143:8002/webim/pc/#/chat",
          to: "",
          windowOpen: true,
          moduleNmae: "门户屏/外部系统",
          logContent: "网信通",
          useNum: 0
        },
        {
          id: 5,
          name: "Deepseek",
          width: "35px",
          height: "39px",
          iconName: "门户-舆情提示",
          sysNo: "sys_rzx_tmind",
          // sysURL: "http://192.168.20.6:8300/login?redirect=/index",
          sysURL: gl.isPad
                ? "http://192.168.20.6:8300/fssoLogin"
                : "http://10.61.23.222:8300/fssoLogin",
          to: "",
          //windowOpen: true,
          moduleNmae: "门户屏/外部系统",
          logContent: "Deepseek",
          useNum: 0
        },
        {
          id: 6,
          name: "网站管理",
          width: "40px",
          height: "40px",
          iconName: "门户-综合搜索",
          sysNo: "trs_wzgl",
          // sysURL: SYSTEM_CONFIG_XZ[0].sysURL,
          sysURL: " http://10.61.23.145/ids/websitecms.jsp",
          to: "",
          moduleNmae: "门户屏/外部系统",
          logContent: "网站管理",
          useNum: 0
        },
        {
          id: 7,
          name: "短信平台",
          width: "35px",
          height: "39px",
          iconName: "门户-舆情提示",
          sysNo: "trs_dxpt",
          sysURL: "http://10.61.23.142/user-auth-server/sso/v1/tokenValid",
          to: "",
          moduleNmae: "门户屏/外部系统",
          logContent: "短信平台",
          useNum: 0
        },
        {
          id: 8,
          name: "跨网文件传输",
          width: "35px",
          height: "39px",
          iconName: "门户-事件分析",
          sysNo: "sys_wjcs",
          sysURL: "http://10.61.23.235/front/api/v1/TRS/SSOLogin",
          to: "",
          moduleNmae: "门户屏/外部系统",
          logContent: "跨网传输",
          useNum: 0
        },
      ],
      centerFeature: 1,
      navList: [
        {
          id: 1,
          title: "网络舆情中心",
          iconName: "门户-网络舆情中心",
          logContent: "舆情中心二级",
          width: "170px",
          top: "28px",
          height: "105px",
          list: [...SYSTEM_CONFIG_XZ],
        },
        {
          id: 2,
          title: "网络传播中心",
          iconName: "门户-网络宣传中心",
          logContent: "传播中心大屏",
          width: "188px",
          top: "0px",
          height: "116px",
          list: [
            {
              sysNo: "trs_wx",
              sysName: "智慧网宣管理系统",
              sysURL: gl.isPad
                ? "https://192.168.20.6:10443/psystemjn/sys/ssatuologin?deviceType=1"
                : "https://10.61.23.226/psystemjn/sys/ssatuologin?deviceType=0",
              iconName: "门户-网络宣传中心",
              moduleNmae: "门户屏/系统入口",
              logContent: "网宣管理系统",
            },
            {
              sysNo: "sys_code_wp",
              sysName: "网评引导系统",
              sysURL: gl.isPad
                ? "https://192.168.20.6:18443/packstage"
                : "https://10.61.23.216:18443/ly/api/v2/login",
              iconName: "门户-网络宣传中心",
              moduleNmae: "门户屏/系统入口",
              logContent: "网评引导系统",
            },
          ],
        },
        {
          id: 3,
          title: "网络安全中心",
          iconName: "门户-网络安全中心",
          width: "170px",
          top: "13px",
          height: "105px",
          sysNo: "wa_xtzhpt",
          sysURL: gl.isPad
            ? "https://192.168.20.6:11443/ext/api/third/login_pad"
            : "https://************/ext/api/third/login",
          moduleNmae: "门户屏/系统入口",
          to: "NetworkSecurityScreenJinanNew",
          //windowOpen: true,
          logContent: "网安指挥平台",
          // list: [
          //   {
          //     sysNo: "wa_xtzhpt",
          //     sysName: "网络安全协调指挥系统",
          //     sysURL:
          //       (gl.isPad
          //         ? "https://192.168.20.6:12443"
          //         : "https://************") + "/ext/api/third/login",
          //     "moduleNmae": "门户屏/系统入口",
          //     "logContent": "网安指挥平台",
          //   },
          //   {
          //     sysNo: "wa_xtzhptx",
          //     sysName: "网络安全协调指挥系统(新)",
          //     sysURL: gl.isPad
          //       ? "https://192.168.20.6:11443/ext/api/third/login_pad"
          //       : "https://************/ext/api/third/login",
          //       to: "/home/<USER>/NetworkSecurityScreenJinan",
          //     "moduleNmae": "门户屏/系统入口",
          //     "logContent": "网安新平台",
          //   },
          // ],
        },
        {
          id: 4,
          title: "网络治理中心",
          iconName: "门户-网络管理中心",
          logContent: "举报平台大屏",
          width: "188px",
          top: "0px",
          height: "116px",
          list: [
            {
              sysNo: "trs_xxjb",
              sysName: "违法和不良信息举报系统",
              sysURL: gl.isPad
                ? "http://192.168.20.6:8085"
                : "http://10.61.23.149/",
              windowOpen: true,
              moduleNmae: "门户屏/系统入口",
              logContent: "举报系统",
            },
            {
              sysNo: "trs_lhpy",
              sysName: "互联网联合辟谣系统",
              sysURL: gl.isPad
                ? "http://192.168.20.6:8086"
                : "http://10.61.23.149:81/",
              windowOpen: true,
              moduleNmae: "门户屏/系统入口",
              logContent: "辟谣系统",
            }
          ],
        },
        {
          id: 6,
          title: "数字产业中心",
          iconName: "门户-网络数字产业中心",
          width: "170px",
          top: "28px",
          height: "105px",
          // list: [],
          sysNo: "wa_xtzhpt",
          windowOpen: true,
          sysURL: "http://10.61.23.236:85/government/index_offiine.html",
        },
        {
          id: 5,
          title: "网络指挥中心",
          iconName: "门户-网络数据中心",
          width: "170px",
          top: "28px",
          height: "105px",
          list: [{
              sysNo: "trs_wx",
              sysName: "应急管理系统",
              sysURL: "http://10.61.23.136:31288/yj/",
              iconName: "门户-网络宣传中心",
              moduleNmae: "门户屏/系统入口",
              logContent: "应急管理系统",
            },
            {
              sysNo: "sys_code_wp",
              sysName: "网信执法系统",
              sysURL: "http://10.61.23.136:31289/wxzf/",
              iconName: "门户-网络宣传中心",
              moduleNmae: "门户屏/系统入口",
              logContent: "网信执法系统",
            }],
        },
      ], // 底部导航
      navList2: [
        {
          id: 1,
          title: "网络舆情中心",
          iconName: "门户-网络舆情中心",
          logContent: "舆情中心二级",
          width: "170px",
          top: "28px",
          height: "105px",
          list: [...SYSTEM_CONFIG_XZ],
        },
        {
          id: 2,
          title: "网络传播中心",
          iconName: "门户-网络宣传中心",
          logContent: "传播中心大屏",
          width: "188px",
          top: "0px",
          height: "116px",
          list: [
            {
              sysNo: "trs_wx",
              sysName: "智慧网宣管理系统",
              sysURL: gl.isPad
                ? "https://192.168.20.6:10443/psystemjn/sys/ssatuologin?deviceType=1"
                : "https://10.61.23.226/psystemjn/sys/ssatuologin?deviceType=0",
              iconName: "门户-网络宣传中心",
              moduleNmae: "门户屏/系统入口",
              logContent: "网宣管理系统",
            },
            {
              sysNo: "sys_code_wp",
              sysName: "网评引导系统",
              sysURL: gl.isPad
                ? "https://192.168.20.6:18443/packstage"
                : "https://10.61.23.216:18443/ly/api/v2/login",
              iconName: "门户-网络宣传中心",
              moduleNmae: "门户屏/系统入口",
              logContent: "网评引导系统",
            },
          ],
        },
        {
          id: 3,
          title: "网络安全中心",
          iconName: "门户-网络安全中心",
          width: "170px",
          top: "13px",
          height: "105px",
          sysNo: "wa_xtzhpt",
          sysURL: gl.isPad
            ? "https://192.168.20.6:11443/ext/api/third/login_pad"
            : "https://************/ext/api/third/login",
          moduleNmae: "门户屏/系统入口",
          to: "NetworkSecurityScreenJinanNew",
          //windowOpen: true,
          logContent: "网安指挥平台",
          // list: [
          //   {
          //     sysNo: "wa_xtzhpt",
          //     sysName: "网络安全协调指挥系统",
          //     sysURL:
          //       (gl.isPad
          //         ? "https://192.168.20.6:12443"
          //         : "https://************") + "/ext/api/third/login",
          //     "moduleNmae": "门户屏/系统入口",
          //     "logContent": "网安指挥平台",
          //   },
          //   {
          //     sysNo: "wa_xtzhptx",
          //     sysName: "网络安全协调指挥系统(新)",
          //     sysURL: gl.isPad
          //       ? "https://192.168.20.6:11443/ext/api/third/login_pad"
          //       : "https://************/ext/api/third/login",
          //       to: "/home/<USER>/NetworkSecurityScreenJinan",
          //     "moduleNmae": "门户屏/系统入口",
          //     "logContent": "网安新平台",
          //   },
          // ],
        },
        {
          id: 4,
          title: "网络治理中心",
          iconName: "门户-网络管理中心",
          logContent: "举报平台大屏",
          width: "188px",
          top: "0px",
          height: "116px",
          list: [
            {
              sysNo: "trs_xxjb",
              sysName: "违法和不良信息举报系统",
              sysURL: gl.isPad
                ? "http://192.168.20.6:8085"
                : "http://10.61.23.149/",
              windowOpen: true,
              moduleNmae: "门户屏/系统入口",
              logContent: "举报系统",
            },
            {
              sysNo: "trs_lhpy",
              sysName: "互联网联合辟谣系统",
              sysURL: gl.isPad
                ? "http://192.168.20.6:8086"
                : "http://10.61.23.149:81/",
              windowOpen: true,
              moduleNmae: "门户屏/系统入口",
              logContent: "辟谣系统",
            },
            {
              sysNo: "trs_xhpy",
              sysName: "智慧辟谣系统",
              sysURL: gl.isPad
                ? "http://10.61.23.149:83/admin/loginByTrs"
                : "http://10.61.23.149:83/admin/loginByTrs",
              moduleNmae: "门户屏/系统入口",
              logContent: "辟谣系统",
            },
          ],
        },
        {
          id: 6,
          title: "数字产业中心",
          iconName: "门户-网络数字产业中心",
          width: "170px",
          top: "28px",
          height: "105px",
          // list: [],
          sysNo: "wa_xtzhpt",
          windowOpen: true,
          sysURL: "http://10.61.23.236/government/index_offline.html",
        },
        {
          id: 5,
          title: "网络指挥中心11",
          iconName: "门户-网络数据中心",
          width: "170px",
          top: "28px",
          height: "105px",
          list: [],
        },
      ],
      footList: [], //导航子级模块
      footName: "", //子级模块外层
      footIconName: "", //外层icon
      wzNumber: {},
      todayLoginNum: 0,
      loginNum: 0,
      isPad: false,

      todaySjCount: 0,
      markingSjCount: 0,
      stopFlag: false,
    };
  },
  //生命周期 - 创建完成（访问当前this实例）
  created() {
    this.organType = localStorage.getItem("organType");
    this.isPad = gl.isPad;
    if (localStorage.getItem("userAccount") == "test2" ||
    localStorage.getItem("userAccount") == "qianhongguo1" ||
    localStorage.getItem("userAccount") == "zhuning") {
      this.navList = this.navList2;
    }
  },
  //方法所在
  methods: {
    // 获取底部所有数据
    getAllUsed() {
      this.$http.get(gl.serverURL + "/behavior/allUsed").then((res) => {
        let data = res.body.data;
        this.interviewCount = data.totalNum; // 总访问量
        this.todayInterviewCount = data.todayNum; // 今日访问量
        this.userOnLineCount = data.onlineCount; // 总访问量
        this.userAccountCount = data.userCount; // 今日访问量
      });
    },
    getSystemUseNum() {
      this.$http.get(gl.serverURL + "/behavior/getSystemUseNum").then((res) => {
        let data = res.body.data;
        if (data){
          this.featureList.forEach(feature => {
            if (data[feature.logContent]) {
              feature.useNum = data[feature.logContent];
            }
          });
          this.featureList.sort((a, b) => {
            return b.useNum - a.useNum;
          });
        }
        this.featureListShow = true;
      });
    },
    JumpDetails() {
      const { href } = this.$router.resolve({
        path: "/ControlsDetails",
      });
      this.getLog("门户屏/内部系统", '跳转');
      window.open(href, "_blank");
    },
    moment,

    openPop() {
      this.PopWindowModel = true;
    },
    close() {
      this.PopWindowModel = false;
    },
    goJcDetails(val) {
      let data = {
        sysURL: SYSTEM_CONFIG_XZ[0].sysURL,
        to: "/main/details?situation=" + val.situation + ";msgKey=" + val.mkey,
        sysNo: "trs_jccz",
      };
      this.goPath(data);
    },
    onMouseEnter() {
      this.stopFlag = true;
    },
    onMouseLeave() {
      this.stopFlag = false;
      clearInterval(this.timerNew);
      this.getEchartsData();
    },
    //获取账号今日登录次数
    getLoginNubmer() {
      this.$http.get(gl.serverURL + "/behavior/getUserLogin").then((res) => {
        let data = res.body;
        if (data.status == 0) {
          this.todayLoginNum = data.data.todayLoginNum;
          this.loginNum = data.data.loginNum;
        }
      });
    },
    // 网络攻击数获取
    getGjNumber() {
      this.$http.get(gl.wlgjCountAPI).then((response) => {
        let data = response.body;
        this.wzNumber.gjCount = data.count ? data.count : 0;
      });
    },
    // 网安获取单位数
    getUnitCount() {
      let params = {
        user: "7855d7cd00000",
        timestamp: new Date().getTime(),
        sign: md(new Date().getTime() + "7855d7cd00000jw8wp1qbub"),
        systemUser: "admin",
        data: {
          pageNo: 1,
          pageSize: 1,
          result: [],
          total: 1,
          totalPages: 1,
        },
      };
      this.$http
        .post(gl.unitCountAPI, params, { emulateJSON: false })
        .then((response) => {
          let result = response.body;
          this.wzNumber.unitCount = result.data.total;
        });
    },
    // 网安获取系统数
    getSystemCount() {
      let params = {
        user: "7855d7cd00000",
        timestamp: new Date().getTime(),
        sign: md(new Date().getTime() + "7855d7cd00000jw8wp1qbub"),
        data: {
          pageNo: 1,
          pageSize: 1,
          total: 1,
          totalPages: 1,
        },
      };
      this.$http
        .post(gl.systemCountAPI, params, { emulateJSON: false })
        .then((response) => {
          let result = response.body;
          this.wzNumber.systemCount = result.data.total;
        });
    },

    // 网安获取ip资产数
    getIpCount() {
      let params = {
        user: "7855d7cd00000",
        timestamp: new Date().getTime(),
        sign: md(new Date().getTime() + "7855d7cd00000jw8wp1qbub"),
        data: {},
      };
      this.$http
        .post(gl.ipCountAPI, params, { emulateJSON: false })
        .then((response) => {
          let result = response.body;
          this.wzNumber.ipCount = result.data.total;
        });
    },

    // 网安获取安全事件数
    getAqCount() {
      let params = {
        user: "7855d7cd00000",
        timestamp: new Date().getTime(),
        sign: md(new Date().getTime() + "7855d7cd00000jw8wp1qbub"),
        data: {
          systemUser: "admin",
          pageNo: 1,
          pageSize: 100,
          startTime: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
          endTime: moment(new Date()).format("YYYY-MM-DD ") + "23:59:59",
          result: [],
          total: "1",
          totalPages: "1",
        },
      };
      this.$http
        .post(gl.aqCountAPI, params, { emulateJSON: false })
        .then((response) => {
          let result = response.body;
          this.wzNumber.aqCount = result.data.total;
        });
    },

    // 网安获取风险事件数
    getFxCount() {
      let params = {
        user: "7855d7cd00000",
        timestamp: new Date().getTime(),
        sign: md(new Date().getTime() + "7855d7cd00000jw8wp1qbub"),
        data: {
          startEndTime: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
          endEndTime: moment(new Date()).format("YYYY-MM-DD ") + "23:59:59",
        },
      };
      this.$http
        .post(gl.fxCountAPI, params, { emulateJSON: false })
        .then((response) => {
          let result = response.body;
          this.wzNumber.fxCount = result.data.total;
        });
    },

    // 网安获取安全日志数
    getAqrzCount() {
      this.$http.get(gl.aqrzCountAPI).then((response) => {
        let data = response.body;
        this.wzNumber.aqrzCount = data.count ? data.count : 0;
      });
    },

    // 网安获取安全通报数
    getAqtbCount() {
      console.log();

      let params = {
        user: "7855d7cd00000",
        timestamp: new Date().getTime(),
        sign: md(new Date().getTime() + "7855d7cd00000jw8wp1qbub"),
        data: {
          createdAtLower: moment().add(-29, "d").format("YYYY-MM-DD 00:00:00"),
          createdAtUpper: moment(new Date()).format("YYYY-MM-DD ") + "23:59:59",
        },
      };
      this.$http
        .post(gl.aqtbCountAPI, params, { emulateJSON: false })
        .then((response) => {
          let result = response.body;
          this.wzNumber.aqtbCount = result.data.total;
        });
    },

    // 网安 获取通报列表数
    getTbList() {
      let params = {
        user: "7855d7cd00000",
        timestamp: new Date().getTime(),
        sign: md(new Date().getTime() + "7855d7cd00000jw8wp1qbub"),
        data: {
          pageNo: 1,
          pageSize: 20,
        },
      };
      this.$http
        .post(gl.tbListAPI, params, { emulateJSON: false })
        .then((response) => {
          let result = response.body;
          let list = result.data.result;
          if (list && list.length > 0) {
            list.forEach((item) => {
              item.createTime = new Date(item.createdAt);
            });
          }
          this.networkBulletinList.splice(0);
          this.networkBulletinList.push(...list);
          this.realtimeDataMerge();
        });
    },

    getEchartsData() {
      if (this.stopFlag) {
        return;
      }
      let nextIndex =
        this.echartsIndex + 1 >= this.bubbingList.length
          ? 0
          : this.echartsIndex + 1;

      let time = new Date().getTime();
      if (
        this.bubbingList[nextIndex] &&
        this.bubbingList[nextIndex].echartData &&
        this.bubbingList[nextIndex].echartData.oldTime &&
        time - this.bubbingList[nextIndex].echartData.oldTime < 5 * 60 * 1000
      ) {
        this.echartsIndex = nextIndex;
        this.timerNew = setTimeout(() => this.getEchartsData(), 10 * 1000);
        return;
      }

      let params = {
        area: this.bubbingList[nextIndex].name,
      };
      this.$http
        .get(gl.jcczAPI + "/home/<USER>", { params })
        .then((res) => {
          let data = res.body;
          if (data.status == 0) {
            // 原先逻辑
            this.bubbingList[nextIndex].echartData = data.data ? data.data : {};
            this.bubbingList[
              nextIndex
            ].echartData.oldTime = new Date().getTime();
          }
        })
        .finally(() => {
          this.echartsIndex = nextIndex;
          this.timerNew = setTimeout(() => this.getEchartsData(), 10 * 1000);
          // this.timerNew = setTimeout(() => this.getEchartsData(), 10 * 100000);
        });
    },

    getEcharts() {
      this.$http.get(gl.jcczAPI + "/home/<USER>").then((res) => {
        let data = res.body;
        if (data.status == 0) {
          let list = data.data;
          this.bubbingList.forEach((v) => {
            v.number = list[v.name];
          });
        }
        setTimeout(() => this.getEcharts(), 5 * 60 * 1000);
      });
    },
    // 获取当前日期星期
    getModay(id) {
      let str = "";
      if (id == 0) {
        str = "星期日";
      } else if (id == 1) {
        str = "星期一";
      } else if (id == 2) {
        str = "星期二";
      } else if (id == 3) {
        str = "星期三";
      } else if (id == 4) {
        str = "星期四";
      } else if (id == 5) {
        str = "星期五";
      } else if (id == 6) {
        str = "星期六";
      }

      return str;
    },

    // 点击底部路由
    showFooter(item) {
      console.log(item);
      if (item.title === "网络安全中心" || item.title === "数字产业中心") {
        this.goPath(item);
        return;
      }
      if (item.logContent) {
        this.getLog("门户屏/大屏跳转", item.logContent);
      }
      this.footList = item.list;
      this.footName = item.title;
      this.footIconName = item.iconName;
      if (item.list == 0) {
        return;
      } else {
        this.footFlag = !this.footFlag;
      }
    },
    // 加密方法
    Encrypt(word) {
      let time = new Date().getTime();
      let words = "szwxdj-" + time;
      let encryptedData = CryptoJS.AES.encrypt(
        words,
        CryptoJS.enc.Utf8.parse("0oVQCagdpC2PpASi"),
        { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 }
      ).toString();
      return encryptedData;
    },
    goNewPath(toUrl) {
      let url = "https://************/api/thirdPart/auth";
      url += "?token=" + this.Encrypt();
      url += "&redirectUrl=" + toUrl;
      return url;
    },
    getMsgData(data) {
      let param = {
        sign: data,
      };
      this.$http
        .post(gl.jcczAPI + "/login/user", param, { emulateJSON: true })
        .then((res) => {
          if (res.body.status == 0) {
            localStorage.setItem("userId", res.body.data.userId);
            localStorage.setItem("userName", res.body.data.userName);
            localStorage.setItem("organName", res.body.data.organName);
            localStorage.setItem("organId", res.body.data.organId);
            localStorage.setItem("iphone", res.body.data.userTelephone);
            localStorage.setItem("userAccount", res.body.data.userAccount);
            localStorage.setItem(
              "resources",
              JSON.stringify(res.body.data.resource)
            );
            localStorage.setItem(
              "sysNos",
              res.body.data.sysNos ? res.body.data.sysNos.toString() : ""
            );
            localStorage.setItem(
              "roleName",
              res.body.data.roleName ? res.body.data.roleName : ""
            );
            gl.loginU = {
              userId: res.body.data.userId,
              userName: res.body.data.userName,
              organId: res.body.data.organId,
              organName: res.body.data.organName,
              userAccount: res.body.data.userAccount,
              resources: res.body.data.resource,
              sysNos: res.body.data.sysNos,
            };
            localStorage.setItem(
              "departmentName",
              res.body.data.departmentName
            );
            localStorage.setItem("departmentId", res.body.data.departmentId);
            localStorage.setItem("browser", res.body.data.browser);
            localStorage.setItem("ip", res.body.data.ip);
            localStorage.setItem("tokens", res.body.data.token);
          } else {
            localStorage.removeItem("userId");
            localStorage.removeItem("userName");
            localStorage.removeItem("organName");
            localStorage.removeItem("organId");
            localStorage.removeItem("organName");
            localStorage.removeItem("userAccount");
            localStorage.removeItem("resources");
            localStorage.removeItem("sysNos");
            localStorage.removeItem("iphone");
            localStorage.removeItem("roleName");
            localStorage.removeItem("tokens");
            gl.loginU = {};
          }
        });
    },
    async goPath(item) {
      debugger
      if (
        this.organType == 2 &&
        item.sysNo != "trs_sdzb" &&
        item.sysNo != "trs_gxt" &&
        item.sysNo != "trs_wx" &&
        item.sysNo != "trs_ybjczh" &&
        item.sysNo != "trs_ybywxt" &&
        item.sysNo != "trs_wxt" &&
        item.sysNo != "trs_dxpt" &&
        item.sysNo != "trs_wzgl" &&
        item.sysNo != "wa_xtzhpt" &&
        item.sysNo != "wa_xtzhptx"&&
        item.sysNo != "sys_wjcs"
      ) {
        this.$Message.error("您没有权限访问该系统");
        return;
      }

      if (!item.sysURL) {
        return;
      }
      if (item.windowOpen) {
        if (item.sysNo == "trs_wxt") {
          let params = {
            iphone: localStorage.getItem("iphone"),
          };
          await this.$http
              .get(gl.jcczAPI + "/common/message", { params })
              .then((res) => {
                console.log(res);
                if (res.body.data) {
                  item.sysURL = res.body.data;
                }
              });
        }
        if (item.moduleNmae && item.logContent) {
          this.getLog(item.moduleNmae, item.logContent);
        }
        window.open(item.sysURL);
        return;
      }

      if (item.sysNo == "trs_wzgl") {
        $("#fortheticketvalue").val(localStorage.getItem("userAccount"));
          if (!item.to) {
            item.to = "";
          }
          $("#to").val(item.to);
          $("#ticketmessage").attr("action", item.sysURL);
          $("#ticketmessage").submit();
          this.getLog(item.moduleNmae, item.logContent);
          return;
      }
      let url = gl.serverURL + "/tokenMsg";
      let params = { systemNo: item.sysNo, to: item.to };
      this.$http.get(url, { params: params }).then((response) => {
        let data = response.body;
        if (data.status == 0 && data.data) {
          let url = item.sysURL;
          if (item.sysNo == "sys_code_wp" && gl.isPad) {
            url = url + "?token=" + data.data.token;
          }
          if (item.moduleNmae && item.logContent) {
            this.getLog(item.moduleNmae, item.logContent);
          }
          $("#fortheticketvalue").val(data.data.token);
          if (!item.to) {
            item.to = "";
          }
          $("#to").val(item.to);
          $("#ticketmessage").attr("action", url);
          $("#ticketmessage").submit();
        } else {
          this.$Message.error({
            content: data.message,
            duration: 3,
            closable: true,
          });
        }
      });
    },
    //获取提示单统计信息
    getSjImportYqList() {
      this.$http.get(gl.jcczAPI + "/common/sjImportYq").then((res) => {
        let data = res.body;
        if (data.status == 0) {
          let arr = [];
          if (data.data && data.data.length > 0) {
            data.data.forEach((i, index) => {
              let obj = {
                msgTitle: i.mtitle,
                createTime: i.insertTime,
                msgContent: i.mabstract,
                type: 1,
                msgUname: i.uname,
                msgSiution: i.situation,
                mwebsiteName: i.mwebsiteName,
                msentiment: i.msentiment,
                scopeArea: i.scopeArea,
                mkey: i.mkey,
                mpublishTime: i.mpublishTime,
              };
              arr.push(obj);
            });
          }
          this.dataList = arr;
          // this.getInfoList();
        }
      });
      // this.getPromptList(1);//查询提示单当天数据-头部信息
      //         this.getSjyqCount(); 涉济舆情暂时关闭
      this.getPromptList(0);
      this.getJbCount();
      this.getPyCount();
      // this.getUserAccountCount(); //账号总数
      // this.getUserOnLineCount(); //在线人数
      setTimeout(() => this.getSjImportYqList(), 3 * 60 * 1000);
    },
    // 实时数据列表
    realtimeDataMerge() {
      // 合并三个列表
      const combinedList = [
        ...this.promptDataList,
        ...this.networkBulletinList,
      ];

      // 根据createTime排序，这里假设createTime是每个对象中的一个属性
      // 使用Array.prototype.sort进行排序，注意转换为数字进行比较
      combinedList.sort((a, b) => Number(b.createTime) - Number(a.createTime));

      // 取最新的20条数据
      this.$refs.vs.changeData(combinedList.slice(0, 20));
    },
    //获取提示单统计信息
    getPromptList(timeType) {
      let params = {
        timeType: timeType, //时间类型 0:全部,1:当天,3:近三天,7:近一周,30:近一月,-1:自定义
        status: -1, //提示状态(-1:全部,2:未接收,3:已接收,4:已反馈,5:已退回,6:已完结)
      };
      this.$http.get(gl.jcczAPI + "/prompt/findAll", { params }).then((res) => {
        let data = res.body;

        if (data.status == 0) {
          if (timeType == 1) {
            //查询为当天数据，只为头部提示单数量赋值
            this.promptCount = data.data.count;
          } else {
            this.promptDataList.splice(0);
            this.promptDataList.push(...data.data.data);
            this.realtimeDataMerge();
          }
        }
      });
    },
    // getUserOnLineCount() {
    //   let url = gl.serverURL + "/sys/user/getOnLineCount";
    //   this.$http.get(url).then((response) => {
    //     let data = response.body;
    //     if (data.status == 0) {
    //       this.userOnLineCount = data.data;
    //     }
    //   });
    // },
    // getUserAccountCount() {
    //   let url = gl.serverURL + "/sys/user/list";
    //   let params = {
    //     organId: 0,
    //     pageNo: 1,
    //     pageSize: 20,
    //   };

    //   this.$http.get(url, { params: params }).then((response) => {
    //     let data = response.body;
    //     if (data.status == 0) {
    //       this.userAccountCount = data.data.count;
    //     }
    //   });
    // },
    getSjyqCount() {
      let pageSize = 20;
      let params = {
        sysNo: 2, //区分系统 1.sd，2jc
        orderBy: 1, //排序字段 1:报送时间 2:待定
        orderType: "desc", //排序方式 desc/asc
        pageNo: 1, //分页页码
        pageSize: pageSize, //分页条数
        source: 2, //1 涉鲁 2 涉济
        timeType: 0, //0 当天 1三天 -1自定义
      };
      this.$http.get(gl.sdzbAPI + "/info/infoList", { params }).then((res) => {
        let result = res.body;
        if (result.status === 0) {
          console.log(result, "result");
          this.sjyqCount = result.data.count;
        }
      });
    },
    getJbCount() {
      this.$http.get(gl.jbCountAPI).then((response) => {
        let data = response.body; // {"today":0,"sevenday":17}
        this.jbCount = data.sevenday * 1;
      });
    },
    getPyCount() {
      this.$http.get(gl.pyCountAPI).then((response) => {
        let data = response.body; // {"today": "0","thismonth": "22"}
        this.pyCount = data.thismonth * 1;
      });
    },
    getSjCount() {
      this.$http.get(gl.jcczAPI + "/common/sjMsgCount").then((response) => {
        let data = response.body;
        if (data.status === 0) {
          console.log(data, "result");
          this.sjCount = data.data.num24;
          this.todaySjCount = data.data.numToday;
          this.markingSjCount = data.data.numImport;
        }
      });
      setTimeout(() => this.getSjCount(), 3 * 60 * 1000);
    },
    //该数据所属模块暂时不要，所以数据相应不获取
    // getInfoList() {
    //   let pageSize = 10 - this.promptCount;
    //   let params = {
    //     sysNo: 2, //区分系统 1.sd，2jc
    //     orderBy: 1, //排序字段 1:报送时间 2:待定
    //     orderType: "desc", //排序方式 desc/asc
    //     pageNo: 1, //分页页码
    //     pageSize: pageSize, //分页条数
    //     source: 2, //1 涉鲁 2 涉济
    //     // timeType: 0, //0 当天 1三天 -1自定义
    //   };
    //   this.$http.get(gl.sdzbAPI + "/info/infoList", { params }).then((res) => {
    //     let result = res.body;
    //     if (result.status === 0) {
    //       console.log(result, "result");
    //       let arr = [];
    //       if (result.data.list && result.data.list.length > 0) {
    //         result.data.list.forEach((i, index) => {
    //           let obj = {
    //             msgTitle: i.msgTitle,
    //             createTime: i.msgPublishTime,
    //             msgContent: i.msgContent,
    //             type: 2,
    //             msgUname: i.msgUname,
    //             msgSiution: i.msgSiution,
    //           };
    //           arr.push(obj);
    //         });
    //         this.dataList = [...this.dataList, ...arr];
    //       }
    //     }
    //   });
    // },
    /**
     * 通过etag检查网页版本是否更新
     */
    fetchETagAndCompare() {
      const resourceUrl = window.location.href;

      this.$http
        .head(resourceUrl)
        .then((response) => {
          // 获取最新的ETag
          const currentETag = response.headers.get("ETag");
          if (this.previousETag == null) {
            console.log("save ETag:" + currentETag);
            this.previousETag = currentETag;
            return;
          }
          // 比较ETag是否改变
          if (currentETag !== this.previousETag) {
            console.log("ETag改变，系统版本有变化刷新页面...");
            // 刷新页面
            window.location.reload();
          } else {
            console.log("ETag is the same, no need to refresh.");
          }
        })
        .catch((error) => {
          console.error("Error fetching ETag:", error);
        })
        .finally(() => {
          setTimeout(() => this.fetchETagAndCompare(), 2 * 60 * 1000);
        });
    },
    getWangAn() {
      //this.getUnitCount();
      this.getSystemCount();
      //this.getIpCount();
      //this.getAqCount();
      //this.getFxCount();
      this.getTbList();
      this.getGjNumber();
      this.getAqrzCount();
      this.getAqtbCount();
      setTimeout(() => this.getWangAn(), 2 * 60 * 1000);
    },
    getWxCount() {
      this.$http
        .get("/wxApi/pspreadjn/media/pool/open/getScreenNowCount")
        .then((response) => {
          let data = response.body;
          if (data.code === 200) {
            this.xczlCount = data.data;
          }
        });
      // 智慧网宣系统 媒体主体、信源数量
      let params = {
        startTime: new Date(2023, 9, 1).getTime(),
        endTime: new Date().getTime(),
      };
      this.$http
        .get("/wxApi/pspreadjn/media/pool/open/mediaLibrary/statistics", {
          params,
        })
        .then((response) => {
          let data = response.body;
          if (data.code === 200) {
            this.wxmtCount = data.data.mediaLibraryCount;
            this.wxxyCount = data.data.mediaAccountCount;
          }
        });

      // 智慧网宣系统 监测专题数量
      this.$http
        .post("/wxApi/pspreadjn/screen/event/getThisYearEventCount")
        .then((response) => {
          let data = response.body;
          if (data.code === 200) {
            this.wxjcCount = data.thisYearEventCount;
          }
        });

      setTimeout(() => this.getWxCount(), 3 * 60 * 1000);
    },
    out() {
      this.$http.get(gl.serverURL + "/logout").then((response) => {
        let data = response.body;
        if (data.status == 0) {
          localStorage.removeItem("userName");
          localStorage.removeItem("userAccount");
          localStorage.removeItem("sysNos");
          localStorage.removeItem("tokens");
          this.userName = "";
          this.logged = false;
          this.$Message.success("退出登录成功！");
          this.$router.push("/home");
        }
      });
    },
    formatNumber(num) {
      if (num >= *********) {
        return (num / *********).toFixed(1) + ' 亿';
      } else if (num >= 100000) {
        return (num / 10000).toFixed(1) + ' 万';
      } else {
        return num.toString();
      }
    },
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {
    document.title = "综合门户大屏";

    this.userAccount = localStorage.getItem("userAccount");
    this.userName = localStorage.getItem("userName");
    this.getLoginNubmer();
    this.getSjImportYqList(); //查询舆情信息，涉济推荐

    this.getSjCount(); // 涉济数量
    this.getWxCount();
    this.getWangAn();
    this.getEcharts();
    this.getAllUsed();
    this.getSystemUseNum();
    // 检查大屏版本是否更新
    this.fetchETagAndCompare();

    // 地图数据更新
    this.getEchartsData();
    let count = 0;
    // 动态时间显示
    this.timer = setInterval(() => {
      this.nowtime = new Date();
      count++;

      if (count % 5 == 0 && this.pauseId !== "showIx") {
        // 每5秒执行
        this.showIx = this.showIx >= 2 ? 0 : this.showIx + 1;
      }
      if (count % 5 == 0 && this.pauseId !== "showQix") {
        // 每5秒执行
        this.showQix = this.showQix >= 1 ? 0 : this.showQix + 1;
      }

      if (count % 4 == 0 && this.pauseId !== "showWxIx") {
        // 每5秒执行
        this.showWxIx = this.showWxIx >= 3 ? 0 : this.showWxIx + 1;
      }
      if (count % 4 == 0 && this.pauseId !== "showWaIndex") {
        // 6秒执行
        this.showWaIndex = this.showWaIndex >= 3 ? 0 : this.showWaIndex + 1;
      }
    }, 1000);
    gl.t = this;
  },
  beforeDestroy() {
    // 清除定时器，防止内存泄漏
    clearInterval(this.timer);
  },
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.frame {
  width: 1920px;
  height: 1080px;
  background: url("../../assets/img/bottom.svg") no-repeat center 920px,
    url("../../assets/img/wayBack.png") no-repeat;
  position: relative;

  .header {
    display: flex;
    justify-content: space-between;
    padding: 0px 20.29px 0px 23.49px;

    .time {
      height: 27px;
      font-family: TRENDS;
      color: #ffffff;
      font-size: 20px;
      text-align: left;
      margin-top: 11.78px;
      padding-left: 6.52px;
      letter-spacing: 3px;
    }

    .time-one {
      height: 15px;
      font-family: TRENDS;
      color: #a7a7a7;
      text-align: left;
      padding-left: 6.52px;
      margin-bottom: 6.52px;
      letter-spacing: 1px;
    }

    .title {
      position: absolute;
      left: 0;
      top: 0;
      background: url("../../assets/img/header.png") no-repeat center top;
      width: 100%;
      height: 127px;
    }

    .disposition {
      position: absolute;
      top: 66px;
      right: 20px;
      height: 21px;
      font-family: MicrosoftYaHei;
      color: #fffdfb;
      font-size: 16px;
      cursor: pointer;
      letter-spacing: 5px;
    }
  }

  .nubmer {
    position: absolute;
    top: 73.11px;
    width: 1920px;
    height: 110px;
    z-index: 99;

    .rotate {
      /deep/ .nubmers-name {
        position: relative;
        transition: all 0.7s 0s;
        transform: rotateX(90deg);
        z-index: 90;
      }

      /deep/ .nubmers-list {
        transition: all 0.7s 0s;
        transform: rotateX(90deg);
        transform-origin: center 40px;
      }

      /deep/ p > svg {
        visibility: hidden;
      }
    }

    .rotate.rotateShow {
      /deep/ .nubmers-name {
        transition: all 1s 0.5s;
        transform: rotateX(0deg);
        z-index: 99;
      }

      /deep/ .nubmers-list {
        transition: all 1s 0.5s;
        transform: rotateX(0deg);
      }

      /deep/ p > svg {
        visibility: visible;
      }
    }

    .nubmers {
      position: absolute;
      left: 182.01px;

      .nubmers-list {
        width: 229.46px;
        text-align: center;
        height: 56px;
        font-family: Impact;
        color: #ff7200;
        font-size: 46px;

        .lists,
        /deep/ .odometer-value {
          font-family: Impact;
          background: -webkit-linear-gradient(top, #ffd65a, #ffa414);
          /* Safari/Chrome */
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          min-width: 25px;
          /* 防数字左右抖动 */
        }

        .tiao {
          height: 21px;
          font-family: MicrosoftYaHei;
          color: #ffffff;
          font-size: 15.9px;
          line-height: 27.83px;
        }
      }

      .nubmers-name {
        display: flex;
        align-items: center;
        height: 22px;

        span {
          font-family: TRENDS;
          color: #ffffff;
          font-size: 18px;
          line-height: 31.5px;
        }
      }
    }
  }

  .content {
    .today-header {
      top: 180px;
      left: 364px;
      width: 305px;
      height: 30px;
      padding-left: 73.85px;
      background: url("../../assets/img/todayBack.png") no-repeat bottom;
    }

    .today-list {
      position: absolute;
      top: 216px;
      left: 71px;
      width: 601px;
      height: 237px;
      background: url("../../assets/img/leftTopBack.png") no-repeat;
      padding: 20px 0px 4px 37px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        display: none;
      }

      .today-lists {
        font-family: MicrosoftYaHei;
        width: 461.66px;
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(
          90deg,
          rgba(4, 132, 144, 0.35) 0%,
          rgba(4, 119, 144, 0) 100%
        );
        margin-bottom: 14px;
        padding: 5.33px 4.66px 7.59px 11.67px;

        .today-text {
          height: 21px;
          font-size: 16px;
          line-height: 16px;
          width: 415px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .today-textNew {
          height: 16px;
          color: #ffffff;
          font-size: 12px;
          line-height: 16px;
          margin-left: 20.35px;
        }
      }
    }

    .account-header {
      top: 180px;
      width: 305px;
      height: 30px;
      right: 360px;
      background: url("../../assets/img/todayBack-right.png") no-repeat bottom;
    }

    .account-back {
      position: absolute;
      top: 216px;
      right: 67px;
      width: 607px;
      height: 246px;
      background: url("../../assets/img/rightTopBack.png") no-repeat;
      padding: 30.6px 0px 30.4px 106.18px;

      .backs {
        display: flex;
        align-items: flex-end;

        .backs-title {
          height: 21px;
          font-family: MicrosoftYaHei;
          color: #ffffff;
          font-size: 15.99px;
        }

        .backs-nubmer {
          height: 23px;
          font-family: DIN-Medium;
          color: #ffffff;
          font-size: 18.36px;
          margin-bottom: 3px;
        }
      }
    }

    .work-back {
      width: 640px;
      height: 252px;
      left: 73px;
      padding: 20px 0px 18px 42.34px;
      background: url("../../assets/img/leftBottomBack.png") no-repeat;
      overflow-y: auto;

      &::-webkit-scrollbar {
        display: none;
      }

      .work-list {
        font-family: MicrosoftYaHei;
        color: #ffffff;
        font-size: 16px;
        width: 461.66px;
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0px 15px 0px 20.33px;
        background: linear-gradient(
          90deg,
          rgba(4, 132, 144, 0.35) 0%,
          rgba(4, 119, 144, 0) 100%
        );
        margin-bottom: 18px;
      }
    }

    .feature-back {
      width: 640px;
      height: 270px;
      position: absolute;
      right: 73px;
      background: url("../../assets/img/rightBottomBack.png") no-repeat;
      padding: 0px 31px 18px 92px;
      display: flex;
      flex-wrap: wrap;

      .feature-backs {
        position: relative;
        width: 121.1px;
        height: 87px;
        font-family: MicrosoftYaHei;
        color: #ffffff;
        font-size: 15.99px;
        text-align: center;
        margin: 38px 0px 0px 51px;
        cursor: pointer;

        .backs-name {
          position: absolute;
          display: inline-block;
          width: 100%;
          left: 0px;
          top: 35.9px;
        }

        .backs-svg {
          position: absolute;
          width: 38px;
          height: 38px;
          display: inline-block;
          left: 50%;
          top: -17px;
          transform: translateX(-50%);
        }

        .backs-svgNew {
          position: absolute;
          left: 0px;
          top: 0px;
        }
      }
    }
  }

  .earts {
    position: absolute;
    top: 109px;
    left: 596px;
    width: 656px;
    height: 724px;
    background: url("../../assets/img/echarts.svg") no-repeat;
    z-index: 50;
  }

  .bubbling {
    position: absolute;
    top: 109px;
    left: 596px;
    z-index: 60;
    transition: all 2.5s 0.3s;
    transform: translateY(10px) scale(0.9);

    > svg,
    .float-name,
    .modals-yuan {
      transition: all 2.5s 0.3s;
      opacity: 0;
    }

    transform-origin: center center;

    .bubbling-modal {
      position: absolute;

      .modals {
        position: absolute;
        transition: all 1s 0s;
        transform: rotateX(90deg);
        cursor: pointer;
        background: url(../../assets/img/box/top-left.svg) top left,
          url(../../assets/img/box/top-right.svg) top right,
          url(../../assets/img/box/bottom-left.svg) bottom left,
          url(../../assets/img/box/bottom-right.svg) bottom right;
        background-size: 15px;
        background-color: #28528ac7;
        background-repeat: no-repeat;
        color: #fff;
        font-size: 14px;
        padding: 10px;
      }

      .modals-number {
        margin: 3px auto;
        width: 44px;
        height: 16px;
        line-height: 16px;
        text-align: right;
        padding-right: 2px;
        background: url(../../assets/img/earchs-number.svg) no-repeat;
      }

      .modals-yuan {
        width: 13px;
        height: 13px;
        border-radius: 100%;
        border: 5px solid #ff9d00;
        margin: 3px auto;
        background: #fff;
      }
    }
  }

  .bubbling.showBubbling {
    z-index: 999;
    transition: all 2s 0s;
    transform: translateY(-5px) scale(1.1);

    > svg,
    .float-name,
    .modals-yuan {
      transition: all 2.5s 0.3s;
      opacity: 1;
    }

    transform-origin: center center;

    .modals {
      transition: all 1s 2s;
      box-shadow: inset 0 0 5px 0px #5de6fdcc;
      transform: rotateX(0deg);

      &.nodata {
        transform: rotateX(90deg);
      }
    }
  }
}

.blue {
  color: #0ff5f7;
}

.yellows {
  color: #ff9d2d;
}

.positionNew {
  position: absolute;
  top: 198px;
}

.today-title {
  height: 27px;
  font-family: TRENDS;
  color: #c9feff;
  font-size: 22px;
  line-height: 13.87px;
  text-align: center;
  letter-spacing: 3px;
  display: inline-block;
}

.systemZong {
  top: 825px;
  left: 98px;
  display: flex;
  justify-content: space-between;
  width: calc(~"100% - 189px");

  .system-account {
    height: 50px;
    width: 346px;
    font-family: PingFang SC;
    font-weight: 600;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;

    .system-nubmer {
      color: #ff9200;
      font-size: 30px;
    }
  }
}

.foot-nav {
  position: relative;
  width: 180px;
  height: 127px;
  cursor: pointer;

  .foot-icon {
    position: absolute;
    width: 187.81px;
    display: inline-block;
    left: 0px;
    top: 0px;
  }

  .foot-text {
    position: absolute;
    height: 22px;
    text-align: center;
    font-family: PingFang SC;
    font-weight: 600;
    color: #ffffff;
    font-size: 16px;
    display: inline-block;
    width: 100%;
    white-space: nowrap;
  }
}

.returns {
  margin-left: 50px;
  width: 110px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  font-family: Microsoft YaHei;
  color: #ffffff;
  font-size: 20px;
  cursor: pointer;
}

.circle {
  width: 109px;
  height: 109px;
  background-color: #3498db;
  border-radius: 50%;
  animation: grow-fade 5s infinite linear;
  position: absolute;
  top: 420px;
  left: 905px;
}

@keyframes grow-fade {
  0% {
    transform: scale(1);
    opacity: 0;
  }

  20% {
    transform: scale(2);
    opacity: 0.3;
  }

  40% {
    transform: scale(3);
    opacity: 0.6;
  }

  60% {
    transform: scale(4);
    opacity: 0.3;
  }

  80% {
    transform: scale(5);
    opacity: 0;
  }

  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.HardwareInformation {
  position: absolute;
  left: 615px;
  top: 000px;
}

.ToggleBox {
  position: absolute;
  left: 900px;
  top: 850px;
  display: flex;
  z-index: 999999;

  .item {
    border: 1px solid #48b8ef;
    height: 5px;
    width: 50px;
    margin: 0 10px;
    cursor: pointer;
  }

  .active {
    background: #48b8ef;
  }
}
.ToggleFeatureBox{
  position: absolute;
  left: 59%;
  bottom: 0;
  display: flex;
  z-index: 999999;
  transform: translate(-50%);
  .item {
    border: 1px solid #48b8ef;
    height: 5px;
    width: 50px;
    margin: 0 10px;
    cursor: pointer;
  }

  .active {
    background: #48b8ef;
  }

}
</style>
