<template>
  <div class="marquee-wrapper">
    <div class="marquee" ref="marquee">
      <div class="marquee-content" ref="marqueeContent" >
      
        <!-- 克隆一份内容以实现无缝滚动 -->
        <template v-for="(outItem, outIx) in [0,1]">
          <div class="marquee-item" v-for="(item, index) in items" :key="outIx+'_'+index">
            <div>
              <p class="today-text blue" :class="index % 2!=0 ? 'yellows' : ''" @click="toJcDetails(item)">
               <!-- <span style="width:30px;display:inline-block;">{{ index + 1 }}.</span> -->
              
               <span style="width:26px;display:inline-block;">
                 <svg-icon
                :icon-class="situationJSON[item.msgSiution]"
                style="width: 20px; height: 16px;"
            />
               </span>
               {{item.msgTitle && item.msgTitle!='undefined' ? item.msgTitle : item.msgContent }}
              </p>
              <p class="today-textNew">
                <!-- {{ situationJSON[item.msgSiution] ? situationJSON[item.msgSiution] + '-' : '' }}
                {{ item.msgUname && item.msgUname!='undefined' ? item.msgUname : "" }}
                {{ moment(item.createTime).format("YYYY-MM-DD HH:mm:ss") }} -->
                {{moment(item.mpublishTime).format("YYYY-MM-DD HH:mm:ss")}}
                <span style="margin:0px 10px;">{{item.msentiment == 1 ? '正面': item.msentiment == -1 ? '负面':'中性'}}</span>
                <span v-for="(k,index) in getArray(item.scopeArea)" :key="index">
                  {{k}}  <span v-if="getArray(item.scopeArea).length == 2 && index == 0" style="margin:0px 2px;">|</span>
                  </span>
              </p>
            </div>
            <!-- <svg-icon
                :icon-class="index %2 != 0 ? '门户-黄色编辑' : '门户-编辑'"
                style="width: 30px; height: 32px; cursor: pointer"
            /> -->
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import moment from "moment"
import situationJSON from '@/assets/json/situation.json';

export default {
  name: 'VerticalMarquee',
  props: {
    items: {
      type: Array,
      required: true
    },
    speed: {
      type: Number,
      default: 3 // 每条数据滚动需要的时间s，越大速度越慢
    }
  },
  mounted() {
    this.startMarquee();
  },
  watch:{
	  items(ov,nv){	      
	      let duration = this.items.length * this.speed; // 动画持续时间
	      this.$refs.marqueeContent.style.animationDuration = `${duration}s`;
	  }
  },
  data(){
	  return {
		  situationJSON
	  };
  },
  methods: {
    moment,
    toJcDetails(val) {
      let data = {
        sysURL:SYSTEM_CONFIG_XZ[0].sysURL,
        to:'/main/details?situation=' + val.msgSiution + ';msgKey=' + val.mkey ,
        sysNo: "trs_jccz",
      }
      this.$emit('goDetail',data)
    },
    getArray(data) {
       let list = []
      if (data) {
        if (data.indexOf('[') > -1) {
          list = JSON.parse(data)
        } else {
          list = data.split(',') 
        }
        if (list.length > 2) {
          list=list.slice(0,1)
        }
      }
    return list
    },
    startMarquee() {
      const marqueeContent = this.$refs.marqueeContent;
      const marquee = this.$refs.marquee;
      
      let duration = this.items.length * this.speed; // 动画持续时间

      marqueeContent.style.animationDuration = `${duration}s`;

      // 监听动画结束事件并重新开始动画
      marqueeContent.addEventListener('animationiteration', () => {
        marquee.scrollTop = 0;
        
        duration = this.items.length * this.speed; // 动画持续时间
        marqueeContent.style.animationDuration = `${duration}s`;
      });
    }
  }
};
</script>

<style scoped >
.marquee-wrapper {
  width: 100%;
  height: 246px; /* 根据需要调整容器高度 */
  overflow: hidden;
}

.marquee {
  display: flex;
  flex-direction: column;
  white-space: nowrap;
  height: 100%;
  overflow: hidden;
}

.marquee-content {
  display: flex;
  flex-direction: column;
  animation: marquee linear infinite;
}

.marquee-item {
  display: block;
  padding: 10px 0; /* 每个项之间的间隔 */
  font-size: 1.2em;
  background-color: #f0f0f0;
  margin: 5px 0;

  font-family: MicrosoftYaHei;
  width: 461.66px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(90deg,
  rgba(4, 132, 144, 0.35) 0%,
  rgba(4, 119, 144, 0) 100%);
  margin-bottom: 14px;
  padding: 5.33px 4.66px 7.59px 11.67px;
}

/* 动画关键帧 */
@keyframes marquee {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-50%);
  }
}

.today-text {
  height: 21px;
  font-size: 16px;
  line-height: 16px;
  width: 415px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
}

.today-textNew {
  /* height: 16px; */
  color: #ffffff;
  font-size: 14px;
  line-height: 22px;
  padding-left: 31px;
}

.blue {
  color: #0ff5f7;
}

.yellows {
  color: #ff9d2d;
}
</style>
