<template>
  <div>
    <p class="nubmers-list">
      <span v-show="number == null" class="lists">--</span>
      <IOdometer class="lists" :value="getNumber(number)" v-show="number != null" />
      <span class="tiao">{{ getType(type) }}</span>
    </p>
    <p>
      <svg-icon icon-class="数字线" style="min-width: 229.46px" />
    </p>
    <p class="nubmers-name">
      <span>
        <svg-icon
          :icon-class="
            name == '网络安全中心' || name == '网络传播中心'
              ? '黄色-三角'
              : '蓝色-三角'
          "
          style="width: 14.8px; height: 18.25px; margin-right: 5.7px"
      /></span>
      <a :href="path" v-if="path"
        >{{ name }}{{ nameOne ? "：" + nameOne : "" }}</a
      >
      <span :style="name != '网络安全中心' ? 'cursor: pointer':''" v-else>{{ name }}{{ nameOne ? "：" + nameOne : "" }}</span>
    </p>
  </div>
</template>

<script>
import IOdometer from "vue-odometer";
import "odometer/themes/odometer-theme-default.css";
export default {
  components: { IOdometer },
  props: ["type", "name", "number", "nameOne", "path"],
  data() {
    return {};
  },
  //生命周期 - 创建完成（访问当前this实例）
  created() {},
  //方法所在
  methods: {
    formatNumber(num) {
      if (num >= 100000000) {
        return (num / 100000000).toFixed(2) + ' 亿';
      } else if (num >= 100000) {
        return (num / 10000).toFixed(2) + ' 万';
      } else {
        return num.toString();
      }
    },
    getType(t) {
      let str = t
      if ((this.nameOne == '近一月网络攻击数' || this.nameOne == '近一月安全日志数') &&  this.number) {
        let one = 100000000;
        let two = 1000000;
        if (Number(this.number) - one >= 0) {
           return '亿';
        } else {
          if (this.nameOne == '近一月网络攻击数') {
            if (Number(this.number) - two >= 0) {
              return '万';
            }else {
              return str
            }
          }else {
             return '万';
          }
        }
      }else {
        return str
      }
    },
    getNumber(d) {
      let data = d
      if (this.nameOne == '近一月网络攻击数' || this.nameOne == '近一月安全日志数') {
        let one = 100000000;
        let two = 1000000;
        if (Number(data) - one >= 0) {
           return (Number(data) / one).toFixed(2);
        } else {
          if (this.nameOne == '近一月网络攻击数') {
            if (Number(data) - two >= 0) {
              return (Number(data) / 10000).toFixed(2);
            }else {
              return data
            }
          }else {
             return (Number(data) / 10000).toFixed(2);
          }
        }
      }else {
        return data
      }
    },
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {},
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.nubmers-list {
  min-width: 237px;
  text-align: center;
  height: 56px;
  font-family: Impact;
  color: #ff7200;
  font-size: 46px;

  .lists,
  /deep/ .odometer-value {
    font-family: Impact;
    background: -webkit-linear-gradient(
      top,
      #ffd65a,
      #ffa414
    ); /* Safari/Chrome */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    min-width: 25px; /* 防数字左右抖动 */
  }

  .tiao {
    height: 21px;
    font-family: MicrosoftYaHei;
    color: #ffffff;
    font-size: 15.9px;
    line-height: 27.83px;
  }
}
.nubmers-name {
  display: flex;
  align-items: center;
  height: 22px;
  z-index: 3;
  span,
  a {
    font-family: TRENDS;
    color: #ffffff;
    font-size: 18px;
    line-height: 31.5px;
  }
}
</style>
