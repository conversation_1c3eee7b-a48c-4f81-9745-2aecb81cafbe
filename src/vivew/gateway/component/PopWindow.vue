<template>
  <div class="PopWindow">
    <div class="content">
      <Icon type="md-arrow-dropleft-circle" @click="switchType" />
      <Icon type="md-arrow-dropright-circle" @click="switchType" />
      <div class="title">
        {{ typeId === "1" ? "涉济信息统计" : "涉济重点关注信息量统计" }}
      </div>
      <div class="controls">
        <div class="time">
          <div
            v-for="(v, k) in timeList"
            :class="['item', timeId === k ? 'active' : '']"
            @click="switchTime(k)"
          >
            {{ v }}
          </div>
          <!-- <div
            :class="['item', timeId === '2' ? 'active' : '']"
            @click="switchTime('2')"
          >
            近一月
          </div>
          <div
            :class="['item', timeId === '3' ? 'active' : '']"
            @click="switchTime('3')"
          >
            近一年
          </div> -->
        </div>
        <Icon type="md-close" @click="close" style="cursor: pointer" />
      </div>
      <div class="chat" ref="chat"></div>

      <div class="overview">
        {{ typeId === "1" ? "涉济网络信息总量" : "涉济重点关注网络信息总量" }}：
        <span class="Highlight">
          {{ formatNumber(totalPositiveNum + totalNegativeNum) }}
        </span>
        条。其中正面信息
        <span class="Highlight">{{ formatNumber(totalPositiveNum) }}</span>
        条，占比<span class="Highlight">{{
          totalPositiveNum + totalNegativeNum
            ? (
                (totalPositiveNum / (totalPositiveNum + totalNegativeNum)) *
                100
              ).toFixed(2) + "%"
            : "--"
        }}</span
        >；负面信息
        <span class="Highlight">{{ formatNumber(totalNegativeNum) }}</span>
        条，占比
        <span class="Highlight">{{
          totalPositiveNum + totalNegativeNum
            ? (
                (totalNegativeNum / (totalPositiveNum + totalNegativeNum)) *
                100
              ).toFixed(2) + "%"
            : "--"
        }}</span
        >。
      </div>
    </div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import echarts from "echarts";
import moment from "moment";

export default {
  data() {
    // 这里存放数据
    return {
      echarts,
      chartData: {},
      timeId: "2",
      typeId: "1",
      timeList: {
        1: "近24小时",
        2: "近一月",
        3: "近一年",
      },
      totalPositiveNum: 0,
      totalNegativeNum: 0,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    type: {
      default: "1",
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.typeId = this.type;
  },
  // 方法集合
  methods: {
    formatNumber(num) {
      if (num < 100000) {
        // 1. 10万以下，正常显示（带千位分隔符）
        return num.toLocaleString();
      } else if (num >= 100000 && num < 100000000) {
        // 2. 超过10万，小数点后保留1位，显示为万
        return (num / 10000).toFixed(1) + "万";
      } else if (num >= 100000000) {
        // 3. 超过1亿，保留最多4位有效数字，显示为亿
        let formatted = (num / 100000000).toFixed(3); // 先保留3位小数
        // 去除末尾的无效0，保留最多1位小数
        formatted = parseFloat(formatted).toPrecision(4);
        return formatted + "亿";
      }
    },

    close() {
      this.$emit("close");
    },
    switchType() {
      this.typeId = this.typeId === "1" ? "2" : "1";
      this.timeId = "2";
      this.getData();
    },
    switchTime(id) {
      this.timeId = id;
      this.getData();
    },
    getData() {
      let params = {};
      let url =
        this.typeId === "1"
          ? "/jcczapi/common/sjMsgStat?dayNum="
          : "/jcczapi/common/recommendStat?dayNum=";
      let that = this;
      this.$http.get(url + this.timeId, { params }).then((res) => {
        this.totalPositiveNum = 0;
        this.totalNegativeNum = 0;
        this.chartData = res.body.data;

        // 遍历对象并计算总数
        Object.keys(this.chartData).forEach((key) => {
          this.totalPositiveNum += this.chartData[key].positiveNum;
          this.totalNegativeNum += this.chartData[key].negativeNum;
        });
        this.setChart();
      });
      this.getLog("门户屏/涉济信息统计", '浏览');
    },
    setChart() {
      const newData = Object.keys(this.chartData).reduce((acc, key) => {
        const item = this.chartData[key];
        acc[key] = item.positiveNum + item.negativeNum; // 计算和
        return acc;
      }, {});
      const values = Object.values(this.chartData);
      const option = {
        backgroundColor: "transparent",
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(150, 150, 150, 0.1)",
              width: 40, // 阴影宽度设置为柱子宽度的两倍
            },
          },
          textStyle: {
            color: "#FFFFFF",
            fontSize: 20, // 设置字体大小为20px
          },
          backgroundColor: "#0F2278", // 设置提示窗背景色
          borderColor: "#078DCC", // 设置提示窗边框颜色
          borderWidth: 1, // 设置提示窗边框宽度为1px
          formatter: function (params) {
            const 信息量 = params.find(
              (param) => param.seriesName === "信息量"
            );
            let time = "";
            if (this.timeId === "1") {
              time = moment(Number(信息量.name)).format("HH:mm");
            }
            if (this.timeId === "2") {
              time = moment(Number(信息量.name)).format("MM-DD");
            }
            if (this.timeId === "3") {
              time = moment(Number(信息量.name)).format("YY-MM");
            }
            const data = values[params[0].dataIndex];
            return 信息量
              ? `
            <span style='font-size:18px;font-weight:900;'>
              ${time}
            </span>  
            <br/> 
            <span style='font-size:20px;font-weight:900;'>
              信息总量： ${信息量.value.toLocaleString()}
            </span>
            <br/> 
            <span style='font-size:14px;'>
              正面信息：${data.positiveNum.toLocaleString()}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;占比：${
                  信息量.value
                    ? ((data.positiveNum / 信息量.value) * 100).toFixed(2) + "%"
                    : "-"
                }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
            <br/> 
            <span style='font-size:14px;'>
              负面信息：${data.negativeNum.toLocaleString()}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;占比：${
                  信息量.value
                    ? ((data.negativeNum / 信息量.value) * 100).toFixed(2) + "%"
                    : "-"
                }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
            `
              : "";
          }.bind(this),
        },
        toolbox: {
          iconStyle: {
            color: "#FFFFFF",
          },
        },
        xAxis: [
          {
            type: "category",
            data: Object.keys(this.chartData),
            axisPointer: {
              type: "shadow",
              shadowStyle: {
                color: "rgba(150, 150, 150, 0.1)",
                width: 40, // 阴影宽度设置为柱子宽度的两倍
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              formatter: function (value) {
                if (this.timeId === "1") {
                  return moment(Number(value)).format("HH:mm");
                }
                if (this.timeId === "2") {
                  return moment(Number(value)).format("MM-DD");
                }
                if (this.timeId === "3") {
                  return moment(Number(value)).format("YY-MM");
                }
              }.bind(this),
              color: "#FFFFFF",
              fontSize: "16",
            },
            axisLine: {
              lineStyle: {
                color: "#FFFFFF",
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "信息量",
            axisLabel: {
              formatter: "{value}",
              color: "#FFFFFF",
              fontSize: "16",
            },
            axisLine: {
              show: false,
            },
            nameTextStyle: {
              color: "#FFFFFF",
              fontSize: "16",
            },
          },
          {
            type: "value",
            interval: 5,
            axisLabel: {
              show: false,
              color: "#FFFFFF",
            },
            axisLine: {
              show: false,
            },
            nameTextStyle: {
              color: "#FFFFFF",
            },
          },
        ],
        series: [
          {
            name: "信息量",
            type: "bar",
            barWidth: this.timeId === "3" ? 30 : 20,
            itemStyle: {
              normal: {
                color: function (params) {
                  const date = moment(Number(params.name));
                  if (this.timeId === "2") {
                    if (date.day() === 6 || date.day() === 0) {
                      return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: "#FF5C02" },
                        { offset: 1, color: "#E38B50" },
                      ]);
                    }
                  }
                  return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: "#2069E7" },
                    { offset: 1, color: "#03BCEB" },
                  ]);
                }.bind(this),
                barBorderRadius: [3, 3, 0, 0],
              },
            },
            tooltip: {
              valueFormatter: function (value) {
                return value + " ml";
              },
            },
            data: Object.values(newData),
          },
          {
            name: "Temperature",
            type: "line",
            lineStyle: {
              color: "#44E0F7",
              type: "solid",
              width: 5,
            },
            itemStyle: {
              normal: {
                opacity: 0,
              },
            },
            tooltip: {
              valueFormatter: function (value) {
                return value + " °C";
              },
            },
            data: Object.values(newData),
          },
        ],
      };
      const chart = this.echarts.init(this.$refs.chat);
      chart.setOption(option);
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getData();
  },
};
</script>
<style scoped lang="less">
.PopWindow {
  position: fixed;
  right: 0;
  left: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999999999;
  display: flex;
  justify-content: center;
  align-items: center;

  .content {
    position: relative;
    width: 1200px;
    height: 650px;

    background: url("../../../assets/img/弹框背景.svg");

    & > .ivu-icon {
      font-size: 50px;
      position: absolute;
      color: #00b8e5;
      cursor: pointer;
      top: 50%;

      &:nth-child(1) {
        left: -10%;
      }

      &:nth-child(2) {
        right: -10%;
      }
    }

    .title {
      color: #fff;
      line-height: 80px;
      text-align: center;
      padding: 0 30px;
      font-size: 28px;
      font-weight: 600;
      text-shadow: 0 0 2px #98f7f7, 0 0 2px #98f7f7, 0 0 2px #98f7f7,
        0 0 2px #98f7f7;
    }

    .controls {
      display: flex;
      align-items: center;
      //width: 230px;
      justify-content: space-between;
      position: absolute;
      right: 20px;
      top: 100px;

      .time {
        border: 1px solid #0091e0;

        .item {
          float: left;
          color: #fff;
          line-height: 24px;
          text-align: center;
          min-width: 60px;
          padding: 0 10px;
          font-size: 16px;
          height: 24px;
          font-weight: 600;
          background: #005488;
          cursor: pointer;
        }

        .active {
          background-color: #0091e0;
        }
      }

      /deep/ .ivu-icon {
        font-size: 24px;
        color: #74b9ff;
      }
    }

    .chat {
      width: 1200px;
      height: 470px;
      margin: 50px auto 10px;
    }
    .overview {
      text-align: center;
      color: #fff;
      font-size: 16px;

      .Highlight {
        color: #24d9ff;
        font-weight: 600;
      }
    }
  }
}
</style>
