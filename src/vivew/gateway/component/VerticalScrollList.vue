<template>
  <div class="scroll-wrapper" ref="wrapper">
    <div class="scroll-content" ref="content">
      <div class="scroll-item" v-for="(item, index) in displayedItems" :key="index" >
        <div>
          <div class="text-line ellipsis" style="overflow: hidden;text-overflow:ellipsis;white-space:nowrap;width: 400px;">
            <template v-if="item.subject">
              {{item.subject}}
            </template>
            <template v-else>
            <span class="prstatus" :class="getName(item.promptStatus,1)" v-if="!item.periodsTime">{{getName(item.promptStatus,2)}}</span>  {{ item.periodsTime ?  '生成网情要报第' + item.periodsTime + '期' : '发送舆情提示单【' + item.promptNum +'】至' + item.sendOrgName}}
            </template>
          </div>
          <div class="text-line" style="font-size:14px;text-align:left;">
            {{ moment(item.createTime).format("MM月DD日 HH:mm") }}
            &emsp;
            来自-{{item.subject?"网络安全中心":"网络舆情中心"}}
          </div>
        </div>
        <svg-icon
            icon-class="门户-编辑动态"
            style="width: 19px; height: 19px; cursor: pointer"
        />
      </div>
    </div>
  </div>
</template>

<script>

import moment from "moment"

export default {
  name: 'VerticalScrollList',
  props: {},
  data() {
    return {
      displayedItems: null, // 克隆一份内容以实现无缝滚动
      scrollIndex: 0
    };
  },
  mounted() {
    this.startScrolling();
  },
  methods: {
    moment,
    getName(id,type) {
     if (id) {
      if (id == 2) {
        if (type == 1) {
          return 'wjs'
        }else {
         return '未接收'
        }
      } else if(id == 3) {
         if (type == 1) {
          return 'wfk'
        }else {
         return '未反馈'
        }
      } else if(id == 6 || id == 4) {
         if (type == 1) {
          return 'ywj'
        }else {
          if (id == 6) {
             return '已完结'
          }else {
            return '已反馈'
          }
        
        }
      } else if(id == 5) {
         if (type == 1) {
          return 'bth'
        }else {
         return '被退回'
        }
      }
     }else {
      return 
     }
    },
    startScrolling() {
      const content = this.$refs.content;
      const itemHeight = 58;
      const duration = 2; // 每次滚动的持续时间

      if (this.scrollIndex > 0) {
        content.style.transition = 'none'; // 禁用过渡效果
        content.style.transform = `translateY(${-1*this.itemHeight*this.scrollIndex}px)`;
        // 强制重绘以应用无过渡效果的变换
        content.offsetHeight;
      }
       content.style.transition = `transform ${duration}s`;
       content.style.transform = `translateY(0px)`;
    },
    changeData(nv){
      if(this.displayedItems == null){
        if(nv == null){
          return;
        }
        this.displayedItems = nv;
        this.scrollIndex =  nv.length;
      } else {
        // 只合并新数据
        const uniqueNV = nv.filter(item => !this.displayedItems.some(displayedItem => displayedItem.promptMsgId === item.promptMsgId) );
        this.displayedItems.unshift(...uniqueNV);
        this.scrollIndex = uniqueNV.length;
      }
      
      this.startScrolling();
      
      if(this.displayedItems.length>40)
        this.displayedItems.splice(0,40); // 保留40
    }
  }
};
</script>

<style lang="less" scoped>
.scroll-wrapper {
  width: 100%;
  overflow: hidden;
  position: relative;
  height: var(--container-height);
}

.scroll-content {
  display: flex;
  flex-direction: column;
}
.scroll-item {
  display: block;
  text-align: center;
  background-color: #f0f0f0;
  margin: 5px 0;
  font-family: MicrosoftYaHei;
  color: #ffffff;
  font-size: 16px;
  width: 461.66px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 7px 15px;
  background: linear-gradient(90deg,
  rgba(4, 132, 144, 0.35) 0%,
  rgba(4, 119, 144, 0) 100%);
  margin-bottom: 15px;
  
  .text-line{
    line-height: 22px;
    width: 400px;
    text-align: left;
    white-space:nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.prstatus {
font-size:14px;
border-radius:3px;
width: 50px;
line-height: 20px;
height: 20px;
text-align: center;
display: inline-block;
}
.wjs {
background: #2B6E91;
}
.wfk {
background: #916F28;
}
.ywj {
background: #6A6A6A;
}
.bth {
background: #653131;
}
</style>
