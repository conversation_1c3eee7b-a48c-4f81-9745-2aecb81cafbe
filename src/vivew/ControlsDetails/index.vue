<template>
  <div class="ControlsDetails">
    <div class="header">
      <div class="title">数字网信平台用户使用情况分析</div>
      <div class="StatisticalData">
        <div class="item">
          <div class="label">累计操作：</div>
          <div class="value">
            {{ formatNumber(allUsedData.totalNum) }}
          </div>
          <div class="label">次</div>
        </div>
        <div class="item">
          <div class="label">开通账号：</div>
          <div class="value">
            {{ formatNumber(allUsedData.userCount) }}
          </div>
          <div class="label">个</div>
        </div>
        <div class="item">
          <div class="label">今日操作：</div>
          <div class="value">
            {{ formatNumber(allUsedData.todayNum) }}
          </div>
          <div class="label">次</div>
          <div class="label">（历史最高：</div>
          <div class="value">
            {{ formatNumber(allUsedData.todayHistoryNum) }}
          </div>
           <div class="label">次 ）</div>
        </div>
        <div class="item">
          <div class="label">当前在线：</div>
          <div class="value">
            {{ formatNumber(allUsedData.onlineCount) }}
          </div>
          <div class="label">人</div>
            <div class="label">（历史最高：</div>
          <div class="value">
            {{ formatNumber(allUsedData.onlineHistoryCount) }}
          </div>
           <div class="label">人 ）</div>
        </div>
      </div>
    </div>
    <div class="StatisticalPeriod">
      <div class="optionsValue">
        统计时段：{{ selectTime === 99 ? customPeriod : TimeFrame }}
      </div>
      <div class="optionsList">
        <div class="label">统计时间：</div>
        <div class="list">
          <div
            v-for="item in timeOptionsList"
            :key="item.id"
            :class="['item', selectTime === item.id ? 'active' : '']"
            @click="selectTime = item.id"
          >
            {{ item.name }}
          </div>
          <DatePicker
            v-if="selectTime === 99"
            v-model="customTime"
            format="yyyy-MM-dd"
            placeholder="Select date"
            placement="bottom-end"
            style="width: 200px;"
            type="daterange"
            @on-change="dateChange"
          ></DatePicker>
        </div>
      </div>
    </div>
    <div class="tableFrame" :class="{'tableFrame-mobile': messageChartWidth == '100%'}">
      <OuterFrame style="width: 918px;" title="用户使用情况">
        <template v-slot:header>
          <div class="searchCondition">
            <Input
              v-model="userName"
              search
              style="width: 300px;"
              @on-search="userSearch"
            >
            <Select v-model="searchType" slot="prepend" style="width: 100px;">
              <Option value="1">按姓名查找</Option>
              <Option value="2">按单位查找</Option>
            </Select>
            </Input>
            <div class="list">
              <div
                v-for="item in unitOptionsList"
                :key="item.id"
                :class="['item', organTypes === item.id ? 'active' : '']"
                @click="deptCode = null;organTypes = item.id"
              >
                {{ item.name }}
              </div>
            </div>
          </div>
        </template>
        <TableList
          :defaultSortKey="2"
          :loading="userLoading"
          :pageNo="userPageNo"
          :rule="userTableRule"
          :tableData="userTableData"
          @on-sort="userSort"
        />
        <Page
          v-if="userTableTotal"
          :current="userPageNo"
          :page-size="15"
          :total="userTableTotal"
          show-total
          @on-change="userPageNoChange"
        />
      </OuterFrame>
      <OuterFrame style="width: 918px;" title="单位使用情况">
        <template v-slot:header>
          <div class="searchCondition">
            <div class="list">
              <div
                v-for="item in statisticsList"
                :key="item.id"
                :class="['item', statisticsTypes === item.id ? 'active' : '']"
                @click="statisticsTypes = item.id"
              >
                {{ item.name }}
              </div>
            </div>
          </div>
        </template>
        <TableList
          :key="statisticsTypes"
          :defaultSortKey="5"
          :loading="unitLoading"
          :pageNo="unitPageNo"
          :rule="unitTableRule"
          :tableData="paginate"
          @on-sort="unitSort"
          :isClick="true"
          @nameClick="nameClick"
        />
        <Page
          v-if="unitAllData.length > 0"
          :current="unitPageNo"
          :page-size="15"
          :total="unitAllData.length"
          show-total
          @on-change="unitPageNoChange"
        />
      </OuterFrame>
      <OuterFrame style="width: 918px; height: 410px;" title="平台访问情况">
        <template v-slot:header>
          <div class="searchCondition">
            <div class="list">
              <div
                v-for="item in statList"
                :key="item.id"
                :class="['item', statType === item.id ? 'active' : '']"
                @click="statType = item.id"
                v-show="item.id == 2 ? selectTime > 30 : true"
              >
                {{ item.name }}
              </div>
            </div>
          </div>
        </template>
        <nodata-page
          v-if="!loading && resultData === null"
          :classVal="'nodata2'"
        ></nodata-page>
        <Spin v-if="loading">
          <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
          <div>Loading</div>
        </Spin>
        <div v-if="resultData" ref="tendChart" class="chart"></div>
      </OuterFrame>
      <OuterFrame style="width: 918px; height: 410px;" title="终端使用情况">
        <template v-slot:header>
          <div class="searchCondition">
            <div class="list">
              <div
                v-for="item in unitOptionsListNew"
                :key="item.id"
                :class="['item', tendKeyName === item.name ? 'active' : '']"
                @click="changetend(item)"
              >
                {{ item.name }}
              </div>
            </div>
          </div>
        </template>
        
        <div class="zdChartbox">
          <div class="part">
            <nodata-page
              v-if="!tendLoading && tendData === null"
              :classVal="'nodata2'"
            ></nodata-page>
            <Spin v-if="tendLoading">
              <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
              <div>Loading</div>
            </Spin>
             <div v-if="tendData" ref="tendChartNew" class="chart"></div>
          </div>

          <div class="part">
            <!-- 单位占比 -->
            <div class="unit-ratio-container">
             
              <nodata-page
                v-if="!unitRatioLoading && unitRatioData === null"
                :classVal="'nodata2'"
              ></nodata-page>
              <Spin v-if="unitRatioLoading">
                <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
                <div>Loading</div>
              </Spin>
              <div v-if="unitRatioData" ref="unitRatioChart" class="chart"></div>
            </div>
          </div>

        </div>
        
      </OuterFrame>
      <OuterFrame style="width: 918px; height: 410px;" title="时段使用情况">
        <template v-slot:header>
          <div class="searchCondition">
            <RadioGroup v-model="timeChartType">
              <Radio :label="1">曲线图</Radio>
              <Radio :label="2">气泡图</Radio>
            </RadioGroup>
            <div class="list">
              <div
                v-for="item in TimeFrameListNew"
                v-if="item.id"
                :key="item.id"
                :class="['item', TimeFrameType === item.id ? 'active' : '']"
                @click="
                  () => {
                    if (item.id) TimeFrameType = item.id;
                  }
                "
              >
                {{ item.name }}
              </div>
            </div>
          </div>
        </template>
        <TimeFrame
          :chartType="timeChartType"
          :type="TimeFrameType"
          :startTime="
            customTime
              ? moment(customTime[0]).format('YYYY-MM-DD 00:00:00')
              : null
          "
          :endTime="
            customTime
              ? moment(customTime[1]).format('YYYY-MM-DD 23:59:59')
              : null
          "
        />
      </OuterFrame>
      <OuterFrame style="width: 918px; height: 410px;" title="模块使用情况">
        <template v-slot:header>
          <div class="searchCondition">
            <RadioGroup v-model="moduleChartsType">
              <Radio :label="1">饼图</Radio>
              <Radio :label="2">柱状图</Radio>
            </RadioGroup>
            <div class="list">
              <div
                v-for="item in TimeFrameList"
                :key="item.id"
                :class="['item', ModuleType === item.id ? 'active' : '']"
                @click="
                  () => {
                    if (item.id) ModuleType = item.id;
                  }
                "
              >
                {{ item.name }}
              </div>
            </div>
          </div>
        </template>
        <Module
          :chartType="moduleChartsType"
          :type="ModuleType"
          :startTime="
            customTime
              ? moment(customTime[0]).format('YYYY-MM-DD 00:00:00')
              : null
          "
          :endTime="
            customTime
              ? moment(customTime[1]).format('YYYY-MM-DD 23:59:59')
              : null
          "
        />
      </OuterFrame>
      <OuterFrame :style="{width: messageChartWidth}" title="短信使用情况">
        <MessageChart :messageChartData="messageChartData" :messagePieChartData="messagePieChartData" :hasLine="messageChartWidth == '100%'"/>
      </OuterFrame>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import moment from "moment";
import OuterFrame from "./components/outerFrame.vue";
import TableList from "./components/tabelList.vue";
import HardwareInformation from "@/components/HardwareInformation";
import mockData from "./mockData/unitData.js";

import echarts from "echarts";
import NodataPage from "../SystemConfiguration/components/nodataPage.vue";

import TimeFrame from "./components/timeFrame.vue";
import Module from "./components/module.vue";
import MessageChart from "./components/messageChart.vue";

const timeOptionsList = [
  {
    id: 7,
    name: "近一周",
  },
  {
    id: 30,
    name: "近一月",
  },
  {
    id: 365,
    name: "近一年",
  },
  {
    id: 99,
    name: "自定义",
  },
];

const statisticsList = [
  {
    id: 1,
    name: "全部",
  },
    {
    id: 2,
    name: "按市直单位统计",
  },
  {
    id: 3,
    name: "按区县统计",
  },
  // {
  //   id: 2,
  //   name: "按部门统计",
  // },
];

const unitOptionsList = [
  {
    id: 0,
    name: "全部",
    organName:"",
  },
  {
    id: 1,
    name: "市网信办",
    organName:"",
  },
  {
    id: 2,
    name: "区县网信办",
    organName:"",
  },
  {
    id: 3,
    name: "市直单位",
    organName:"",
  }
];

const TimeFrameList = [
  {
    id: "trs_sdzb,trs_jccz",
    name: "网络舆情中心",
  },
  {
    id: "trs_wx,sys_code_wp",
    name: "网络传播中心",
  },
  {
    id: "sys_code_wa",
    name: "网络安全中心",
  },
  {
    id: "sw_jbxt",
    name: "网络治理中心",
  },
  {
    id: "",
    name: "网络指挥中心",
  },
  {
    id: "",
    name: "网络产业中心",
  },
];
const userTableRule = [
  {
    key: "userName",
    label: "姓名",
    style: {
      width: "150px",
    },
  },
  {
    key: "organName",
    label: "单位 / 部门",
    style: {
      flex: 1,
    },
  },
  {
    key: "statNum",
    label: "操作次数",
    style: {
      width: "140px",
    },
    sort: true,
    sortId: 2,
  },
  {
    key: "lastTime",
    label: "最后一次操作时间",
    style: {
      width: "220px",
    },
  },
];
const unitTableRule = [
  {
    key: "organName",
    label: "单位名称",
    width: 100,
    style: {
      flex: 1,
    },
  },
  {
    key: "registerNum",
    label: "注册账号",
    width: 100,
    style: { width: "120px" },
    sort: true,
    sortId: 1,
  },
  {
    key: "activeNum",
    label: "活跃账号",
    width: 100,
    style: {
      width: "120px",
    },
    sort: true,
    sortId: 2,
  },
  {
    key: "activeRateStr",
    label: "活跃率",
    width: 100,
    style: {
      width: "120px",
    },
    sort: true,
    sortId: 3,
  },
  {
    key: "statNum",
    label: "操作总次数",
    width: 100,
    style: {
      width: "140px",
    },
    sort: true,
    sortId: 5,
  },
  {
    key: "useNum",
    label: "人均访问数",
    width: 140,
    style: {
      width: "140px",
    },
    sort: true,
    sortId: 6,
  },
];

const statList = [
  {
    id: 1,
    name: "按天统计",
  },
  {
    id: 2,
    name: "按月统计",
  },
];

export default {
  data() {
    // 这里存放数据
    return {
      mockData,
      timeOptionsList,
      unitOptionsList,
      unitOptionsListNew: [...unitOptionsList, 
  {
    id: 99,
    name: "街道乡镇",
    organName:"街道",
  }],
      statList,
      TimeFrameList,
      TimeFrameListNew: [ {
        id: "trs_sdzb,trs_jccz,trs_wx,sys_code_wp,sys_code_wa,sw_jbxt",
        name: "全部",
      },...TimeFrameList],

      timeChartType: 1,
      moduleChartsType: 1,

      statisticsTypes: 1,
      statisticsList,

      TimeFrameType: "trs_sdzb,trs_jccz,trs_wx,sys_code_wp,sys_code_wa,sw_jbxt",
      ModuleType: "trs_sdzb,trs_jccz",
      userTableRule, //用户列表规则
      unitTableRule, //单位列表规则
      selectTime: 30, //统计时间
      customTime: null, //统计时间-自定义时段

      organTypes: 0, //用户使用情况-单位类型
      userName: "", //用户使用情况-用户名

      allUsedData: [], //顶部全部数据
      userData: [], //用户使用情况

      userTableData: [],

      userPageNo: 1,
      unitPageNo: 1,

      userLoading: false,
      unitLoading: false,

      unitAllData: [],
      userTableTotal: 0,

      resultData: null,

      statType: 1,

      loading: false,
      tendLoading: false,
      tendData: null, //饼图数据
      tendOrganType: 0, //饼图数据,
      tendKeyName:"全部",
      tendOrganName:"",//饼图数据OrganName
      searchType: '1', // 用户使用情况搜索类型
      deptCode: null,
      isActive: null,
      messageChartWidth: '918px',
      messageChartData: null,
      messagePieChartData: null,

      // 单位占比相关数据
      unitRatioLoading: false,
      unitRatioData: null,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {
    NodataPage,
    OuterFrame,
    TableList,
    HardwareInformation,
    TimeFrame,
    Module,
    MessageChart,
  },
  props: {},

  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.getAllUsed();
    this.setViewportScale();
  },
  // 方法集合
  methods: {
    setViewportScale() {
      const originalWidth = 1920; // 原始宽度
      const currentWidth = window.innerWidth;
      // 计算宽度和高度的缩放比例
      const scaleWidth = currentWidth / originalWidth;
      this.updateMetaViewport(scaleWidth);
     
    },
    updateMetaViewport(scale) {
      console.log(scale);
      let viewportMeta = document.querySelector('meta[name="viewport"]');
      if (!viewportMeta) {
        // 如果没有 viewport meta 标签，创建一个
        viewportMeta = document.createElement("meta");
        viewportMeta.name = "viewport";
        document.head.appendChild(viewportMeta);
      }
      // 设置 viewport meta 标签的 content 属性
      viewportMeta.setAttribute(
        "content",
        `width=device-width, initial-scale=${scale}, maximum-scale=${scale}, user-scalable=no`
      );
      this.$nextTick(() => {
        if( window.innerWidth  <= 918 * 2){
          this.messageChartWidth = '918px'
        }else{
          this.messageChartWidth = '100%'
        }
      })
    },
    moment,
    setTerminalCharts() {
      this.$nextTick(() => {
         let myChart = echarts.init(this.$refs.tendChartNew);
        let bLength = myChart.getWidth();
        myChart.clear();
        let options = {
          title: {
            left: "center",
            textStyle: {
              color: "#999",
              fontWeight: "normal",
              fontSize: 14,
            },
          },
          color: [
            "#91cc75",
            "#fac858",
            "#ee6666",
            "#73c0de",
            "#3ba272",
            "#fc8452",
            "#9a60b4",
            "#ea7ccc",
            "#c0c0c0",
          ],
          series: {
            type: "pie",
            radius: "55%",
            left: "center",
            width: 800,
            avoidLabelOverlap: true, // 避免标签重叠
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1,
            },
            label: {
              normal: {
                alignTo: "edge",
                formatter: (params) => {
                  let num = params.value
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                  return `\n${params.name}\n{num|${num}}{time|（${params.percent}%）}\n`;
                },
                rich: {
                  time: {
                    fontSize: 12,
                    color: "#999",
                  },
                  num: {
                    fontSize: 12,
                    color: "#999",
                  },
                },
                minMargin: 10,
                edgeDistance: 10,
                lineHeight: 18,
                color: "#333",
                position: "outside",
                padding: 0,
                fontSize: 16,
              },
            },
            labelLine: {
              normal: {
                length: 20,
                length2: bLength * 0.2,
              },
            },
            data: this.tendData,
          },
        };
        myChart.setOption(options);
      })
    },
    changetend(itm) {
      this.tendOrganType = itm.id==99?0:itm.id;
      this.tendOrganName =itm.organName;
      this.tendKeyName =itm.name
      this.getTerminalData();
      this.getUnitRatioData();
    },
    // 处理全零数据，为每个项目分配相等的值用于显示
    processZeroData(data) {
      const total = data.reduce((sum, item) => sum + item.value, 0);

      if (total === 0) {
        // 如果所有数据都是0，给每个项目分配相等的值用于显示
        return data.map(item => ({
          ...item,
          value: 1, // 用于显示的值
          originalValue: item.value, // 保存原始值
          isZeroData: true // 标记这是零数据
        }));
      }

      return data.map(item => ({
        ...item,
        originalValue: item.value,
        isZeroData: false
      }));
    },

    // 计算最优起始角度，避免标签线交叉
    calculateOptimalStartAngle(data) {
      const total = data.reduce((sum, item) => sum + item.value, 0);

      // 如果总数为0，返回默认角度
      if (total === 0) {
        return 90; // 从顶部开始
      }

      let currentAngle = 0;
      const angles = [];

      // 计算每个扇形的角度和位置
      data.forEach(item => {
        const angle = (item.value / total) * 360;
        angles.push({
          name: item.name,
          value: item.value,
          startAngle: currentAngle,
          endAngle: currentAngle + angle,
          centerAngle: currentAngle + angle / 2,
          angle: angle
        });
        currentAngle += angle;
      });

      // 找到最小的扇形，让它们分布在不容易交叉的位置
      const smallSegments = angles.filter(seg => seg.angle < 30); // 小于30度的扇形

      if (smallSegments.length >= 2) {
        // 如果有多个小扇形，让它们分布在对角位置
        // 计算让最小的两个扇形分别在90度和270度附近的起始角度
        const smallest = smallSegments.sort((a, b) => a.angle - b.angle)[0];
        const targetAngle = 90 - smallest.centerAngle; // 让最小扇形的中心在90度
        return (targetAngle + 360) % 360;
      } else {
        // 默认让最大扇形从底部开始，避免标签在顶部重叠
        const largest = angles.sort((a, b) => b.angle - a.angle)[0];
        const targetAngle = 270 - largest.centerAngle; // 让最大扇形的中心在270度（底部）
        return (targetAngle + 360) % 360;
      }
    },

    // 设置单位占比饼图
    setUnitRatioChart() {
      this.$nextTick(() => {
        if (!this.unitRatioData || this.unitRatioData.length === 0) {
          return;
        }

        let myChart = echarts.init(this.$refs.unitRatioChart);
        let bLength = myChart.getWidth();
        myChart.clear();

        // 处理零数据情况
        const processedData = this.processZeroData(this.unitRatioData);
        const isAllZero = processedData.length > 0 && processedData[0].isZeroData;

        // 计算最优起始角度
        const optimalStartAngle = this.calculateOptimalStartAngle(processedData);

        let options = {
          title: {
            left: "center",
            textStyle: {
              color: "#999",
              fontWeight: "normal",
              fontSize: 14,
            },
          },
          color: ['#5470C6', '#FC8251', '#9A60B4'],
          series: {
            type: "pie",
            radius: "55%",
            left: "center",
            width: 800,
            startAngle: optimalStartAngle, // 使用计算出的最优起始角度
            avoidLabelOverlap: true, // 避免标签重叠
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1,
            },
            label: {
              normal: {
                alignTo: "edge",
                formatter: (params) => {
                  // 如果是全零数据，显示原始值0，否则显示实际值
                  let displayValue = isAllZero ? 0 : params.value;
                  let num = displayValue
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ",");

                  // 如果是全零数据，百分比显示为平均分配
                  let percent = isAllZero ?
                    (100 / processedData.length).toFixed(1) :
                    params.percent;

                  return `\n${params.name}\n{num|${num}}{time|（${percent}%）}\n`;
                },
                rich: {
                  time: {
                    fontSize: 12,
                    color: "#999",
                  },
                  num: {
                    fontSize: 12,
                    color: "#999",
                  },
                },
                minMargin: 10,
                edgeDistance: 10,
                lineHeight: 18,
                color: "#333",
                position: "outside",
                padding: 0,
                fontSize: 16,
              },
            },
            labelLine: {
              normal: {
                length: 20,
                length2: bLength * 0.2,
              },
            },
            data: processedData,
          },
        };

        myChart.setOption(options);
      });
    },
    // 获取单位占比数据
    getUnitRatioData() {
      this.unitRatioLoading = true;
      this.unitRatioData = null;

      // 调用真实接口
      this.$http.get(gl.serverURL +'/behavior/userStat', {
        params: {
          organTypes: this.tendOrganType,
          organName: this.tendOrganName,
        startTime: this.customTime
          ? moment(this.customTime[0]).format("YYYY-MM-DD 00:00:00")
          : null, //开始时间
        endTime: this.customTime
          ? moment(this.customTime[1]).format("YYYY-MM-DD 23:59:59")
          : null, //结束时间
        }
      }).then(res => {
        if (res.data ) {
          const data = res.data.data;
          if (data && Array.isArray(data)) {
            // 转换数据格式为饼图需要的格式
            this.unitRatioData = data.map(item => ({
              name: item.name,
              value: item.value || item.count || 0
            }));

    //          this.unitRatioData = [
    //     {
    //         "name": "市委网信办",
    //         "value": 2917
    //     },
    //     {
    //         "name": "区县网信办",
    //         "value": 2289
    //     },
    //     {
    //         "name": "市直单位",
    //         "value": 23382
    //     },
    //     {
    //         "name": "街道乡镇",
    //         "value": 14998
    //     }
    // ]
            } else {
            this.unitRatioData = [];
          }
        } else {
          this.unitRatioData = [];
          this.$Message.error(res.data.message || '获取单位占比数据失败');
        }
        this.unitRatioLoading = false;
        this.$nextTick(() => {
          if (this.unitRatioData && this.unitRatioData.length > 0) {
            this.setUnitRatioChart();
          }
        });
      }).catch(err => {
        console.error('获取单位占比数据失败:', err);
        this.unitRatioLoading = false;
        this.unitRatioData = [];
        this.$Message.error('获取单位占比数据失败');
      });
    },
    // 终端统计饼图
    getTerminalData() {
      this.tendLoading = true;
      this.tendData = null;
      let params = {
        // dayNum:this.selectTime,
        organTypes: this.tendOrganType,
        organName: this.tendOrganName,
        startTime: this.customTime
          ? moment(this.customTime[0]).format("YYYY-MM-DD 00:00:00")
          : null, //开始时间
        endTime: this.customTime
          ? moment(this.customTime[1]).format("YYYY-MM-DD 23:59:59")
          : null, //结束时间
      };
      this.$http
        .get(gl.serverURL + "/behavior/sourceUsed", { params })
        .then((res) => {
          let result = res.body;
          if (result.status == 0) {
            this.tendData = result.data;
            this.tendLoading = false;
            this.$nextTick(() => {
              this.setTerminalCharts();
            });
          }
        });
    },
    // 用户使用趋势图
    getChartData() {
      this.loading = true;
      this.resultData = null;
      let params = {
        startTime: this.customTime
          ? moment(this.customTime[0]).format("YYYY-MM-DD 00:00:00")
          : null, //开始时间
        endTime: this.customTime
          ? moment(this.customTime[1]).format("YYYY-MM-DD 23:59:59")
          : null, //结束时间
        statType: this.statType, //1 按天统计，2按月统计
      };
      this.$http
        .get(gl.serverURL + "/behavior/organUsedStat", { params })
        .then((res) => {
          if (res.body.status === 0 && res.body.data) {
            this.resultData = res.body.data;
            this.loading = false;

            this.$nextTick(() => {
              this.setCharts();
            });
          } else {
            this.$Message.error("服务器错误！");
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;

          this.$Message.error("服务器错误！");
        });
    },
    setCharts() {
      this.$nextTick(() => {
        let myChart = echarts.init(this.$refs.tendChart);
        myChart.clear();
        let that = this;
        let dataY = this.resultData.dataY;
        let dataX = this.resultData.dataX;
        let data = Object.keys(dataY).map((i) => {
          return {
            name: i,
            type: "line",
            smooth: true,
            symbol: "none",
            lineStyle: {
              width: 1,
            },
            data: Object.values(dataY[i]),
          };
        });
        let legendLeft = Object.keys(this.resultData.dataY);
        let option = {
          color: this.colorData,
          grid: {
            left: "4%",
            right: "4%",
            top: "30%",
            bottom: 35,
            containLabel: true,
          },
          tooltip: {
            trigger: "axis",
            confine: true,
            formatter: (params) => {
              let str = "";
              params.forEach((item) => {
                str =
                  str +
                  item.marker +
                  item.seriesName +
                  "：" +
                  item.value +
                  "<br />";
              });
              return str;
            },
          },
          legend: [
            {
              left: 15,
              top: 10,
              icon: "rect",
              width: "90%",
              itemWidth: 12,
              itemHeight: 4,
              itemGap: 10,
              textStyle: {
                color: "rgba(0,0,0,.85)",
                fontSize: 13,
                lineHeight: 18,
              },
              data: legendLeft,
              selected: (() => {
                let obj = {};
                legendLeft.forEach((i) => {
                  if (i !== "总量") {
                    obj[i] = false;
                  }
                });
                return obj;
              })(),
              formatter: function (name) {
                return echarts.format.truncateText(
                  name,
                  80,
                  "12px Microsoft Yahei",
                  "…"
                );
              },
              tooltip: {
                show: true,
              },
            },
          ],
          xAxis: {
            type: "category",
            boundaryGap: false,
            axisLine: {
              show: true,
              lineStyle: {
                color: "#E3E8EC",
              },
            },
            axisTick: {
              show: true,
              alignWithLabel: true,
              lineStyle: {
                color: "#E3E8EC",
              },
            },
            axisLabel: {
              show: true,
              color: "rgba(0,0,0,.55)",
              lineHeight: 17,
              formatter: function (value) {
                if (moment(Number(value)).hour() === 0) {
                  return moment(Number(value)).format("MM-DD");
                } else {
                  return moment(Number(value)).format("MM-DD hh:ss");
                }
              },
            },
            splitLine: {
              show: false,
            },
            data: dataX,
          },
          yAxis: [
            {
              type: "value",
              minInterval: 1,
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                show: true,
                color: "rgba(0,0,0,.55)",
                lineHeight: 17,
                formatter: (value, index) => {
                  if (value < 1000) {
                    return value;
                  }
                  if (value >= 1000 && value < 10000) {
                    return value / 1000 + "k";
                  }
                  if (value >= 10000) {
                    return value / 10000 + "w";
                  }
                  return value;
                },
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: "#E3E8EC",
                },
              },
            },
            {
              type: "value",
              minInterval: 1,
              position: "right",
              axisLabel: {
                show: true,
                color: "rgba(0,0,0,.55)",
                lineHeight: 17,
                formatter: (value, index) => {
                  if (value < 1000) {
                    return value;
                  }
                  if (value >= 1000 && value < 10000) {
                    return value / 1000 + "k";
                  }
                  if (value >= 10000) {
                    return value / 10000 + "w";
                  }
                  return value;
                },
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
                lineStyle: {
                  color: "#E3E8EC",
                },
              },
            },
          ],
          dataZoom: {
            type: "slider",
            show: true,
            bottom: 20,
            right: 50,
            left: 50,
            height: 20,
            labelFormatter: (index) => {
              const curTime = this.resultData.dataX[index];
              if (moment(curTime).hour() === 0) {
                return echarts.format.formatTime("MM-dd", curTime);
              } else {
                return echarts.format.formatTime("MM/dd hh:mm", curTime);
              }
            },
          },
          series: data,
        };
        const SPLIT_NUMBER = 5;
        myChart.setOption(option);
        setTimeout(() => {
          myChart.resize();
        }, 0);
      })
    },
    unitPageNoChange(d) {
      this.unitPageNo = d;
    },

    userPageNoChange(d) {
      this.userPageNo = d;
      this.getUserUsed();
    },
    unitSort(id) {
      let enumList = {
        1: "registerNum",
        2: "activeNum",
        3: "activeRate",
        4: "logNum",
        5: "statNum",
        6: "useNum",
      };
      let key = enumList[id];
      this.unitAllData.sort((item1, item2) => {
        // 首先根据提供的键进行比较
        if (item1[key] < item2[key]) {
          return 1;
        } else if (item1[key] > item2[key]) {
          return -1;
        } else {
          // 如果指定的键相同，则根据 a 属性进行比较（除非key是'a'）
          if (key !== "statNum" && key != "activeRate" && key != "useNum") {
            if (item1.statNum < item2.statNum) {
              return 1;
            } else if (item1.statNum > item2.statNum) {
              return -1;
            }
          }

          if (key == "activeRate") {
            if (item1.useNum < item2.useNum) {
              return 1;
            } else if (item1.useNum > item2.useNum) {
              return -1;
            }
          }

          if (key == "useNum") {
            if (item1.activeRate < item2.activeRate) {
              return 1;
            } else if (item1.activeRate > item2.activeRate) {
              return -1;
            }
          }
          // 如果所有比较都相同，则返回0
          return 0;
        }
      });
    },
    userSearch() {
      this.userPageNo = 1;
      this.deptCode = null;
      this.isActive = null;
      this.getUserUsed();
    },
    dateChange(d) {
      this.userName = "";
      this.organTypes = 0;
      this.organName =""
      this.statType = 1;
      this.userPageNo = 1;
      this.unitPageNo = 1;
      this.statisticsTypes = 1;
      this.deptCode = null;
      this.isActive = null;
      this.getUserUsed();
      this.getUnitUsed();
      this.getChartData();
      //这里修改
      this.getTerminalData();
      this.getUnitRatioData();
      this.getMessageChart();
    },
    // 获取顶部所有数据
    getAllUsed() {
      this.$http.get(gl.serverURL + "/behavior/allUsed").then((res) => {
        this.allUsedData = res.body.data;
      });
    },
    userSort(id) {
      this.getUserUsed({
        orderBy: id,
      });
    },
    // 获取用户使用情况
    getUserUsed(
      obj = {
        orderBy: 2,
      }
    ) {
      this.userLoading = true;
      this.userTableData = [];
      let params = {
        startTime: this.customTime
          ? moment(this.customTime[0]).format("YYYY-MM-DD 00:00:00")
          : null, //开始时间
        endTime: this.customTime
          ? moment(this.customTime[1]).format("YYYY-MM-DD 23:59:59")
          : null, //结束时间
        // dayNum: this.selectTime === 99 ? null : this.selectTime === 365 ? 100 : this.selectTime, // 7近一周，30近一个月，100近一年
        userName: this.userName, //账号搜索
        searchType: this.searchType, // 搜索类型
        organTypes: this.organTypes ? this.organTypes : null, //机构类型 1 市网信办，2区县网信办
        pageSize: 15,
        pageNo: this.userPageNo,
        ...obj,
        deptCode: this.deptCode,
        isActive: this.isActive

      };
      this.$http
        .get(gl.serverURL + "/behavior/userUsed", { params })
        .then((res) => {
          this.userLoading = false;
          this.userTableTotal = res.body.data.total;
          this.userTableData = res.body.data.list
            ? res.body.data.list.map((i) => {
                return {
                  ...i,
                  lastTime: "-",
                };
              })
            : [];
            this.userUsedLastTime(this.userTableData);
        });
    },
    userUsedLastTime(data){
      const ids = data.map((item) => item.accountName).join(',')
      this.$http
        .get(gl.serverURL + "/behavior/userUsedLastTime?userIds=" + ids )
        .then((res) => {
          if(res.body.status === 0){
            data.forEach((item) => {
              item.lastTime = res.body.data[item.accountName] ? moment(res.body.data[item.accountName]).format("YYYY-MM-DD HH:mm:ss") : "-"
            })
          }
        });
    },
    getUnitUsed() {
      this.unitLoading = true;
      this.unitAllData = [];
      this.unitPageNo = 1;
      let params = {
        startTime: this.customTime
          ? moment(this.customTime[0]).format("YYYY-MM-DD 00:00:00")
          : null, //开始时间
        endTime: this.customTime
          ? moment(this.customTime[1]).format("YYYY-MM-DD 23:59:59")
          : null, //结束时间
        dataType: this.statisticsTypes,
        // dayNum: this.selectTime === 99 ? null : this.selectTime === 365 ? 100 : this.selectTime, // 7近一周，30近一个月，100近一年
      };
      this.$http
        .get(gl.serverURL + "/behavior/organUsed", { params })
        .then((res) => {
          this.unitLoading = false;
          this.unitAllData = res.body.data.map((i) => {
            return {
              ...i,
              activeRateStr: i.activeRate + "%",
              useNum:
                i.activeNum != 0 && i.statNum != 0
                  ? Math.round(i.statNum / i.activeNum)
                  : "0",
            };
          });

          this.unitSort(5);
        })
        .catch((err) => {
          this.unitLoading = false;
        });
    },
    nameClick(item,key){
      console.log(key)
      console.log(item)
      //增加定制化传参 判断点击的是哪一列
      if(key == 'organName'){
        //点击单位名称
        this.userName = item.organName
        this.searchType = '2'
        this.deptCode = item.organId
        this.isActive = null;
        if(this.organTypes == item.organType){
          this.getUserUsed()
        }else{
          this.organTypes = item.organType
        }
      }

      if(key == 'registerNum'){
        //点击注册账号
        this.searchType = '2'
        this.deptCode = item.organId
        this.isActive = 0;
        this.userName = "";
        if(this.organTypes == item.organType){
             this.getUserUsed()
        }else{
             this.organTypes = item.organType
        }
      }

      if(key == 'activeNum'){
         //点击活跃账号
         this.searchType = '2'
         this.deptCode = item.organId
         this.isActive = 1;
         this.userName = "";
         if(this.organTypes == item.organType){
              this.getUserUsed()
         }else{
              this.organTypes = item.organType
         }
      }

    },
    formatNumber(num) {
      if(!num){
        return 0
      }else if (num >= 100000000) {
        return (num / 100000000).toFixed(1) + ' 亿';
      } else if (num >= 100000) {
        return (num / 10000).toFixed(1) + ' 万';
      } else {
        return num.toString();
      }
    },
    getMessageChart(){
        this.$http.get(gl.serverURL + "/sys/sms/list/" +  (this.customTime
            ? moment(this.customTime[0]).format("YYYY-MM-DD 00:00:00")
            : '') + ',' +  (this.customTime
          ? moment(this.customTime[1]).format("YYYY-MM-DD 23:59:59")
          : '')).then((res) => {
          this.messageChartData = res.body.data
        })
        this.$http.get(gl.serverURL + "/sys/sms/circle/" +  (this.customTime
            ? moment(this.customTime[0]).format("YYYY-MM-DD 00:00:00")
            : '') + ',' +  (this.customTime
          ? moment(this.customTime[1]).format("YYYY-MM-DD 23:59:59")
          : '')).then((res) => {
          this.messagePieChartData = res.body.data
        })

    }
  },
  // 计算属性 类似于 data 概念
  computed: {
    paginate() {
      // 计算起始索引
      const startIndex = (this.unitPageNo - 1) * 15;
      // 截取指定页的数据
      return this.unitAllData.slice(startIndex, startIndex + 15);
    },
    //获取自定义时间展示
    customPeriod() {
      if (this.customTime && this.customTime[0]) {
        return `${moment(this.customTime[0]).format(
          "YYYY-MM-DD"
        )}  至  ${moment(this.customTime[1]).format("YYYY-MM-DD")}`;
      } else {
        return "请选择时间范围";
      }
    },
    // 统计时段展示
    TimeFrame() {
      this.customTime = null;
      if (this.selectTime === 99) {
        return "请选择时间范围";
      }
      const endDate = moment(); // 当前日期
      const startDate = moment().subtract(this.selectTime - 1, "days"); // 计算起始日期
      this.customTime = [startDate, endDate];
      return `${startDate.format("YYYY-MM-DD")}  至  ${endDate.format(
        "YYYY-MM-DD"
      )}`;
    },
  },
  // 监控 data 中的数据变化
  watch: {
    statisticsTypes() {
      this.getUnitUsed();
    },
    selectTime(d) {
      if (d !== 99) {
        this.userName = "";
        this.organTypes = 0;
        this.statType = 1;
        this.userPageNo = 1;
        this.unitPageNo = 1;
        this.statisticsTypes = 1;
        this.deptCode = null
        this.$nextTick(() => {
          this.getUserUsed();
          this.getUnitUsed();
          this.getChartData();
          this.getTerminalData();
          this.getUnitRatioData();
          this.getMessageChart();
        });
      } else {
        this.customTime = null;
      }
    },
    organTypes() {
      this.userPageNo = 1;
      if(this.organTypes == 0){
        this.userName = ''
        this.searchType = '1'
      }
      this.getUserUsed();
    },
    statType() {
      this.getChartData();
    },
  },
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getUserUsed();
    this.getUnitUsed();
    this.getChartData();
    this.getTerminalData();
    this.getUnitRatioData();
    this.getMessageChart();
  },
};
</script>

<style lang="less" scoped>
.ControlsDetails {
  background-color: #fbf7fa;

  .header {
    display: flex;
    justify-content: center;
    padding: 30px;
    background: linear-gradient(to bottom, #015dc9, #2d8cf0);
    flex-wrap: wrap;

    .title {
      text-align: center;
      width: 100%;
      font-size: 50px;
      color: #fff;
      font-weight: 600;
      font-family: "Microsoft YaHei";
      text-shadow: 4px 4px 2px rgba(0, 0, 0, 0.5);
    }

    .StatisticalData {
      margin-top: 20px;
      width: 1400px;
      display: flex;
      justify-content: space-between;

      .item {
        display: flex;
        align-items: flex-end;

        .label {
          font-size: 20px;
          color: #fff;
        }

        .value {
          color: #ffde00;
          font-size: 26px;
          font-weight: 600;
          line-height: 33px;
        }
      }
    }
  }

  .StatisticalPeriod {
    background-color: #fff;
    padding: 20px 10px;
    margin: 20px 20px 10px;
    border-radius: 5px;
    font-size: 16px;
    display: flex;
    justify-content: space-between;

    .optionsList {
      display: flex;
      align-items: center;
    }
  }

  .tableFrame {
    margin: 0 20px;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    &.tableFrame-mobile{
      justify-content: space-between;
    }

    .searchCondition {
      margin-left: 10px;
      flex: 1;
      display: flex;
      justify-content: right;
      /deep/.ivu-input-group-append .ivu-select-selection, /deep/.ivu-input-group-prepend .ivu-select-selection{
        background-color: inherit;
        margin: -1px;
        border: 1px solid transparent;
      }
    }
  }
}

.list {
  display: flex;
  align-items: center;

  .item {
    border-radius: 5px;
    border: 1px solid #a4a3a3;
    color: #a4a3a3;
    font-size: 14px;
    line-height: 20px;
    padding: 0 5px;
    cursor: pointer;
    margin: 0 5px;
    height: 22px;
  }

  .active {
    border-color: #318ef0;
    color: #318ef0;
  }
}

.ivu-page {
  text-align: center;
}
.zdChartbox{
  display: flex;
  flex-direction: row;
  height:350px;
  .part{
    min-height:100%;
    flex:1;
    // border:1px solid red;
    max-width:50%;
 
  
    
  }
}

.unit-ratio-container {
  padding: 10px;
  height: 100%;

  .unit-ratio-title {
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e8e8e8;
  }
}

/deep/ .ivu-icon-ios-search {
  background-color: rgba(0, 0, 0, 0);
  color: #666;
}

.chart {
  height: 350px;
}
</style>
