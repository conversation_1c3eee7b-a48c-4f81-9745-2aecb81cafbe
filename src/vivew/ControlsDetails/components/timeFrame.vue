<template>
  <div class="model">
    <nodata-page
      v-if="!loading && chartData === null"
      :classVal="'nodata2'"
    ></nodata-page>
    <Spin v-if="loading">
      <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
      <div>Loading</div>
    </Spin>
    <div v-if="chartData" ref="chart" class="chart"></div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import moment from "moment";
import echarts from "echarts";
export default {
  data() {
    // 这里存放数据
    return {
      loading: true,
      chartData: null,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    startTime: {
      default: null,
    },
    endTime: {
      default: null,
    },
    type: {
      default: "",
    },
    chartType: {
      default: 1,
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    moment,
    getChartData() {
      let params = {
        startTime: this.startTime,
        endTime: this.endTime,
        sysCodes: this.type,
        startTime: this.startTime,
        endTime: this.endTime,
      };
      this.$http
        .get(gl.serverURL + "/behavior/timeUsedStat", { params })
        .then((res) => {
          console.log(res);
          this.chartData = res.body.data;
          this.loading = false;

          // this.chartData = [
          //   {
          //     "0点": 0,
          //     "1点": 0,
          //     "2点": 0,
          //     "3点": 0,
          //     "4点": 0,
          //     "5点": 0,
          //     "6点": 0,
          //     "7点": 5,
          //     "8点": 16,
          //     "9点": 91,
          //     "10点": 30,
          //     "11点": 0,
          //     "12点": 0,
          //     "13点": 0,
          //     "14点": 0,
          //     "15点": 0,
          //     "16点": 0,
          //     "17点": 15,
          //     "18点": 192,
          //     "19点": 194,
          //     "20点": 76,
          //     "21点": 13,
          //     "22点": 0,
          //     "23点": 0,
          //   },
          //   {
          //     "0点": 0,
          //     "1点": 0,
          //     "2点": 0,
          //     "3点": 0,
          //     "4点": 0,
          //     "5点": 0,
          //     "6点": 0,
          //     "7点": 0,
          //     "8点": 166,
          //     "9点": 65,
          //     "10点": 32,
          //     "11点": 0,
          //     "12点": 0,
          //     "13点": 0,
          //     "14点": 0,
          //     "15点": 0,
          //     "16点": 0,
          //     "17点": 2,
          //     "18点": 3,
          //     "19点": 18,
          //     "20点": 24,
          //     "21点": 0,
          //     "22点": 0,
          //     "23点": 0,
          //   },
          // ];
          this.$nextTick(() => {
            if (this.chartType === 1) {
              this.setLineChart();
            } else {
              this.setChart();
            }
            // this.setChart();
          });
        });
    },
    setLineChart() {
      let myChart = echarts.init(this.$refs.chart);
      let arr1 = Object.values(this.chartData[0]);
      let arr2 = Object.values(this.chartData[1]);
      console.log(arr1, arr2, count);
      let count = arr1.map((i, index) => {
        return i + arr2[index];
      });
      console.log(arr1, arr2, count);

      console.log(count);
      myChart.clear();
      let option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["总量", "市网信办", "区县网信办"],
          selected: {
            市网信办: false,
            区县网信办: false,
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: Object.keys(this.chartData[0]),
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: "总量",
            type: "line",
            // stack: "Total",
            data: count,
            smooth: true,
          },
          {
            name: "市网信办",
            type: "line",
            // stack: "Total",
            data: arr1,
            smooth: true,
          },
          {
            name: "区县网信办",
            type: "line",
            // stack: "Total",
            data: arr2,
            smooth: true,
          },
        ],
      };
      myChart.setOption(option);
    },
    setChart() {
      let title = [
        {
          textBaseline: "middle",
          top: "33.33%",
          text: "市网信办",
        },
        {
          textBaseline: "middle",
          top: "66.66%",
          text: "区县网信办",
        },
      ];
      let hours = Object.keys(this.chartData[0]);
      console.log(hours);
      let singleAxis = [
        {
          left: 150,
          type: "category",
          boundaryGap: false,
          data: hours,
          top: "30%",
          height: "4.2857142857142865%",
          axisLabel: {
            interval: 0,
          },
        },
        {
          left: 150,
          type: "category",
          boundaryGap: false,
          data: hours,
          top: "60%",
          height: "4.2857142857142865%",
          axisLabel: {
            interval: 0,
          },
        },
      ];
      //取基数
      console.log(this.chartData[0], this.chartData[1]);
      let Cardinal_1 = Math.max(...Object.values(this.chartData[0])) / 10;
      let Cardinal_2 = Math.max(...Object.values(this.chartData[1])) / 10;
      let series = [
        {
          singleAxisIndex: 0,
          coordinateSystem: "singleAxis",
          type: "scatter",
          data: Object.values(this.chartData[0]).map((item, index) => {
            return [index, item];
          }),
          symbolSize: function (dataItem) {
            return Math.round(dataItem[1] / Cardinal_1) * 3;
          },
        },
        {
          singleAxisIndex: 1,
          coordinateSystem: "singleAxis",
          type: "scatter",
          data: Object.values(this.chartData[1]).map((item, index) => {
            return [index, item];
          }),
          symbolSize: function (dataItem) {
            return Math.round(dataItem[1] / Cardinal_2) * 3;
          },
        },
      ];
      console.log(series);
      console.log(this.$refs.chart);
      let myChart = echarts.init(this.$refs.chart);
      myChart.clear();
      let options = {
        tooltip: {
          position: "top",
          backgroundColor: "#fff", // 背景色
          formatter: function (params) {
            return `
            <div style=" border: 2px solid ${params.color};
              border-radius: 8px;
              padding: 5px 10px;
              color: #333;
              font-size: 12px;
              background-color: #fff;
               box-shadow: 1px 1px 5px 1px ${params.color};
              ">
              ${params.marker}${params.name}， ${params.value[1]}次
            </div>

            `;
          },
        },
        title: title,
        singleAxis: singleAxis,
        series: series,
      };
      console.log(options);

      myChart.setOption(options);
      console.log(111);
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    chartType(val) {
      if (val === 1) {
        this.setLineChart();
      } else {
        this.setChart();
      }
    },
    type(){
      this.getChartData()
    }
  },
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getChartData();
  },
};
</script>
<style scoped lang="less">
.model {
  height: 350px;
  .ivu-spin {
    margin-top: 200px;
  }
  .chart {
    height: 350px;
  }
}
</style>
