<!--
 * @Author: yang<PERSON>shan <EMAIL>
 * @Date: 2025-04-08 14:37:41
 * @LastEditors: yangfushan <EMAIL>
 * @LastEditTime: 2025-04-10 16:04:14
 * @FilePath: \jn_home_web\src\vivew\ControlsDetails\components\messageChart.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="chart-container" :class="{ hasline: hasLine }">
        <div class="chart-item">
            <div class="charts" id="messageLineBarChart" style=""></div>
            <div class="message-count">
                短信使用总量（发送成功）: <span style="color: #6792ee;"> {{ messageCount }}</span> 条
            </div>
        </div>
        <div class="chart-item">
            <div
                class="charts"
                id="messagePieChart"
                style="width: 918px; height: 300px"
            ></div>
        </div>
    </div>
</template>

<script>
import echarts from "echarts";
import moment from "moment";

export default {
    name: "messageChart",
    data() {
        return {
            messageCount: 0,
            lineBarChart: null,
            pieChart: null,
        };
    },
    props: {
        messageChartData: {
            type: Object,
            default: () => {},
        },
        messagePieChartData: {
            type: Array,
            default: () => [],
        },
        hasLine: {
            type: Boolean,
            default: false,
        },
    },
    mounted() {
        // this.initLineBarChart();
        // this.initPieChart();
        // 监听窗口大小变化，调整图表大小
        // window.addEventListener("resize", this.handleResize);
    },
    beforeDestroy() {
        // window.removeEventListener("resize", this.handleResize);
        if (this.lineBarChart) {
            this.lineBarChart.dispose();
        }
        if (this.pieChart) {
            this.pieChart.dispose();
        }
    },
    methods: {
        handleResize() {
            if (this.lineBarChart) {
                this.lineBarChart.resize();
            }
            if (this.pieChart) {
                this.pieChart.resize();
            }
        },
        initLineBarChart() {
            if (!this.messageChartData || this.messageChartData.data.length === 0) {
                return;
            }
            const dataArr = this.messageChartData.data.map((item) => {
                return {
                    name: moment(item.key).format("MM-DD"),
                    value: item.value,
                };
            });
            // 初始化柱状图+折线图
            const chartDom = document.getElementById("messageLineBarChart");
            this.lineBarChart = echarts.init(chartDom);

            const option = {
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "shadow",
                    },
                },
                grid: {
                    left: "3%",
                    right: "4%",
                    bottom: "3%",
                    containLabel: true,
                },
                xAxis: {
                    type: "category",
                    data: dataArr.map((item) => item.name),
                    //   axisLabel: {
                    //     interval: function(index, value) {
                    //       // 只显示部分标签，类似于图片中的效果
                    //       return index % 2 === 0;
                    //     },
                    //     rotate: 0,
                    //     align: 'center'
                    //   },
                    axisTick: {
                        alignWithLabel: true,
                    },
                },
                yAxis: {
                    type: "value"
                },
                series: [
                    {
                        name: "短信发送量",
                        type: "bar",
                        barWidth: "40%",
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(
                                0,
                                0,
                                0,
                                1,
                                [
                                    { offset: 0, color: "#a4baed" },
                                    { offset: 1, color: "#c6ddf7" },
                                ]
                            ),
                        },
                        data: dataArr.map((item) => item.value),
                    },
                    {
                        name: "短信发送量",
                        type: "line",
                        smooth: false,
                        data: dataArr.map((item) => item.value),
                        lineStyle: {
                            color: "#5685eb",
                            width: 2,
                        },
                        symbol: "none",
                        symbolSize: 0,
                        tooltip: {
                            show: false
                        }
                    },
                ],
            };

            this.lineBarChart.setOption(option);
        },
        initPieChart() {
            // 初始化饼图
            if (!this.messagePieChartData || this.messagePieChartData.length === 0) {
                return;
            }
            const dataArr = this.messagePieChartData.map((item) => {
                return {
                    value: item.count,
                    name: item.name, 
                };
            });
            // console.log(dataArr, "dataArr"); #1890ff
          
            const chartDom = document.getElementById("messagePieChart");
            this.pieChart = echarts.init(chartDom);

            const option = {
                tooltip: {
                    trigger: "item",
                    formatter: "{b}: {c} ({d}%)",
                },
                legend: {
                    show: false,
                },
                series: [
                    {
                        name: "短信使用分布",
                        type: "pie",
                        radius: ["40%", "70%"],
                        avoidLabelOverlap: true,
                        itemStyle: {
                            borderRadius: 0,
                            borderColor: "#fff",
                            borderWidth: 2,
                        },
                        label: {
                            show: true,
                            position: "outside",
                            formatter: "{b}\n{c}条 ({d}%)",
                            fontSize: 12,
                            color: "#666",
                            lineHeight: 18,
                            alignTo: "labelLine",
                            distanceToLabelLine: 5,
                            padding: [0, 0, 0, 0]
                        },
                        labelLine: {
                            show: true,
                            length: 15,
                            length2: 80,
                            minTurnAngle: 90,
                            smooth: false
                        },
                        data: dataArr.map((item, index) => {
                            return {
                                value: item.value,
                                name: item.name,
                                itemStyle: { color: index % 4 === 0 ? "#FC8251" : index % 4 === 1 ? "#5470C6" : index % 4 === 2 ? "#9A60B4" : "#d53a35" },
                                label: {
                                    formatter: "{b}{c}条 ({d}%)",
                                },
                                labelLine: {
                                    lineStyle: {
                                        color: index % 4 === 0 ? "#FC8251" : index % 4 === 1 ? "#5470C6" : index % 4 === 2 ? "#9A60B4" : "#d53a35",
                                    }
                                }
                            };
                        }),
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 14,
                                fontWeight: "bold"
                            }
                        }
                    }
                ]
            };

            this.pieChart.setOption(option);
        },
    },
    watch: {
        messageChartData: {
            handler(newVal) {
                if (newVal) {
                    this.initLineBarChart();
                    this.messageCount = newVal.totalCount
                }
            },
            deep: true,
            immediate: true,
        },
        messagePieChartData: {
            handler(newVal) {
                if (newVal) {
                    this.initPieChart();
                }
            },
            deep: true,
            immediate: true,
        },
        hasLine: {
            handler(newVal) {
                console.log(newVal, "newVal");
            },
        },
    },
};
</script>

<style lang="less" scoped>
.chart-container {
    max-width: 100%;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    flex-wrap: wrap;
    position: relative;
    &.hasline {
        &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 50%;
            bottom: 0;
            border-left: 3px dashed #ccc;
            // transform: translateX(-50%);
        }
    }
    .chart-item {
        width: 918px;
        height: 350px;
        margin-bottom: 20px;
        .charts {
            width: 100%;
            height: 90%;
        }
    }
    .message-count {
        text-align: center;
    }
}

.message-count {
    text-align: left;
    font-size: 14px;
    color: #666;
    margin-top: 10px;
    padding-left: 20px;
}
</style>
