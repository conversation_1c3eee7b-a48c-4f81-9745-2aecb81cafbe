<template>
  <div class="tableList">
    <div class="header row">
      <div class="item">序号</div>
      <div
        v-for="item in rule"
        :key="item.key"
        :style="item.style"
        class="item"
      >
        {{ item.label }}
        <Icon
          v-if="item.sort"
          :style="{ color: sortKey === item.sortId ? '#409EFF' : '' }"
          type="md-arrow-down"
          @click.native="switchSort(item.sortId)"
        />
      </div>
    </div>
    <div class="listBox">
      <nodata-page
        v-if="!loading && tableData.length === 0"
        :classVal="'nodata2'"
      ></nodata-page>
      <Spin v-if="loading">
        <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
        <div>Loading</div>
      </Spin>
      <div v-for="(item, index) in tableData" :key="index" class="row">
        <div class="item">
          {{ (pageNo - 1) * 15 + index + 1 }}
        </div>
        <div
          v-for="i in rule"
          :key="i.key + index"
          :style="i.style"
          :title="item[i.key]"
          class="item"
        >
          <template v-if="isClick && (i.key == 'organName'||i.key == 'registerNum'||i.key == 'activeNum')">
            <span style="cursor: pointer;" @click="emitClick(item,i.key)">{{ i.key == 'registerNum' && item['qxRegisterNum'] != 0  ? item[i.key] + "(" + item['qxRegisterNum'] + ")" : 
              i.key == 'activeNum' && item['qxActiveNum'] != 0   ? item[i.key] + "(" + item['qxActiveNum'] + ")" : item[i.key]}}</span>
          </template>
          <template v-else>
             {{ item[i.key] }}
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import NodataPage from "../../SystemConfiguration/components/nodataPage.vue";

export default {
  name: "tableList",
  data() {
    // 这里存放数据
    return {
      sortType: "asc",
      sortKey: null,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: { NodataPage },
  props: {
    rule: {
      default: [],
    },
    defaultSortKey: {
      default: "",
    },
    tableData: {
      default: () => [],
    },
    loading: {
      default: false,
    },
    pageNo: {
      default: 0,
    },
    isClick: {
      default: false
    }
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    switchSort(key) {
      if (this.sortKey !== key) {
        this.sortKey = key;
        this.$emit("on-sort", key);
      }
    },
    emitClick(item,key){
      //增加定制化传参 判断点击的是哪一列
      this.$emit('nameClick', item,key)
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.sortKey = this.defaultSortKey;
  },
};
</script>

<style lang="less" scoped>
.tableList {
  .header {
    background-color: #e1d9d9;
    display: flex;
    font-weight: 600;
    font-size: 20px;
    line-height: 40px;

    /deep/ .ivu-icon {
      font-size: 20px;
      cursor: pointer;
    }
  }

  .listBox {
    height: 450px;

    .row {
      height: 30px;
      border-bottom: 2px solid #f0ecec;
      border-right: 2px solid #f0ecec;

      &:nth-child(2n) {
        background-color: #fbf7fa;
      }

      .item {
        line-height: 20px;
        border-left: 2px solid #f0ecec;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        //margin-left: -1px;
      }
    }
  }

  .row {
    display: flex;
    justify-content: space-between;

    .item {
      padding: 0 10px;
      text-align: center;
      min-width: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
