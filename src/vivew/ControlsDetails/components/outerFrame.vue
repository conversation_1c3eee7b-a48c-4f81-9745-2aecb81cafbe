<template>
  <div class="outerFrame">
    <div class="header flex">
      <div class="line"></div>
      <div class="title">{{ title }}</div>
      <slot name="header" />
    </div>
    <slot />
    <Spin v-if="loading" fix>
      <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
export default {
  data() {
    // 这里存放数据
    return {};
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    title: {
      default: "标题",
    },
    loading: {
      default: false,
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {},
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {},
};
</script>
<style scoped lang="less">
.outerFrame {
  position: relative;
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  padding: 10px;
  margin-bottom: 20px;
  .header {
    align-items: center;
    height: 40px;
    .line {
      width: 6px;
      height: 15px;
      background: #5585ec;
      margin-right: 10px;
    }
    .title {
      font-weight: 600;
      color: #5d5d5d;
      font-size: 20px;
    }
  }
}
</style>
