<template>
  <div class="model">
    <div class="toggle">
      <Icon type="md-arrow-dropleft" @click.native="previous" />
      <span>
        {{ realModuleList[moduleListIndex].name }}
      </span>
      <Icon type="md-arrow-dropright" @click.native="next" />
    </div>
    <nodata-page
      v-if="!loading && chartData === null"
      :classVal="'nodata2'"
    ></nodata-page>
    <Spin v-if="loading">
      <Icon class="demo-spin-icon-load" size="18" type="ios-loading"></Icon>
      <div>Loading</div>
    </Spin>
    <div v-if="chartData" ref="chart" class="chart"></div>
  </div>
</template>
<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';
import moment from "moment";
import echarts from "echarts";
export default {
  data() {
    // 这里存放数据
    return {
      loading: true,
      chartData: null,
      baseData: [],
      moduleList: [
        {
          key: 'trs_sdzb,trs_jccz',
          name: '网络舆情中心',
          children: [
            {
              key: 'trs_jccz',
              name: '互联网舆情监测处置评价系统'
            },
            {
              key: 'trs_sdzb',
              name: '泉城哨点直报系统'
            }
          ]
        },
        {
          key: 'trs_wx,sys_code_wp',
          name: '网络传播中心',
          children: [
            {
              key: 'trs_wx',
              name: '智慧网宣管理系统'
            },
            {
              key: 'sys_code_wp',
              name: '网评管理系统'
            }
          ]
        },
        {
          key: 'sys_code_wa',
          name: '网络安全中心',
          children: [
            {
              key: 'sys_code_wa',
              name: '网安系统'
            },
          ]
        },
        {
          key: 'sw_jbxt',
          name: '网络治理中心',
          children: [
            {
              key: 'sw_jbxt',
              name: '违法和不良信息举报系统'
            },
            // {
            //   key: 'sw_jbxt',
            //   name: '互联网联合辟谣系统'
            // },
          ]
        }
      ],
      toggleList: {
        trs_jccz: "互联网舆情监测处置评价系统",
        trs_sdzb: "泉城哨点直报系统",
      },
      toggleId: "trs_jccz",
      moduleListIndex: 0
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    startTime: {
      default: null,
    },
    endTime: {
      default: null,
    },
    type: {
      default: "",
    },
    chartType: {
      default: 1,
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    moment,
    getBaseData(){
      this.loading = true
      let params = {
        startTime: this.startTime,
        endTime: this.endTime,
      };
      this.$http
        .get(gl.serverURL + "/behavior/moduleUsedStatAll", { params })
        .then((res) => {
          this.loading = false;
          if(res.body.data){
            for(let i in res.body.data){
               this.baseData.push({
                name: i,
                value: res.body.data[i]
               })
            }
          }
         
          this.$nextTick(() => {
            if (this.chartType === 1) {
              this.setPieChart();
            } else {
              this.setChart();
            }
          });
        });
    },
    previous() {
      if(this.realModuleList.length == 1) return
      if(this.moduleListIndex == 0){
       this.moduleListIndex = this.realModuleList.length - 1
      }else{
        this.moduleListIndex--;
      }
      this.toggleId = this.realModuleList[this.moduleListIndex].key
      this.getChartData();
    },
    //下一个
    /**
     * 触发下一个操作的空方法。
     * 该方法当前未实现具体逻辑，可能需要根据业务需求进一步定义。
     */
    next() {
      if(this.realModuleList.length == 1) return
      if(this.moduleListIndex == this.realModuleList.length - 1){
        this.moduleListIndex = 0
      }else{
        this.moduleListIndex++;
      }
      this.toggleId = this.realModuleList[this.moduleListIndex].key
      this.getChartData();
    },
    setPieChart() {
      console.log(this.chartData);
      let myChart = echarts.init(this.$refs.chart);
      myChart.clear();
      let count = 0;
      Object.values(this.chartData).forEach((element) => {
        count += element;
      });
      console.log(count);

      let option = {
        tooltip: {
          trigger: "item",
        },
        legend: [
          {
            top: "5%",
            left: "0%",
            textStyle: {
              color: "#333",
            },
            width: 44,
            data: this.baseData.length > 0 ? this.baseData.map(item => item.name) : [],
          },
          {
            top: "5%",
            right: "0%",
            textStyle: {
              color: "#333",
            },
            width: 88,
            data: Object.keys(this.chartData),
          },
        ],
        series: [
          {
            legend: {
              top: "5%",
              left: "center",
            },
            width: 440,
            type: "pie",
            radius: ["20%", "70%"],
            avoidLabelOverlap: false,
            padAngle: 5,
            // left: "%",
            itemStyle: {
              borderRadius: 10,
              borderWidth: 2,
              borderColor: "#fff",
            },
            label: {
              show: false,
              position: "center",
            },
            labelLine: {
              show: false,
            },
            data: this.baseData.length > 0 ? this.baseData : [],
          },

          {
            type: "pie",
            width: 440,
            radius: ["20%", "70%"],
            avoidLabelOverlap: false,
            padAngle: 5,
            left: "46%",
            itemStyle: {
              borderRadius: 10,
              borderWidth: 2,
              borderColor: "#fff",
            },
            label: {
              show: false,
              position: "center",
            },
            labelLine: {
              show: false,
            },
            data: Object.keys(this.chartData).map((key) => {
              return {
                name: key,
                value: this.chartData[key],
              };
            }),
          },
        ],
      };

      myChart.setOption(option);
    },
    getChartData() {
      let params = {
        startTime: this.startTime,
        endTime: this.endTime,
        sysCode: this.toggleId,
        startTime: this.startTime,
        endTime: this.endTime,
      };
      // this.chartData = {
      //   涉济监测: 5059,
      //   舆情提示: 649,
      //   事件分析: 262,
      //   网情要报: 163,
      //   评价考核: 60,
      //   通知管理: 21,
      //   知识管理: 17,
      // };
      // this.loading = false;
      // this.$nextTick(() => {
      //   this.setChart();
      // });

      // return;
      this.$http
        .get(gl.serverURL + "/behavior/moduleUsedStat", { params })
        .then((res) => {
          this.loading = false;
          console.log(res);
          this.chartData = res.body.data;
          // this.chartData = {
          //   涉济监测: 5059,
          //   舆情提示: 649,
          //   事件分析: 262,
          //   网情要报: 163,
          //   评价考核: 60,
          //   通知管理: 21,
          //   知识管理: 17,
          // };
          this.$nextTick(() => {
            if (this.chartType === 1) {
              this.setPieChart();
            } else {
              this.setChart();
            }
          });
        });
    },
    setChart() {
      let myChart = echarts.init(this.$refs.chart);
      myChart.clear();
      let option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: Object.keys(this.chartData),
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        series: [
          {
            name: "总量",
            type: "bar",
            barWidth: "60%",
            itemStyle: {
              color: "#5470C6",
            },
            data: Object.values(this.chartData),
          },
        ],
      };
      myChart.setOption(option);
    },
  },
  // 计算属性 类似于 data 概念
  computed: {
    realModuleList(){
      const list = this.moduleList.find(item => item.key == this.type)
      return list  ? list.children : []
    }
  },
  // 监控 data 中的数据变化
  watch: {
    chartType(val) {
      if (val === 1) {
        this.setPieChart();
      } else {
        this.setChart();
      }
    },
    type: {
      handler(newVal){
        if(newVal){
          this.moduleListIndex = 0
          this.$nextTick(() => {
            this.toggleId = this.realModuleList[this.moduleListIndex].key
            this.getChartData()
          })
        }
      },
      immediate: true
    }
  },
  //过滤器
  filters: {},
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getBaseData()
    // this.getChartData();
  },
};
</script>
<style scoped lang="less">
.model {
  height: 350px;
  .ivu-spin {
    margin-top: 200px;
  }
  .toggle {
    position: absolute;
    top: 50px;
    left: 72%;
    transform: translateX(-50%);
    color: #5097d2;
    font-weight: 600;
    z-index: 2;
    /deep/.ivu-icon {
      font-size: 18px;
      cursor: pointer;
    }
  }
  .chart {
    height: 350px;
  }
}
</style>
