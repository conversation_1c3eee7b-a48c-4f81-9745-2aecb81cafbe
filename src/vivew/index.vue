<template>
  <div class="frame">
    <router-view />
  </div>
</template>
<script>
export default {
  components: {},
  created() {
    // this.setViewportScale();
  },
  methods: {
    // setViewportScale() {
    //   const originalWidth = 1920; // 原始宽度
    //   const currentWidth = window.innerWidth;
    //   // 计算宽度和高度的缩放比例
    //   const scaleWidth = currentWidth / originalWidth;
    //   this.updateMetaViewport(scaleWidth);
    // },
    // updateMetaViewport(scale) {
    //   console.log(scale);
    //   let viewportMeta = document.querySelector('meta[name="viewport"]');
    //   if (!viewportMeta) {
    //     // 如果没有 viewport meta 标签，创建一个
    //     viewportMeta = document.createElement("meta");
    //     viewportMeta.name = "viewport";
    //     document.head.appendChild(viewportMeta);
    //   }
    //   // 设置 viewport meta 标签的 content 属性
    //   viewportMeta.setAttribute(
    //     "content",
    //     `width=device-width, initial-scale=${scale}, maximum-scale=${scale}, user-scalable=no`
    //   );
    // },
  },
  mounted() {
    console.log(this.$route);
    let time = new Date();
    localStorage.setItem("overtime", time);
    window.addEventListener("mousedown", () => {
      let curtime = new Date();
      let forntTime = new Date(localStorage.getItem("overtime"));
      if (curtime - forntTime > 24 * 60 * 60 * 1000) {
        localStorage.removeItem("resources");
        localStorage.removeItem("sysNos");
        localStorage.removeItem("userName");
        sessionStorage.removeItem("resources");
        sessionStorage.removeItem("sysNos");
        sessionStorage.removeItem("userName");
        location.reload();
      } else {
        localStorage.setItem("overtime", curtime);
      }
    });
  },
};
</script>
<style scoped lang="less">
body,
html {
  padding: 0;
  margin: 0;
}
.frame {
  width: 100%;
  height: 100%;
}
</style>
