<!-- 入口页 -->
<template>
  <div class="frame">
    <div class="content">
      <div
        v-for="p in list"
        :key="p.id"
        class="page"
        :class="{ pageShow: pageNo == p.id }"
        style="position: absolute; top: 0px; left: 0px;"
      >
        <div
          v-for="(c, index) in p.list"
          :key="index"
          class="itm"
          :style="{
            'margin-bottom': index + 1 == 3 ? '250px' : '',
            'transition-delay': index * 0.3 + 's',
          }"
        >
          <span class="number">{{ (p.id - 1) * 6 + index + 1 }}</span>
          <div style="margin-left: 50px;">
            <div
              style="margin-bottom: 90px; font-size: 60px;"
              :style="{
                'max-width': index == 2 || index == 3 ? '1450px' : '1650px',
              }"
              class="ellipsis"
            >
              {{ c.mTitle ? c.mTitle : c.mContent }}
            </div>
            <div style="font-size: 40px;">
              <span
                >{{ situationJSON[c.situation]
                }}{{ c.uname ? "-" + c.uname : "-" + c.mWebsiteName }}</span
              >
              <span style="margin-left: 10px;">{{
                moment(c.publishTime).format("YYYY-MM-DD HH:mm:ss")
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="time">{{ moment(nowtime).format("YYYY-MM-DD HH:mm:ss") }}</div>
  </div>
</template>

<script>
import moment from "moment";
import situationJSON from "@/assets/json/situation.json";

export default {
  data() {
    return {
      situationJSON,
      nowtime: new Date(), //获取当前时间
      list: [],
      pageNo: 1,
    };
  },
  //生命周期 - 创建完成（访问当前this实例）
  created() {
    // this.setViewportScale();
  },
  //方法所在
  methods: {
    moment,
    setViewportScale() {
      const originalWidth = 2160; // 原始宽度
      const currentWidth = window.innerWidth;
      // 计算宽度和高度的缩放比例
      const scaleWidth = currentWidth / originalWidth;
      this.updateMetaViewport(scaleWidth);
    },
    updateMetaViewport(scale) {
      console.log(scale);
      let viewportMeta = document.querySelector('meta[name="viewport"]');
      if (!viewportMeta) {
        // 如果没有 viewport meta 标签，创建一个
        viewportMeta = document.createElement("meta");
        viewportMeta.name = "viewport";
        document.head.appendChild(viewportMeta);
      }
      // 设置 viewport meta 标签的 content 属性
      viewportMeta.setAttribute(
        "content",
        `width=device-width, initial-scale=${scale}, maximum-scale=${scale}, user-scalable=no`
      );
    },
    getList() {
      this.$http
        .get(gl.jcczAPI + "/common/everydayOpinion")
        .then((response) => {
          let data = response.body;
          if (data.status == 0) {
            let newList = [
              { id: 1, list: [] },
              { id: 2, list: [] },
              { id: 3, list: [] },
              { id: 4, list: [] },
              { id: 5, list: [] },
            ];
            newList.forEach((v) => {
              v.list = data.data.splice(0, 6);
            });

            this.list = newList;
          } else {
            this.$Message.error({
              content: data.message,
              duration: 3,
              closable: true,
            });
          }
        })
        .finally(() => {
          setTimeout(() => this.getList(), 5 * 60 * 1000);
        });
    },
  },
  beforeDestroy() {
    // 清除定时器，防止内存泄漏
    clearInterval(this.timer);
  },
  //生命周期 - 挂载完成（访问DOM元素）
  mounted() {
    document.title = "每日舆情";
    this.getList();

    var count = 0;
    // 动态时间显示
    this.timer = setInterval(() => {
      count++;
      this.nowtime = new Date();

      if (count % 10 == 0) {
        // 每个10秒
        var nextNo = this.pageNo + 1;
        this.pageNo = nextNo > this.list.length ? 1 : nextNo;
      }
    }, 1000);
  },
};
</script>
<style lang="less" scoped>
.frame {
  position: relative;
  width: 2160px;
  height: 3840px;
  /* transform: scale(1.1);
transform-origin: top left; 
 */
  background: url("../../assets/img/advertisement.svg") no-repeat center center;
  .content {
    position: relative;
    top: 625px;
    left: 148px;
    width: 1900px;
    height: 2900px;
    overflow: hidden;
    font-family: Microsoft YaHei;
    font-size: 30px;
    color: #fff;
    .page {
      z-index: 1;
      &.pageShow {
        z-index: 10;
        .itm {
          opacity: 1;
          transition: all 1.5s 0s;
          transform: scaleY(1);
          .number {
          }
        }
      }
      .itm {
        opacity: 0;
        transition: all 1.5s 0s;
        transform: scaleY(0);
        display: flex;
        margin-bottom: 280px;
        height: 238px;
        align-items: center;
        .number {
          display: inline-block;
          height: 120px;
          min-width: 129px;
          max-width: 160px;
          text-align: center;
          font-weight: 900;
          color: #8ffffe;
          font-size: 100px;
        }
      }
    }
  }
  .time {
    position: absolute;
    bottom: 114px;
    left: 740px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #ffffff;
    font-size: 75px;
  }
}
</style>
