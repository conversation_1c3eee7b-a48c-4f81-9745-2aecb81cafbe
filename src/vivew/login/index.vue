<template>
  <div class="inputFrame">
    <div class="title">登录中心</div>
    <Input
      prefix="ios-contact"
      placeholder="请输入用户名"
      v-model="account"
      style="width: 340px; margin: 70px 0 0 50px"
    >
      <svg-icon icon-class="用户名" slot="prefix"
    /></Input>
    <Input
      prefix="ios-contact"
      placeholder="请输入密码"
      type="password"
      v-model="password"
      style="width: 340px; margin: 30px 0 0 50px"
      ><svg-icon icon-class="密码" slot="prefix"
    /></Input>
    <div class="btn" style="padding: 0 100px" v-if="loginLoading && IsReadUkey">
      <span>读取ukey中</span>
    </div>
    <div
      class="btn"
      style="padding: 0 100px"
      v-if="loginLoading && !IsReadUkey"
    >
      <span>登录中</span>
    </div>
    <div class="btn cursorP" v-if="!loginLoading" @click.stop="login">
      <span>登录</span>
    </div>
  </div>
</template>
  
<script>
const CryptoJS = require("crypto-js"); // 引用AES源码js
const key = CryptoJS.enc.Utf8.parse("ADF2081720D2A3F1"); // 十六位十六进制数作为密钥
const iv = CryptoJS.enc.Utf8.parse("A2B46F813D55E34C"); //十六位十六进制数作为密钥偏移量

export default {
  name: "",
  //import 引入组件
  components: {},
  data() {
    return {
      account: "",
      password: "",
      ukeyNo: "",
      socket: null,
      snTimeoutObj: null,
      loginLoading: false,
      IsReadUkey: false,
    };
  },
  created() {
    var lett = this;
    document.onkeydown = function (e) {
      var key = window.event.keyCode;
      if (key == 13) {
        lett.login();
      }
    };
  },
  beforeRouteLeave(to, form, next) {
    if (this.socket) {
      this.socket.close();
    }
    next();
  },
  methods: {
    initSocket() {
      let self = this;
      //初始化websocket
      this.socket = new WebSocket("ws://127.0.0.1:30833");
      this.socket.onclose = function (event) {
        console.log("Client notified socket has closed");
        self.logc(event);
      };
      this.socket.onerror = function (event) {
        console.log("Client notified socket has error", event);
        self.logc(event);
      };
      this.socket.onmessage = function (event) {
        try {
          // console.log('Client received a message', event);
          self.logc(event);
        } catch (e) {
          console.log("message: " + e);
        }
      };
      this.socket.onopen = function (event) {
        console.log("Connected");
        self.logc(event);
      };
    },
    logc(ev) {
      if (ev.type == "message") {
        try {
          var myarray = JSON.parse(ev.data);
          if (myarray.FunName == "GetKeySerialNumber") {
            if (myarray.SerialNum) {
              console.log("获取到ukey了，" + myarray.SerialNum);
              if (myarray.SerialNum == "no insert key") {
                this.ukeyNo = "no insert key";
              } else {
                this.ukeyNo = this.Encrypt(myarray.SerialNum);
                console.log("获取key成功，" + new Date());
              }
            } else {
              this.ukeyNo = "no insert key";
              console.log("获取到ukey了，" + myarray.SerialNum);
            }
          }
        } catch (error) {
          console.log("logc: " + error);
        }
      }
    },
    login() {
      if (!this.account) {
        this.$Message.error({
          content: "请输入登录用户名",
          duration: 3,
          closable: true,
        });
        return;
      }
      if (!this.password) {
        this.$Message.error({
          content: "请输入登录密码",
          duration: 3,
          closable: true,
        });
        return;
      }
      if (this.loginLoading) {
        return;
      }
      this.loginLoading = true;
      let self = this;
      setTimeout(function () {
        var msg = {
          FunName: "GetKeySerialNumber",
        };
        self.socket.send(JSON.stringify(msg)); //获取ukey序列码
      }, 2);
      setTimeout(function () {
        let param = {
          userAccount: self.account,
          password: self.Encrypt(self.password),
        };
        //第一次登录
        self.LoginRequest(param);
      }, 5);
    },
    /**
     * 1、不知道该用户是否插入ukey
     * 2、不知道什么时候能获取到ukey
     * 故使用如下方式获取ukey序列号，最多等待2s。一旦超时，则直接提交登录请求
     */
    awaitUkeyNo() {
      let self = this;
      var asyncFunc = function (i) {
        return new Promise(function (resolve, reject) {
          setTimeout(function () {
            // console.log("index is : ", i);
            resolve(); //异步操作完成后执行resolve方法，配合.then使用，本例子中未使用到。
          }, 50);
        });
      };
      var awaitSubmit = async function () {
        let param = {
          userAccount: self.account,
          password: self.Encrypt(self.password),
        };
        for (var i = 0; i < 200; i++) {
          console.log("awaitSubmit" + i + ":" + self.ukeyNo);
          if (self.ukeyNo && self.ukeyNo.length > 0) {
            console.log("监听获取到ukey序列号了，" + new Date());
            break;
          }
          await asyncFunc(i); //async函数执行时，如果遇到await就会先暂停执行，等待Promise对象异步操作完成后，恢复async函数的执行并返回解析值。
        }
        if (self.ukeyNo && self.ukeyNo != "no insert key") {
          param.serialNo = self.ukeyNo;
          self.LoginRequest(param);
        } else {
          self.$Message.error("未获取到ukey，请检查ukey状态后重试！");
          self.loginLoading = false;
          self.IsReadUkey = false
          return false;
        }
      };
      awaitSubmit();
    },
    LoginRequest(params) {
      let url = gl.serverURL + "/login";
      this.$http
        .post(url, params, { emulateJSON: true, withCredentials: true })
        .then((response) => {
          let data = response.body;
          console.log(data);
          if (data.status == 0) {
            this.loginLoading = false;
            this.ukeyNo = null;
            this.$parent.loginFrame();
          } else if (
            data.status == 111 &&
            data.message.indexOf("未插入") != -1
          ) {
            //当用户登录需要ukey
            this.awaitUkeyNo();
            this.IsReadUkey = true
          } else {
            this.loginLoading = false;
            this.IsReadUkey = false
            this.ukeyNo = null;
            this.$Message.error({
              content: data.message,
              duration: 3,
              closable: true,
            });
          }
        })
        .catch((err) => {
          // console.log(err);
          this.$Message.error("服务器出错！！");
          this.loginLoading = false;
          this.IsReadUkey = false
          this.ukeyNo = null;
        });
    },
    // 加密方法
    Encrypt(word) {
      let encrypted = CryptoJS.AES.encrypt(word, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });
      return encrypted.toString();
    },
  },
  mounted() {
    gl.login = this;
    this.initSocket();
  },
};
</script>
  
<style lang='less' scoped>
.inputFrame {
  width: 440px;
  height: 520px;
  background-color: #fff;
  box-shadow: 0 2px 10px 0 rgba(22, 23, 37, 0.3);
  border-radius: 5px;
  border-radius: 5px;
  float: left;
  /deep/.ivu-input-wrapper {
    width: 700px;
    margin: 10px auto;
    height: 40px;
    border-radius: 4px !important;
    overflow: hidden;
    .ivu-input {
      height: 40px;
    }
    .ivu-icon {
      height: 40px;
    }
    .ivu-icon-ios-search {
      line-height: 40px;
      font-size: 20px;
      background-color: #fff;
      color: #2b8cf0;
    }
    .ivu-select-arrow {
      line-height: 3;
    }
  }
  .title {
    width: 100%;
    font-family: STSongti-SC-Black;
    font-size: 24px;
    color: #383f4f;
    letter-spacing: 0;
    text-align: center;
    line-height: 32px;
    margin-top: 60px;
  }
  .wjma {
    font-size: 14px;
    color: #1c7ff1;
    text-align: justify;
    line-height: 20px;
    margin: 20px 0 0 334px;
    font-weight: 600;
  }
  .cursorP {
    cursor: pointer;
  }
  .btn {
    width: 340px;
    height: 50px;
    margin: 50px 0 0 50px;
    background: #1c7ff1;
    border-radius: 4px;
    line-height: 50px;
    display: inline-block;
    font-size: 16px;
    color: #ffffff;
    padding: 0 146px;
    text-align: justify;
    text-align-last: justify;
  }
  /deep/.ivu-input {
    height: 50px;
  }
  /deep/.ivu-input-prefix {
    line-height: 40px;
    width: 54px;
  }
  /deep/.ivu-input-with-prefix {
    padding-left: 54px;
    background: #f3f6fb;
    border-radius: 4px;
    border: none;
  }
}
.ukeySelect {
  margin: 20px 0 0;
  padding: 0 0 0 50px;
  /deep/ .ivu-checkbox-wrapper {
    font-size: 14px !important;
  }
}
</style>