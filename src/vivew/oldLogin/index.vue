<template>
  <div
    class="homeFrame"
    :style="{
      backgroundImage:
        'url(' + (sysNo ? '' : '../../../static/img/homeBacground.jpg') + ')',
    }"
  >
    <div v-show="!sysNo">
      <div class="loginFrame" v-show="toLog">
        <div class="mask" @click.stop="loginFrame"></div>
        <login class="login" />
      </div>

      <div class="header">
        <div class="left">
          <div class="link">
            <a
              href="https://10.83.13.228/trs/download/95.0.4638.69_chrome_installer.exe"
              target="_blank"
              ><Icon type="md-globe" /> 浏览器下载</a
            >
            <a href="#gateway" target="_blank" ><Icon type="ios-cash-outline" /> 门户首页</a>
            <a href="#dutyRoster" target="_blank" ><Icon type="ios-cash-outline" /> 值班竖屏</a>
            <a href="#advertisement" target="_blank" ><Icon type="ios-cash-outline" /> 舆情竖屏</a>
            
          </div>
        </div>
        <div class="right">
          <div class="iconFrame" v-show="false">
            <svg-icon
              v-for="(item, index) in iconList"
              :key="index"
              :icon-class="item.name"
            />
          </div>
          <div>
            <template v-if="logged">
              <Dropdown>
                <a href="javascript:void(0)" style="color: #fff">
                  <Icon type="md-contact" size="27" />
                  {{ userName }}
                  <Icon type="ios-arrow-down"></Icon>
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem @click.native="updatePasswordPage">
                    <Icon type="md-key" />&nbsp;修改密码
                  </DropdownItem>
                  <DropdownItem
                    v-if="hasPermission('/sys')"
                    @click.native="toInstitutionalDepartment"
                  >
                    <Icon type="md-settings" />&nbsp;门户配置
                  </DropdownItem>
                  <DropdownItem @click.native="out">
                    <Icon type="md-exit" />&nbsp;退出
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </template>
            <template v-else>
              <div class="loginBtn bold" @click="loginModal">请登录</div>
            </template>
          </div>
        </div>
      </div>
      <div class="logo">
        <svg-icon
          icon-class="门户系统logo"
          style="height: 67px; width: 816px"
        />
      </div>
      <div>
        <form
          id="ticketmessage"
          action=""
          method="post"
          :target="sysNo ? '_self' : '_blank'"
          class="btn"
        >
          <div
            class="btnItem"
            :class="{ active: logged && hasSystemPermission(item.sysNo) }"
            v-for="(item, index) in btnJSONData"
            :key="index"
            @click="systemClick(item.sysURL, item.sysNo)"
          >
            <span>
              <span class="name"> {{ item.sysName }} </span>
            </span>
          </div>
          <input type="hidden" value="" name="token" id="fortheticketvalue" />
          <input type="hidden" value="" name="sessionId" id="sessionId" />
          <input type="hidden" value="" name="cityFlag" id="cityFlag" />
          <input type="hidden" value="" name="to" id="to" />
          
        </form>
        
      </div>

      <!-- 修改密码模态框 -->
      <Modal
        v-model="editPswModal"
        title="修改个人密码"
        :loading="updatePswLoading"
        :footer-hide="true"
      >
        <div class="pswDiv">
          <span class="pswSpan">请输入原密码：</span>

          <Input
            prefix="ios-contact"
            placeholder="输入当前密码"
            type="password"
            v-model="oldPassword"
            class="pswInput"
            ><svg-icon icon-class="密码" slot="prefix" />
          </Input>
        </div>
        <div class="pswDiv">
          <span class="pswSpan">请输入新密码：</span>
          <Input
            prefix="ios-contact"
            placeholder="输入新密码"
            type="password"
            v-model="newPsw"
            class="pswInput"
            ><svg-icon icon-class="密码" slot="prefix" />
          </Input>
        </div>
        <div class="pswDiv">
          <span class="pswSpan">请再次输入新密码：</span>
          <Input
            prefix="ios-contact"
            placeholder="输入新密码"
            type="password"
            v-model="confirmPsw"
            class="pswInput"
            ><svg-icon icon-class="密码" slot="prefix" />
          </Input>
        </div>
        <div class="pswDiv" style="display: inline-block; text-align: center">
          <Button size="large" @click="updatePassword(true)">取消</Button>
          <Button
            size="large"
            style="margin-left: 20px"
            type="primary"
            @click="updatePassword(false)"
            >确认</Button
          >
        </div>
      </Modal>
    </div>
    <div v-if="sysNo">
      {{ jumpBySysNo() }}
    </div>
  </div>
</template>

<script>
const CryptoJS = require("crypto-js"); // 引用AES源码js
const key = CryptoJS.enc.Utf8.parse("ADF2081720D2A3F1"); // 十六位十六进制数作为密钥
const iv = CryptoJS.enc.Utf8.parse("A2B46F813D55E34C"); //十六位十六进制数作为密钥偏移量

import $ from "jquery";
import login from "../login";

export default {
  components: { login },
  data() {
    return {
      promptCount:0,//提示单单数量
      promptDataList: [], //提示单数据列表
      dataList: [], //涉济报送数据列表
      citysSysNo: "trs_citys",
      provincesSysNo: "trs_provinces",
      yunWeiURL: "https://************:3000",
      yunWeiSysNo: "yunWei",
      updatePswLoading: false,
      oldPassword: null,
      newPsw: null,
      confirmPsw: null,
      editPswModal: false,
      sysNos: [],
      btnJSONData: [],
      logged: false,
      toLog: false,
      userName: "",
      loginFrames: "loginFrames",
      iconList: [
        { name: "头部-境外网站访问" },
        { name: "头部-收藏列表" },
        { name: "头部-翻译工具" },
        { name: "头部-通知消息" },
        { name: "头部-通知消息备份" },
      ],
      cityList: [
        {
          value: "信息",
          label: "1",
        },
        {
          value: "账号",
          label: "2",
        },
        {
          value: "图片",
          label: "3",
        },
        {
          value: "音频",
          label: "4",
        },
        {
          value: "视频",
          label: "5",
        },
      ],
      value13: "",
      select3: "1",
      sysNo: this.$route.query.sysNo,
      to: this.$route.query.to,
      cityFlag: this.$route.query.cityFlag,
      background: this.$route.query.sysNo
        ? ""
        : "../../../static/img/homeBacground.jpg",
    };
  },
  methods: {
    // 加密方法
    Encrypt(word) {
      let encrypted = CryptoJS.AES.encrypt(word, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });
      return encrypted.toString();
    },
    updatePasswordPage() {
      this.editPswModal = true;
    },
    updatePassword(isCancel) {
      if (isCancel) {
        this.editPswModal = false;
        return;
      }
      this.editPswModal = true;
      if (!this.oldPassword) {
        this.$Message.error("请输入旧密码");
        return;
      }
      if (!this.newPsw || !this.confirmPsw || this.newPsw != this.confirmPsw) {
        this.$Message.error("新密码两次输入结果不一致");
        return;
      }
      if (this.oldPassword == this.newPsw) {
        this.$Message.error("原密码和新密码不能一样");
        return;
      }
      this.updatePswLoading = true;
      let url = gl.serverURL + "/updatePsw";
      this.$http
        .post(
          url,
          {
            newPassword: this.Encrypt(this.newPsw),
            oldPassword: this.Encrypt(this.oldPassword),
          },
          { emulateJSON: true, withCredentials: true }
        )
        .then((response) => {
          let data = response.body;
          if (data.status == 0) {
            this.$Message.success("修改成功！");
            this.editPswModal = false;
            this.oldPassword = "";
            this.newPsw = "";
            this.confirmPsw = "";
          } else {
            this.$Message.error({
              content: data.message,
              duration: 3,
              closable: true,
            });
          }
        });
    },
    hasSystemPermission(thisSysNo) {
      console.log(this.sysNos);
      return (
        this.sysNos &&
        this.sysNos.length > 0 &&
        this.sysNos.indexOf(thisSysNo) >= 0
      );
    },
    toInstitutionalDepartment(data) {
      // this.$router.push(data);
      const { href } = this.$router.resolve({
        path: "/SystemConfiguration",
        // query: {
        //     id: 1
        // }
      });
      window.open(href, "_blank");
    },
    out() {
      this.$http.get(gl.serverURL + "/logout").then((response) => {
        let data = response.body;
        if (data.status == 0) {
          sessionStorage.removeItem("userName");
          sessionStorage.removeItem("sysNos");
          localStorage.removeItem("userName");
          localStorage.removeItem("sysNos");
          this.userName = "";
          this.logged = false;
        }
      });
    },
    loginFrame() {
      this.getUserInfo();
      this.getUserMsg();
      this.getUserToken();
      this.toLog = false;
      this.logged = true;
      //this.$router.push('/gateWay')
    },
    loginModal() {
      this.toLog = true;
    },

    systemClick(sysUrl, sysNo) {
        console.log('444444');
      if (!this.sysNo && (!this.logged || !this.hasSystemPermission(sysNo))) {
        this.sysNo = "";
        return;
      }
      //临时加上舆情态势的跳转
      if (sysNo == "yqts") {
        console.log(this.sysNo,'this.sysNo');
        window.open(sysUrl, this.sysNo ? "_self" : "_blank");
        return;
      }
      if (sysNo == this.yunWeiSysNo) {
        console.log('22222');
        window.open(this.yunWeiURL, this.sysNo ? "_self" : "_blank");
        return;
      }
      let url = gl.serverURL + "/tokenMsg";
      let params = { systemNo: sysNo, to: this.to };

      this.$http.get(url, { params: params }).then((response) => {
        let data = response.body;

        if (data.status == 0 && data.data) {
          $("#fortheticketvalue").val(data.data.token);
          $("#sessionId").val(data.data.sessionId);
          $("#cityFlag").val(this.cityFlag);

          if (this.to) $("#to").val(this.to);
          if (
            this.cityFlag &&
            this.cityFlag.length > 0 &&
            (!this.to || this.to.length == 0)
          ) {
            $("#to").val("jc");
          }
          $("#ticketmessage").attr("action", sysUrl);
          $("#ticketmessage").submit();
        } else {
          this.out();
          this.$Message.error({
            content: data.message,
            duration: 3,
            closable: true,
          });
        }
      });
    },

    getUserInfo() {
      let url = gl.serverURL + "/getUser";
      let $this = this;
      this.$http.get(url).then((response) => {
        let data = response.body;
        if (data.status == 0) {
          $this.userName = data.data.nickName;
          $this.sysNos = data.data.systemCodes;
          sessionStorage.setItem("userName", $this.userName);
          sessionStorage.setItem("sysNos", $this.sysNos);
          localStorage.setItem("region", this.$route.path);
          if (data.data.powers) {
            sessionStorage.setItem(
              "resources",
              JSON.stringify(data.data.powers)
            );
          }
          this.toLog = false;
          this.logged = true;
          this.userName = sessionStorage.getItem("userName");
          this.sysNos = sessionStorage.getItem("sysNos");
          // localStorage.setItem("userName", $this.userName);
          // localStorage.setItem("sysNos", $this.sysNos);
          // if (data.data.powers) {
          //   localStorage.setItem("resources", JSON.stringify(data.data.powers));
          // }
        } else {
          $this.out();
        }
      });
    },

    jumpBySysNo() {
      for (let item of this.btnJSONData) {
        if (item.sysNo == this.sysNo) {
          this.systemClick(item.sysURL, item.sysNo);
        }
      }
    },
    getUserToken() {
      let url = gl.serverURL + "/tokenMsg";
      let params = { systemNo: "trs_home"};

      this.$http.get(url, { params: params }).then((response) => {
        let data = response.body;
        if (data.status == 0) {
          localStorage.setItem("tokens", data.data.token);
        }else{
          localStorage.removeItem("tokens");
        }
      });
    },
    //获取登陆用户
    getUserMsg() {
      let url = gl.serverURL + "/getUserMsg";
      let params = { systemNo: "trs_home"};

      this.$http.get(url, { params: params }).then((response) => {
        let data = response.body;
        if (data.status == 0) {
          localStorage.setItem("userId", data.data.userId);
          localStorage.setItem("userName", data.data.userName);
          localStorage.setItem("organName", data.data.organName);
          localStorage.setItem("organId", data.data.organId);
          localStorage.setItem("iphone", data.data.userTelephone);
          localStorage.setItem("userAccount", data.data.userAccount);
          localStorage.setItem(
                  "resources",
                  JSON.stringify(data.data.resource)
          );
          localStorage.setItem("sysNos", data.data.sysNos?data.data.sysNos.toString():'');
          localStorage.setItem("roleName",data.data.roleName?data.data.roleName:'');
          localStorage.setItem("departmentName", data.data.departmentName);
          localStorage.setItem("departmentId", data.data.departmentId);
          localStorage.setItem("browser", data.data.browser);
          localStorage.setItem("ip", data.data.ip);
          localStorage.setItem("organType",data.data.organType);

          gl.loginU = {
            userId: data.data.userId,
            userName: data.data.userName,
            organId: data.data.organId,
            organName: data.data.organName,
            userAccount: data.data.userAccount,
            resources: data.data.resource,
            sysNos: data.data.sysNos,
          };
        }else{
          localStorage.removeItem('organType')
          localStorage.removeItem("userId");
          localStorage.removeItem("userName");
          localStorage.removeItem("organName");
          localStorage.removeItem("organId");
          localStorage.removeItem("organName");
          localStorage.removeItem("userAccount");
          localStorage.removeItem("resources");
          localStorage.removeItem("sysNos");
          localStorage.removeItem("iphone");
          localStorage.removeItem("roleName");
          gl.loginU = {};
        }
      });
    },
  },
  mounted() {
    gl.home = this;
    this.getUserInfo();
    this.getUserMsg();
  },
  created() {
    this.btnJSONData = SYSTEM_CONFIG_XZ;
    if (this.$route.path !== localStorage.getItem("region")) {
      return;
    }

    // this.getUserInfo();
    // let sessionUserName = sessionStorage.getItem("userName");
    // if (sessionUserName && sessionUserName.length > 0) {
    //   this.toLog = false;
    //   this.logged = true;
    //   this.userName = sessionStorage.getItem("userName");
    //   this.sysNos = sessionStorage.getItem("sysNos");
    // } else {
    // }
    // sessionUserName = localStorage.getItem("userName");
    // if (sessionUserName && localStorage.length > 0) {
    //   this.toLog = false;
    //   this.logged = true;
    //   this.userName = localStorage.getItem("userName");
    //   this.sysNos = localStorage.getItem("sysNos");
    // }
    // console.log(this.sessionUserName, this.logged);
  },
};
</script>

<style lang="less" scoped>
.loginFrame {
  z-index: 9;
  position: absolute;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  .mask {
    position: absolute;
    width: 100%;
    height: 100%;
  }
  .login {
    margin-top: 240px;
    z-index: 99;
  }
}
.homeFrame {
  width: 100%;
  height: 1000px;
  background-size: 100% 100%;
  .header {
    height: 62px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 200px;
    padding-top: 30px;
    .link {
      a {
        margin-right: 30px;
        color: #fff;
        &:hover {
          color: #90cbf5;
        }
      }
    }
    .left {
      font-family: PingFangSC-Semibold;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
    }
    .right {
      .svg-icon {
        width: 32px;
        height: 32px;
        margin-right: 20px;
        line-height: 32px;
      }
      & > div,
      span {
        float: left;
      }
    }
  }
  .logo {
    text-align: center;
    height: 67px;
    margin: 40px 0;
    padding-left: 280px;
    .svg-icon {
      margin: 4px auto;
    }
  }
  .loginBtn {
    display: inline-block;
    transition: all 0.2s;
    background: #ffffff;
    border-radius: 16px;
    height: 32px;
    width: 84px;
    text-align: center;
    line-height: 32px;
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    color: #2354a0;
    cursor: pointer;
    &:hover {
      background: #90cbf5;
      color: #fff;
    }
  }
  /deep/.ivu-select,
  /deep/.ivu-select-selection,
  /deep/.ivu-select-placeholder,
  /deep/.ivu-select-selected-value {
    height: 40px;
    line-height: 40px;
  }
  /deep/.ivu-input-group-prepend {
    border-radius: 20px 0 0 20px;
  }
  /deep/.ivu-select-selection {
    border: none;
  }
  /deep/.ivu-input-inner-container {
    border-radius: 0 20px 20px 0;
    overflow: hidden;
  }
  /deep/.ivu-input-wrapper {
    width: 700px;
    margin: 10px auto;
    height: 40px;
    border-radius: 20px;
    // overflow: hidden;
    .ivu-input {
      height: 40px;
      border: none;
      padding-right: 45px;
    }
    .ivu-icon {
      height: 40px;
    }
    .ivu-icon-ios-search {
      line-height: 40px;
      font-size: 20px;
      background-color: #fff;
      color: #2b8cf0;
      width: 45px;
      text-align: left;
      padding-left: 5px;
    }
    .ivu-select-arrow {
      line-height: 3;
    }
  }
  .btn {
    width: 1540px;
    height: 430px;
    margin: 80px auto;
    display: flex;
    flex-wrap: wrap;
    .active {
      cursor: pointer;
    }
    .btnItem {
      transition: all 0.3s;
      width: 270px;
      height: 190px;
      border-radius: 4px;
      background: rgba(197, 193, 191, 0.2);
      display: flex;
    flex-direction: row;
    justify-content: center;
    flex-wrap: nowrap;
    align-items: center;
      margin: 25px;
      .svg-icon {
        height: 70px;
        width: 70px;
      }
      .name {
        font-family: PingFangSC-Semibold;
        font-size: 20px;
        color: #383f4f;
        text-align: center;
        line-height: 20px;
      }
      &.active:hover {
        background: #57a3f340;
      }
    }
  }
}
.pswDiv {
  display: inline-flex;
  text-align: right;
  padding-top: 20px;
  display: flex;
  justify-content: center;
  width: 100%;
  height: 30%;
  .pswSpan {
    display: flex;
    width: 25%;
    text-align: right;
    align-items: center;
  }
}
.pswInput {
  width: 300px;
  margin: 0px 0 0 0px;
}
.ivu-dropdown-item {
  font-size: 14px !important;
}
/deep/.ivu-input-prefix {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
