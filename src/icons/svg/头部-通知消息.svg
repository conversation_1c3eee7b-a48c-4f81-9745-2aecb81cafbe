<?xml version="1.0" encoding="UTF-8"?>
<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>头部-通知消息</title>
    <g id="登录+门户" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="门户" transform="translate(-1518.000000, -30.000000)">
            <g id="编组" transform="translate(1518.000000, 30.000000)">
                <rect id="矩形备份-9" fill="#000000" opacity="0.200000003" x="0" y="0" width="32" height="32" rx="4"></rect>
                <path d="M6,12.9367791 L14.3771765,18.1026477 C15.4549412,18.7670302 17.092,18.7572226 18.1597647,18.0758785 L18.1597647,18.0758785 L25.9991765,13.0710864 L25.9991765,21.0733308 C25.9933401,22.4180952 24.8779662,23.5040641 23.5068235,23.4999886 L23.5068235,23.4999886 L8.49247059,23.4999886 C7.12162807,23.5033011 6.00667686,22.4177949 6,21.0733308 L6,21.0733308 L6,12.9367791 Z M23.5068235,8.50001179 C24.8775692,8.497206 25.9924194,9.58230655 26,10.92667 L26,10.92667 L26,11.0564773 L17.0949412,16.741575 C16.6562353,17.0210357 15.8607059,17.0263433 15.418,16.7531134 L15.418,16.7531134 L6,10.9453622 L6,10.92667 C6.00583635,9.58186037 7.12128204,8.49587277 8.49247059,8.50001179 L8.49247059,8.50001179 Z" id="形状结合" fill="#FFFFFF" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>