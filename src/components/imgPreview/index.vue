<template>
  <div class="imgPreview" @click.stop="close">
    <!--    <Icon class="close cp" type="md-close-circle" @click.native="close" />-->
    <Carousel
      v-model="CarouselId"
      :style="{ opacity: transparency }"
      :arrow="imgList.length > 1 ? 'always' : 'never'"
      dots="outside"
      @click.native.stop="() => {}"
      v-if="type === 'img'"
    >
      <CarouselItem v-for="(item, index) in imgList" :key="index">
        <div class="back">
          <div class="demo-carousel">
            <img :src="item" alt="" />
          </div>
        </div>
      </CarouselItem>
    </Carousel>
    <video
      controls
      controlsList="nodownload" 
      style="width: 1000px; height: 800px;"
      :src="imgList[0]"
      v-else
    />
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

export default {
  name: "index",
  data() {
    // 这里存放数据
    return {
      CarouselId: 0,
      transparency: 0,
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    imgList: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: "img",
    },
    defaultId: {
      default: 0,
    },
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.CarouselId = this.defaultId;
    setTimeout(() => {
      this.transparency = 1;
    }, 500);
  },
  // 方法集合
  methods: {
    close() {
      this.$emit("close");
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {
    CarouselId(val, oldVal) {
      console.log(val, oldVal);
    },
  },
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    console.log(this.defaultId);
  },
};
</script>

<style lang="less" scoped>
.imgPreview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999999;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;

  /deep/.ivu-carousel-arrow {
    z-index: 99999;
  }
  /deep/ .left {
    left: -50px;
  }
  /deep/ .right {
    right: -50px;
  }
  .ivu-carousel {
    width: 80%;
    height: 90%;
  }

  .demo-carousel {
    height: calc(~"100vh - 100px");
    // width: 90%;
    overflow-y: auto;
    margin: 0 auto;

    img {
      width: 100%;
      object-fit: contain;
    }
  }

  .close {
    position: fixed;
    right: 20px;
    top: 20px;
    //color: #fff;
    font-size: 60px;
    z-index: 999999999;
  }
}
</style>
