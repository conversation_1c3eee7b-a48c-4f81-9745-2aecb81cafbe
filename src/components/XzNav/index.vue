<template>
  <div class="navFrame" :style="{ height: navHeight + 'px' }">
    <div class="navContent">
      <template v-for="item in list">
        <div
          class="navItem"
          :key="item.path"
          @click="toPath(item.path)"
          v-if="!item.permission || hasPermission(item.permission)"
        >
          <svg-icon
            :icon-class="
              $route.path.indexOf(item.path) != -1
                ? item.icon + '-选中'
                : item.icon
            "
            slot="prefix"
          />
          <span
            class="itemTitle"
            :style="{
              color:
                $route.path.indexOf(item.path) != -1 ? '#1C7FF1' : '#383F4F',
            }"
          >
            {{ item.title }}
          </span>
          <div
            class="selected"
            v-show="$route.path.indexOf(item.path) != -1"
          ></div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  props: {
    navHeight: {
      type: Number,
      default: 50,
    },
    list: Array,
  },
  //import 引入组件
  components: {},
  data() {
    return {};
  },
  methods: {
    toPath(data) {
      if (this.$route.path.indexOf(data) == -1){
        this.$router.push(data);
      }
    },
  },
  mounted() {
    if(gl.develop){
        return;
    }
    if(this.list && this.list.length > 0){
            for(let item of this.list){
                if(this.hasPermission(item.permission)){
                    this.$router.push(item.path);
                    return;
                }
            }

    }


  },
};
</script>

<style lang='less' scoped>
.navFrame {
  width: 100%;
  background-color: #fff;
  .navContent {
    width: 1720px;
    margin: 0 auto;
    display: flex;
    height: 100%;
    .navItem {
      min-width: 128px;
      padding: 18px;
      position: relative;
      height: 100%;
      margin-right: 80px;
      cursor: pointer;
      .itemTitle {
        margin-left: 10px;
        font-size: 16px;
        font-weight: 600;
      }
      .selected {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: 3px;
        background: #1c7ff1;
      }
    }
  }
}
</style>