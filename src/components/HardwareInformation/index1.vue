<template>
  <div class="tax-revenue">
    <div class="tax-container">
      <div class="board">
        <div class="title">{{ data.title }}</div>
      </div>
      <div class="carousel-wrap">
        <div
          v-for="(item, index) in data.dataList"
          :key="index"
          :data-id="item.name"
          class="carousel-list"
        >
          <div class="carousel-item">
            <div class="info" @mouseenter="pause" @mouseleave="resume">
              <div class="name">{{ item.name }}</div>
              <div class="value">{{ item.value }}</div>
              <div class="status">{{ item.status }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { API } from "@/http/Api";
// import TWEEN from "@tweenjs/tween.js";

export default {
  data() {
    return {
      data: {
        currentIndex: 0,
        dataList: [],
        tween: null,
        title: "",
      },
      timer2: null,
    };
  },
  methods: {
    animate() {
      const $list = document.querySelectorAll(".carousel-list");
      const $item = document.querySelectorAll(".carousel-item");

      const count = $list.length;
      const step = 360 / count;
      let opacity = 1;
      const maxIndex = *********;
      let iteratorIndex = maxIndex;
      let zIndex = 1;

      this.data.tween = new TWEEN.Tween({ deg: 0 })
        .to({ deg: 360 }, window.uino.taxCarouselTime)
        .start()
        .repeat(Infinity)
        .onUpdate(({ deg }) => {
          for (let i = 0; i < count; i += 1) {
            const list = $list[i];
            const item = $item[i];
            const nextDeg = deg + step * i;

            if (this.isRange(nextDeg, 179.8, 180.2)) {
              this.data.currentIndex = i;
            }

            if (this.isRange(nextDeg, 120, 240)) {
              zIndex = maxIndex + 10;
              iteratorIndex = maxIndex;
              opacity = 1;
            } else if (this.isRange(nextDeg, 240, 330)) {
              iteratorIndex -= 1;
              zIndex = iteratorIndex;
              opacity = 0.6;
            } else if (this.isRange(nextDeg, 330, 360)) {
              iteratorIndex -= 1;
              zIndex = iteratorIndex;
              opacity = 0;
            } else if (this.isRange(nextDeg, 360, 380)) {
              iteratorIndex += 1;
              zIndex = iteratorIndex;
              opacity = 0;
            } else {
              iteratorIndex += 1;
              zIndex = iteratorIndex;
              opacity = 0.6;
            }

            Object.assign(list.style, {
              transform: `translateY(-40px) scaleY(0.2) rotateZ(${nextDeg}deg)`,
              transition: `opacity 0.5s ease-in`,
              zIndex,
              opacity,
            });

            Object.assign(item.style, {
              transform: `rotateZ(${-nextDeg}deg) scaleY(5) scale(${1})`,
            });
          }
        });

      const boardElement = document.querySelector(".board");
      if (boardElement) {
        boardElement.style.zIndex = maxIndex + 5;
      }
    },

    pause() {
      if (!this.data.tween._isPaused) {
        this.data.tween.pause();
      }
    },

    resume() {
      if (this.data.tween._isPaused) {
        this.data.tween.resume();
      }
    },

    isRange(value, min, max) {
      return (
        (value >= min && value <= max) ||
        (value >= min + 360 && value <= max + 360)
      );
    },

    raf() {
      requestAnimationFrame(this.raf);
      TWEEN.update();
    },

    async getData() {
      try {
        if (this.data.tween) this.data.tween.stop();
        const res = {
          title: "总体服务器",
          data: [
            { name: "CPU", status: "使用率", value: "3.16" },
            { name: "存储", status: "使用率", value: "5.84" },
            { name: "内存", status: "使用率", value: "29.78" },
            { name: "通用服务器", status: "未使用", value: "0" },
            { name: "通用服务器", status: "已使用", value: "100" },
            { name: "虚拟机", status: "未使用", value: "3.03" },
            { name: "虚拟机", status: "已使用", value: "96.97" },
            { name: "算力服务器", status: "未使用", value: "100" },
            { name: "算力服务器", status: "已使用", value: "0" },
          ],
        };
        if (res) {
          const result = res.data;
          this.data.title = res.title;
          this.data.dataList = result;
          this.$nextTick(() => {
            this.animate();
            requestAnimationFrame(this.raf);
          });
        }
      } catch (error) {
        console.error("请求数据时发生错误", error);
      }
    },
  },

  mounted() {
    this.getData();
    this.timer2 = setInterval(async () => {
      this.getData();
    }, window.uino.refreshTime);
  },

  beforeDestroy() {
    clearInterval(this.timer2);
  },
};
</script>

<style lang="less" scoped>
.tax-revenue {
  width: 100%;
  position: relative;
  background-size: cover;
  pointer-events: all;

  .tax-container {
    width: 100%;

    .board {
      position: absolute;
      left: 40px;
      top: 0;
      width: 955px;
      height: 587px;
      background: url("../../assets/img/centerImage.png") no-repeat center;
      background-size: 100% 100%;

      .title {
        font-family: DINPro-Regular, SourceHanSansCN-Regular, sans-serif;
        font-weight: 400;
        font-size: 19px;
        color: #e2f2ff;
        background: linear-gradient(
          0deg,
          #55deff 0%,
          #28a7e9 18.2373046875%,
          #c5e5fa 34.3505859375%,
          #ffffff 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        top: 169px;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        margin-left: -10px;
      }
    }

    .carousel-wrap {
      position: absolute;
      left: -118px;
      top: 179px;
      display: flex;
      width: 1270px;
      height: 680px;
      align-items: center;
      justify-content: center;
      background: url("../../assets/img/roll-bg.png") no-repeat center;
      background-size: 100% 100%;
      transform-style: preserve-3d;

      .carousel-list {
        position: absolute;
        width: 0;
        height: 0;
        border-radius: 50%;
        transform: translateY(-20px) scaleY(0.2) rotateZ(0deg);
        will-change: transform;
        transform-style: preserve-3d;
        pointer-events: none;

        .carousel-item {
          width: 232px;
          height: 272px;
          background: url("../../assets/img/ball-bg.png") no-repeat center;
          background-size: cover;
          position: absolute;
          transform: rotateZ(0deg) scaleY(5);
          will-change: transform;
          left: calc(50% - 120px);
          top: -550px;

          .info {
            width: 160px;
            margin-top: 69px;
            margin-left: 45px;
            pointer-events: all;
            text-align: center;
            line-height: 1;

            .status {
              font-family: DINPro-Regular, SourceHanSansCN-Regular, sans-serif;
              font-weight: 400;
              font-size: 13px;
              color: #ffffff;
            }

            .value {
              font-size: 20px;
              font-family: DINPro-Bold, SourceHanSansCN-Bold, sans-serif;
              font-weight: bold;
              color: #ffffff;
              background: linear-gradient(
                0deg,
                rgb(55, 240, 108),
                rgb(255, 255, 255)
              );
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              padding-top: 9px;
              padding-bottom: 9px;
            }

            .name {
              font-family: DINPro-Regular, SourceHanSansCN-Regular, sans-serif;
              font-weight: 400;
              font-size: 12px;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
</style>
