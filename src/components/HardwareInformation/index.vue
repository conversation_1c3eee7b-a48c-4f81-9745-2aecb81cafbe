<template>
  <div class="frames">
    <img
      alt=""
      class="centerImage cp"
      @click="circleClick"
      src="@/assets/img/centerImage.png"
    />
    <div class="round">
      <div class="box">
        <div
          v-for="(item, index) in datalist"
          :key="index"
          :class="['circle', 'circle' + (index + 1)]"
          @click="circleClick(item)"
        >
          <div class="content">
            <img alt="" src="@/assets/img/ball-bg.png" />
            <div class="text">
              <div class="value">{{ item.value ? item.value : '--' }}</div>
              <div class="name">{{ item.name }}</div>
              <!-- <div class="status">{{ item.status }}</div> -->
            </div>
          </div>
          <div class="indicate">
            <img alt="" src="@/assets/img/箭头.png" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具 js，第三方插件 js，json 文件，图片文件等等）
// 例如：import  《组件名称》  from '《组件路径》 ';

export default {
  name: "index",
  data() {
    // 这里存放数据
    return {
      datalist: [
        { name: "CPU", status: "", value: "0" },
        { name: "内存", status: "", value: "0" },
        { name: "设备数量", status: "", value: "0" },
        { name: "磁盘分配率", status: "", value: "0" },
        { name: "接入终端", status: "", value: "0" },
        { name: "网络流量", status: "", value: "0" },
        { name: "数据库存储", status: "", value: "0" },
        { name: "ES集群吞吐量", status: "", value: "0" },
      ],
    };
  },
  // import 引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {},
  // 方法集合
  methods: {
    circleClick() {
      this.getLog('门户屏/大屏跳转', '数据中心大屏')
      window.open(gl.isPad ? "http://192.168.20.6:10012/" : "http://10.61.23.246:10012/", "_blank");
    },
    getData() {
      this.$http.get(gl.wxPingApi + "/dix/global_server").then((res) => {
        this.datalist = res.body.data;
      }).finally(() => {
    	   setTimeout(() => {
    	      this.getData();
    	    }, 5000);
    	});
    },
  },
  // 计算属性 类似于 data 概念
  computed: {},
  // 监控 data 中的数据变化
  watch: {},
  //过滤器
  filters: {},

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getData();
  },

  beforeDestroy() {
  },
};
</script>

<style lang="less" scoped>
.frames {
  .centerImage {
    position: relative;
    bottom: -300px;
    left: 20px;
  }

  .round {
    position: relative;
    width: 700px; /* 椭圆的宽度 */
    height: 450px; /* 椭圆的高度 */
    //border-radius: 50%; /* 使容器成为椭圆 */
    //border: 2px solid #000;
    background: url("../../assets/img/roll-bg.png") no-repeat;
    background-size: 110%;
    background-position: bottom;
  }

  /* 动画的容器(椭圆) */
  .box {
    width: 600px; /* 椭圆的宽度 */
    height: 150px; /* 椭圆的高度 */
    border-radius: 50%; /* 使容器成为椭圆 */
    position: relative;
    box-sizing: border-box;
    margin-top: 70px;
  }

  .circle {
    width: 140px;
    position: absolute;
    z-index: 999;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    cursor: pointer;

    img {
      width: 100%;
    }

    .text {
      position: absolute;
      top: 10px;
      display: flex;
      width: 140px;
      height: 140px;
      flex-wrap: wrap;
      justify-content: center;
      padding: 30px 0;

      div {
        width: 100%;
        text-align: center;
        font-size: 12px;
        color: #ffffff;
      }

      .name {
        width:77px;
      }

      .value {
        color: #9dfeff;
        font-size: 24px;
        font-family: Impact;
        line-height: 24px;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-end;
        justify-content: center;
        align-items: center
      }
    }
  }

  .circle1 {
    animation: animX 10s cubic-bezier(0.36, 0, 0.64, 1) -5s infinite alternate,
      animY 10s cubic-bezier(0.36, 0, 0.64, 1) 0s infinite alternate,
      scale 20s cubic-bezier(0.36, 0, 0.64, 1) 0s infinite alternate;
  }

  .circle2 {
    animation: animX 10s cubic-bezier(0.36, 0, 0.64, 1) -7.5s infinite alternate,
      animY 10s cubic-bezier(0.36, 0, 0.64, 1) -2.5s infinite alternate,
      scale 20s cubic-bezier(0.36, 0, 0.64, 1) -2.5s infinite alternate;
  }

  .circle3 {
    animation: animX 10s cubic-bezier(0.36, 0, 0.64, 1) -10s infinite alternate,
      animY 10s cubic-bezier(0.36, 0, 0.64, 1) -5s infinite alternate,
      scale 20s cubic-bezier(0.36, 0, 0.64, 1) -5s infinite alternate;
  }

  .circle4 {
    animation: animX 10s cubic-bezier(0.36, 0, 0.64, 1) -12.5s infinite alternate,
      animY 10s cubic-bezier(0.36, 0, 0.64, 1) -7.5s infinite alternate,
      scale 20s cubic-bezier(0.36, 0, 0.64, 1) -7.5s infinite alternate;
  }

  .circle5 {
    animation: animX 10s cubic-bezier(0.36, 0, 0.64, 1) -15s infinite alternate,
      animY 10s cubic-bezier(0.36, 0, 0.64, 1) -10s infinite alternate,
      scale 20s cubic-bezier(0.36, 0, 0.64, 1) -10s infinite alternate;
  }

  .circle6 {
    animation: animX 10s cubic-bezier(0.36, 0, 0.64, 1) -17.5s infinite alternate,
      animY 10s cubic-bezier(0.36, 0, 0.64, 1) -12.5s infinite alternate,
      scale 20s cubic-bezier(0.36, 0, 0.64, 1) -12.5s infinite alternate;
  }

  .circle7 {
    animation: animX 10s cubic-bezier(0.36, 0, 0.64, 1) -20s infinite alternate,
      animY 10s cubic-bezier(0.36, 0, 0.64, 1) -15s infinite alternate,
      scale 20s cubic-bezier(0.36, 0, 0.64, 1) -15s infinite alternate;
  }

  .circle8 {
    animation: animX 10s cubic-bezier(0.36, 0, 0.64, 1) -22.5s infinite alternate,
      animY 10s cubic-bezier(0.36, 0, 0.64, 1) -17.5s infinite alternate,
      scale 20s cubic-bezier(0.36, 0, 0.64, 1) -17.5s infinite alternate;
  }

  @keyframes animX {
    0% {
      left: -4%;
    }

    100% {
      left: 96%;
    }
  }

  @keyframes animY {
    0% {
      top: -4%;
    }

    100% {
      top: 96%;
    }
  }

  @keyframes scale {
    0% {
      transform: scale(0.4);
      opacity: 0;
      z-index: 0;
    }

    50% {
      transform: scale(1);
      opacity: 1;
      z-index: 100;
    }

    100% {
      transform: scale(0.4);
      opacity: 0;
      z-index: 0;
    }
  }
}
</style>
