function exportPost(url, params) {
    let temp_form = document.createElement("form");
    // 设置form属性
    temp_form.action = url;
    temp_form.target = "_self";
    temp_form.method = "post";
    temp_form.style.display = "none";

    let arr = Object.keys(params);
    let file = new FormData();
    for (let i = 0; i < arr.length; i++) {
        file.append(arr[i], params[arr[i]]);
        let optFilePaths = document.createElement("input");
        optFilePaths.name = arr[i];
        optFilePaths.value = params[arr[i]];
        temp_form.appendChild(optFilePaths);
    }
    document.body.appendChild(temp_form);
    // 提交表单
    temp_form.submit();
}

export default {
    exportPost
}