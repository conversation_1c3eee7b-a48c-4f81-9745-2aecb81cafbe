@charset "utf-8";
/** 兼容大小屏的相关变量 **/
* {
  margin: 0;
  padding: 0;
}
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
/*滚动条的轨道*/
::-webkit-scrollbar-track {
  background: #eeeeee;
}
/*滚动条的滑块按钮*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #959595;
}
/*滚动条的上下两端的按钮*/
::-webkit-scrollbar-button {
  display: none;
}
html {
  background: #FFFFFF;
}
html body,
html .main,
html .layout {
}
body {
  font-size: 16px;
}
/* 隐藏样式 */
.hide {
  display: none;
}
/* loading 样式*/
.ivu-spin-fix {
  background-color: rgba(255, 255, 255, 0.8);
}
/* 对话框样式 */
.ivu-modal-mask {
  background-color: rgba(55, 55, 55, 0.1);
}
.dis_textarea textarea.ivu-input {
  cursor: not-allowed;
}
.ivu-modal-header {
  padding-top: 0px;
  padding-bottom: 0px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.ivu-modal-header .ivu-modal-header p,
.ivu-modal-header .ivu-modal-header-inner {
  text-align: center;
  font-weight: normal;
  height: 45px;
  line-height: 45px;
  font-size: 16px;
  font-weight: 500;
}
.ivu-modal-close {
  top: 0px;
}
.ivu-modal-close .ivu-icon-ios-close {
  top: 9px;
}
.ivu-modal-close .ivu-icon-ios-close-empty {
  color: white;
}
.ivu-modal-footer {
  border-top: 0px;
  text-align: center;
  outline: none;
}
.ivu-modal-footer button.ivu-btn {
  width: 75px;
  height: 30px;
  padding-top: 3px;
}
.ivu-modal-footer .ivu-btn-text,
.ivu-modal-footer .ivu-btn-text:hover {
  border: 1px #DFE4E9 solid;
  color: #333;
}
.ivu-form-item {
  margin-bottom: 4px;
  vertical-align: top;
  zoom: 1;
}
.ivu-input-wrapper .ivu-input-icon-normal + .ivu-input {
  padding: 4px 30px 4px 7px;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;
}
.ivu-icon-ios-search {
  background-color: #2d8cf0;
  color: white;
  width: 33px;
  height: 32px;
  line-height: 30px;
  font-size: 14px;
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px;
  cursor: pointer;
}
.ivu-select-visible .ivu-select-selection {
  box-shadow: none;
}
/* 默认按钮样式*/
button.ivu-btn {
  height: 30px;
  line-height: 14px;
  padding: 3px 10px;
}
.shadow {
  background: white;
  border-radius: 3px;
  box-shadow: 0px 3px 15px #ccc;
}
.loading.fix {
  position: absolute;
  height: 100%;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.5);
}
.loading.fix.transparent {
  background-color: transparent;
}
.loading.fix .ivu-spin-fix {
  background-color: transparent;
}
button.nav_btn {
  min-width: 80px;
  height: auto;
  padding: 0 10px 0 10px;
  background-color: transparent;
  color: #000;
  margin-right: 16px;
  border: 0;
  border-radius: 0;
}
button.nav_btn.active,
button.nav_btn:hover {
  background-color: transparent;
  color: #2d8cf0;
}
button.nav_btn + button.nav_btn {
  border-left: 1px #cecec7 solid;
  padding-left: 30px;
}
button.nav_btn + hr {
  margin-top: 17px;
  border: 0px;
  border-top: 1px #dfe4ef solid;
}
.ivu-page-options-elevator input:focus,
.ivu-page-options-elevator input:hover {
  box-shadow: unset;
  border-color: #2d8cf0;
}
.clear {
  clear: both;
}
.ivu-btn:hover {
  color: #333;
  border-color: #DFE4E9;
}
.clearfix {
  display: block;
  zoom: 1;
}
.clearfix:after {
  content: " ";
  display: block;
  font-size: 0;
  height: 0;
  clear: both;
  visibility: hidden;
}
input.ivu-input {
  border: 1px solid #E1E1E4;
}
input.ivu-input:focus,
textarea.ivu-input:focus {
  box-shadow: none;
}
a.ivu-btn-text:focus,
.ivu-btn-text:focus,
.ivu-btn-primary:focus {
  box-shadow: none;
}
label.ivu-checkbox-wrapper.ivu-checkbox-group-item :nth-child(2) {
  display: none;
}
.ivu-tabs,
.ivu-tabs .ivu-tabs-card {
  overflow: initial;
}
.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active,
.ivu-tabs-nav .ivu-tabs-tab:hover,
.ivu-select-multiple .ivu-select-item-selected,
.ivu-select-multiple .ivu-select-item-selected:after {
  color: #2d8cf0;
}
.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-nav-container,
.ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  height: 40px;
  line-height: 30px;
}
:focus {
  outline: none;
}
.ivu-input:focus,
.ivu-input:hover,
.ivu-select-selection:hover,
.ivu-select-selection:focus,
.ivu-select-visible .ivu-select-selection {
  border-color: #2d8cf0;
}
.ivu-select-item-selected,
.ivu-select-item-selected:hover,
.ivu-btn-primary,
.ivu-btn-primary:hover,
.ivu-select-item-selected.ivu-select-item-focus {
  background-color: #2d8cf0;
  color: #FFFFFF;
  border-color: #2d8cf0;
}
li.ivu-select-item {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.ivu-tooltip-inner {
  background-color: rgba(0, 0, 0, 0.7);
}
.ivu-input-wrapper .ivu-input-icon-normal + .ivu-input,
.ivu-icon-ios-search {
  height: 30px;
}
