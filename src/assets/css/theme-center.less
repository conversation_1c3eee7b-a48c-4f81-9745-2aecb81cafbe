@charset "utf-8";
@import "./theme-variate";
*{
  margin:0;
  padding:0;
}
::-webkit-scrollbar{
  width: 6px;
  height: 6px;
}
/*滚动条的轨道*/
::-webkit-scrollbar-track{
  background:rgba(238, 238, 238,1);
}
/*滚动条的滑块按钮*/
::-webkit-scrollbar-thumb{
  border-radius: 10px;
  background:rgba(149, 149, 149,1);
}
/*滚动条的上下两端的按钮*/
::-webkit-scrollbar-button{
  display:none;
}
html{
    // background: #FFFFFF;
    body,.main,.layout{
        // background: #FFFFFF;
    }
}

body{ // 默认样式
	font-size: 16px;
//	padding-right: 0!important;
}

/* 隐藏样式 */
.hide{
    display: none;
}

/* loading 样式*/
.ivu-spin-fix {
	background-color: rgba(255,255,255,0.8);
}

/* 对话框样式 */
.ivu-modal-mask{
  background-color: rgba(55,55,55,.1);
}
.dis_textarea textarea.ivu-input {
  cursor: not-allowed;
}
// .ivu-modal-content{
// 	border-radius:3px;
// 	box-shadow: 0px 3px 10px #d5d5d5;
// }
.ivu-modal-header{
	// background: linear-gradient(to bottom,#D6272D, #9E1C24);
	padding-top: 0px;
	padding-bottom: 0px;
	border-top-left-radius:4px;
	border-top-right-radius:4px;
	.ivu-modal-header p, .ivu-modal-header-inner{
		// color: white;
		text-align: center;
		font-weight: normal;
		height:45px;
		line-height: 45px;
        font-size:16px;
        font-weight: 500;
	}
}
.ivu-modal-close{
	top:0px;
    .ivu-icon-ios-close{
        top:9px;
    }
	.ivu-icon-ios-close-empty{
		color:white;
	}
}

.ivu-modal-footer{
	border-top: 0px;
    text-align: center;
    outline:none;
    button.ivu-btn{
    	width: 75px;
    	height: 30px;
    	padding-top:3px;

    }
    // .ivu-btn-primary{
    //     background: linear-gradient(to bottom,#D6272D, #9E1C24);
    //     border: 1px solid #C42727;
    // }

    .ivu-btn-text,.ivu-btn-text:hover {
    	border:1px #DFE4E9 solid;
    	color: #333;

    }
}
.ivu-form-item{
    margin-bottom: 4px;
    vertical-align: top;
    zoom: 1;
}
.ivu-input-wrapper{
        .ivu-input-icon-normal + .ivu-input {
        padding:4px 30px 4px 7px;
        height: 32px;
        line-height: 32px;
        border-radius:4px;
    }
}

.ivu-icon-ios-search {
	background-color: @primary-color;
	color: white;
	width: 33px;
	height: 32px;
	line-height: 30px;
	font-size: 14px;
	border-bottom-right-radius:2px;
	border-top-right-radius:2px;
    cursor:pointer;
}
.ivu-select-visible .ivu-select-selection{
    box-shadow: none;
}
/* 默认按钮样式*/
button.ivu-btn{
	height: 30px;
	line-height: 14px;
    padding: 3px 10px;
}
.shadow{
	background: white;
	border-radius:3px;
	box-shadow: 0px 3px 15px #ccc;
}
.loading.fix{
    position: absolute;
    height: 100%;
    width: 100%;
    background-color: rgba(255,255,255,0.5);
    &.transparent{
        background-color: transparent;
    }
    .ivu-spin-fix{
        background-color: transparent;
    }
}

button.nav_btn{
	min-width:80px;
	height:auto;
	padding: 0 10px 0 10px;
    background-color: transparent;
	color:#000;
    margin-right: 16px;
    border: 0;
    border-radius: 0;
    &.active,&:hover{
    	background-color: transparent;
    	color:@btn-primary-bg;
    }
    &+button.nav_btn{
    	border-left:1px rgb(206,206,199) solid;
    	padding-left: 30px;
    }
    +hr{
    	margin-top: 17px;
    	border:0px;
    	border-top: 1px rgb(223,228,239) solid;
    }
}

// .ivu-page .ivu-page-item-active{
//   background-color:@primary-color;
// }

// .ivu-page-item:hover a{
//   color:rgba(196, 39, 39,0.7);
// }

// .ivu-page-next:hover a, .ivu-page-prev:hover a{
//   color:rgba(196, 39, 39,0.7);
// }

// .ivu-page-item-active:hover a{
//   color:#FFF;
// }
// .ivu-page-item-jump-next a,.ivu-page-item-jump-prev a{
//   color:rgba(196, 39, 39,0.7);
// }
.ivu-page-options-elevator input:focus,.ivu-page-options-elevator input:hover{
  // outline: 0;
  box-shadow:unset;
  border-color:@primary-color;
}

.clear{
	clear: both;
}
.ivu-btn:hover{
    color: #333;
    border-color:#DFE4E9;
}
.clearfix {
  display: block;
  zoom: 1;

  &:after {
    content: " ";
    display: block;
    font-size: 0;
    height: 0;
    clear: both;
    visibility: hidden;
  }
}
input.ivu-input{
    border: 1px solid #E1E1E4;
}
input.ivu-input:focus,textarea.ivu-input:focus{
    box-shadow: none;
}

//版本升级后，box-shadow问题
a.ivu-btn-text:focus,.ivu-btn-text:focus,.ivu-btn-primary:focus{
    box-shadow: none;
}

label.ivu-checkbox-wrapper.ivu-checkbox-group-item :nth-child(2){
	display: none;
}
.ivu-tabs,.ivu-tabs .ivu-tabs-card{
    overflow:initial;
}
.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab-active,.ivu-tabs-nav .ivu-tabs-tab:hover,.ivu-select-multiple .ivu-select-item-selected,.ivu-select-multiple .ivu-select-item-selected:after{
    color: @primary-color;
}
.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-nav-container,.ivu-tabs.ivu-tabs-card>.ivu-tabs-bar .ivu-tabs-tab{
    height: 40px;
    line-height: 30px
}
:focus{
    outline: none;
}
.ivu-input:focus,.ivu-input:hover,.ivu-select-selection:hover,.ivu-select-selection:focus, .ivu-select-visible .ivu-select-selection{
    border-color: @primary-color;
}
.ivu-select-item-selected, .ivu-select-item-selected:hover,.ivu-btn-primary,.ivu-btn-primary:hover,.ivu-select-item-selected.ivu-select-item-focus{
    background-color:  @primary-color;
    color: #FFFFFF;
    border-color: @primary-color;
}
li.ivu-select-item{
    text-overflow: ellipsis;
    overflow: hidden;
    white-space:nowrap;
}
// .ivu-tabs .ivu-tabs-content-animated{
//     will-change: unset;
//     transform: none!important;
// }
.ivu-tooltip-inner{
    background-color: rgba(0,0,0,0.7);
}
.ivu-input-wrapper .ivu-input-icon-normal + .ivu-input,.ivu-icon-ios-search{
    height: 30px;
}
