@charset "UTF-8";

//全局公用样式
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

body,
html {
  height: 100%;
}
// @base-font-size:14px;
// body {
//   font-size: @base-font-size  !important;
//   color: rgba(0, 0, 0, 0.85) !important;
//   font-family: 'Microsoft YaHei';
// }

ul,
ol,
dl {
  margin-bottom: 0;
  list-style: none;
}

.flex {display: flex;align-items: center;}
.flex-auto {flex: 1;}
.flex-none {flex: none;}
.ml-auto {margin-left: auto;}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.du-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
.three-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
.pointer {cursor: pointer;}
.single-space {line-height: 1;}
.vertical-middle {vertical-align: middle;}
.col-center {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
}
.col-left {
  display: flex;
  flex-flow: column nowrap;
  align-items: flex-start;
}
.flex-start {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.row-center {
  display: flex;
  flex-flow: column nowrap;
  justify-content: center;
}
.row-col-center {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: center;
}
.bold {font-weight: bold;}
.relative {position: relative;}
.underline {text-decoration: underline;}
.text-left {text-align: left !important;}
.text-center {text-align: center !important;}
.f14 {font-size: 14px !important;}
.f16 {font-size: 16px !important;}
.f18 {font-size: 18px !important;}
.f24 {font-size: 24px!important;}
.mt5{margin-top:5px;}
.mt10{margin-top:10px;}
.mt15{margin-top:15px;}
.mr5{margin-right:5px;}
.mr10{margin-right:10px;}
.mr15{margin-right:15px;}
.mr20{margin-right:20px;}
.mr30{margin-right:30px;}
.mr50{margin-right:50px;}
.ml-auto{margin-left:auto;}
.ml5{margin-left:5px;}
.ml10{margin-left:10px;}
.ml15{margin-left:15px;}
.ml20{margin-left:20px;}
.ml30{margin-left:30px;}
.ml50{margin-left:50px;}
.mb10{margin-bottom:10px;}
.mb15{margin-bottom:15px;}
.mb20{margin-bottom:20px;}
.pt20{padding-top:20px;}
.pd15{padding:15px;}
.pd20{padding:20px;}
.pl70{padding-left:70px;}
.pad10{padding: 0 10px;}
.pad20{padding: 0 20px;}
.ptb20{padding-top:20px;padding-bottom:20px;}
.plr30{padding-left:30px;padding-right:30px;}
.message-min {min-width: auto;}
.not-allowed {cursor: not-allowed !important;}

// 自定义提示框样式
.ant-modal {
  .ant-modal-header,
  .ant-modal-footer {
    text-align: center;
    color: fade(#000, 85);
  }
  .ant-modal-header {
    height: 50px;
  }
  .ant-modal-body {
    color: fade(#000, 85);
  }
  .ant-modal-footer {
    border-top: 0;
  }
}
.add-class-box {
  .ant-modal-title {
    font-weight: bold;
  }
}

