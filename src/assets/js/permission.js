import errorcodeJSO<PERSON> from "@/assets/json/errorcode";

let totalThread = 0;
let threadNum = 0;
let hasError = false;
let noLogin = false;
export default {
  init(Vue) {
    // http请求统一拦截处理
    Vue.http.interceptors.push(function (request, next) {
      request.credentials = true;
      // 原本逻辑
      // if (request.url.indexOf("http") === -1) {
      //   request.url = gl.serverURL + request.url;
      // }
      if(request.url.indexOf("/static/json/") > -1){ 
        // 测试数据，转换为GET方法
        request.method = "GET";
      }

      if (gl.develop && gl.DEV_TOKEN) {
        request.headers.set("token", gl.DEV_TOKEN);
      }

      if (totalThread === 0) {
        // 进度条开始
        this.$Loading.start();
      }
      totalThread++;
      threadNum++; //未完成的请求

      if (request && request.params) {
        // 请求前处理增加时间标记缓存...
        request.params["_"] = new Date().getTime();
      }
      //------------------------------------------------------------------
      let TOKEN = localStorage.getItem("tokens");
      // let otherUserLogin = (localStorage.getItem("userId") && this.$cookies.get("tsLoginUserId") != localStorage.getItem('userId'));
      // // TOKEN存在 是否有别的用户登录
      if (TOKEN && request.url.indexOf("/extApi/findDept") == -1) {
        // 就为每次请求的headers中设置好TOKEN, 后台根据headers中的TOKEN判断是否登录
        request.headers.set("token", TOKEN);
      }
      
      //------------------------------------------------------------------
      next(function (response) {
        // 请求结果拦截处理
        let status = response.body.status;
        if(status == undefined ){
          // 非常规格式不处理
          return;
        } 
        if (status == errorcodeJSON.login) {
          this.$Message.error(response.body.message);
          sessionStorage.removeItem("userName");
          sessionStorage.removeItem("sysNos");
          localStorage.removeItem("userName");
          localStorage.removeItem("sysNos");
          
          if(!gl.develop) // 开发环境不跳转
            location.replace("/login");
          
        } else if (status == errorcodeJSON.previlige) {
          this.$Message.error(response.body.message);
        }
        // 进度条处理
        if (response.status != 200 || (status != null && status != 0)) {
          this.$Loading.error();
          hasError = true;
        }
        if (threadNum == 0) {
          if (!hasError) this.$Loading.finish();
          hasError = false;
          threadNum = 0;
          totalThread = 0;
        } else if (!hasError) {
          let percent = ((totalThread - threadNum) / totalThread) * 100;
          this.$Loading.update(percent);
        }
      });
    });

    Vue.prototype.hasPermission = function (target, resourceArray) {
      if (gl.develop) {
        return true;
      }
      // 返回处理后的值
      let isHavePermission = false;

      if (!resourceArray) {
        let resources = sessionStorage.getItem("resources")
          ? sessionStorage.getItem("resources")
          : localStorage.getItem("resources");
        if (!resources || resources.length == 0) {
          return false;
        }
        resourceArray = JSON.parse(resources);
        if (resourceArray.length == 0) {
          return false;
        }
      }
      if (resourceArray) {
        for (let urlObj in resourceArray) {
          let resourceExists = resourceArray[urlObj];
          if (resourceExists) {
            if (resourceExists.url == target) {
              isHavePermission = true;
            } else if (resourceExists.children) {
              isHavePermission = this.hasPermission(
                target,
                resourceExists.children
              );
            }
            if (isHavePermission) {
              return true;
            }
          }
        }
      }
      return false;
    };
  },
};
