import VueResource from 'vue-resource';
import iView from 'iview';
import 'iview/dist/styles/iview.css';
import '../css/theme-center.less'; // 自定义主题
import EasyScroll from '../../../easyscroll'; //引入easyscroll滚动条组件
import '../css/common.less'; //公共样式


const copyObj = (obj) => JSON.parse(JSON.stringify(obj))

String.prototype.replaceAll = function (s1, s2) {
	return this.replace(new RegExp(s1, "gm"), s2);
};
// 通用配置、函数
export default {
	init(Vue) {
		// 关闭控制台环境提示
		Vue.config.productionTip = false
		// 请求数据资源组件
		Vue.use(VueResource);
		// 调整表单方式提交
		Vue.http.options.emulateJSON = true;
		// 启用iview组件
		Vue.use(iView);
		// // 启用EasyScroll组件
		Vue.use(EasyScroll);
	}
}