import Vue from "vue";
import Router from "vue-router";
import NotFoundComponent from "@/vivew/404";
Vue.use(Router);

// 解决导航栏中的vue-router在3.0版本以上重复点菜单报错问题
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};

const router = new Router({
  base: document.baseURI.replace(window.location.origin, ""),
  routes: [
    {
      path: "/",
      redirect: "/home",
    },
    {
      path: "/login",
      component: (resolve) => require(["@/vivew/oldLogin"], resolve), //测试
    },
    {
      path:"/ControlsDetails",
      component: (resolve) => require(["@/vivew/ControlsDetails"], resolve),
    },
    {
      path: "/oldLogin",
      component: (resolve) => require(["@/vivew/oldLogin"], resolve),
    },
    {
      path: "/gateWay",
      component: (resolve) => require(["@/vivew/gateway"], resolve),
    },
    {
      path: "/home",
      component: (resolve) => require(["@/vivew/home"], resolve),
    },
    {
      path: "/advertisement",
      component: (resolve) => require(["@/vivew/advertisement"], resolve),
    },
    {
      path: "/dutyRoster",
      component: (resolve) => require(["@/vivew/dutyRoster"], resolve),
    },
    {
      path: "/SystemConfiguration",
      component: (resolve) => require(["@/vivew/SystemConfiguration"], resolve),
      redirect: "/SystemConfiguration/mechanism",
      children: [
        {
          path: "/SystemConfiguration/mechanism",
          component: (resolve) =>
            require(["@/vivew//SystemConfiguration/mechanism"], resolve),
        },
        {
          path: "/SystemConfiguration/role",
          component: (resolve) =>
            require(["@/vivew//SystemConfiguration/role"], resolve),
        },
        {
          path: "/SystemConfiguration/station",
          component: (resolve) =>
            require(["@/vivew//SystemConfiguration/station"], resolve),
        },
        {
          path: "/SystemConfiguration/behaviorAuditing",
          component: (resolve) =>
            require(["@/vivew//SystemConfiguration/behaviorAuditing"], resolve),
          children: [
            {
              path: "/SystemConfiguration/behaviorAuditing/theLog",
              component: (resolve) =>
                require([
                  "@/vivew//SystemConfiguration/behaviorAuditing/theLog",
                ], resolve),
            },
            {
              path: "/SystemConfiguration/behaviorAuditing/statistical",
              component: (resolve) =>
                require([
                  "@/vivew//SystemConfiguration/behaviorAuditing/statistical",
                ], resolve),
            },
          ],
        },
        {
          path: "/SystemConfiguration/feedBack",
          component: (resolve) =>
            require(["@/vivew//SystemConfiguration/feedBack"], resolve),
        },
      ],
    },
    {
      path: "*",
      component: NotFoundComponent,
    }
  ],
});

export default router;
