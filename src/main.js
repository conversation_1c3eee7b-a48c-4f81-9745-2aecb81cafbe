import Vue from 'vue'
import router from './router/index.js'
import Main from './vivew/index.vue'
import './assets/font/font.css'
import common from './assets/js/common';
import permission from './assets/js/permission';
import Contextmenu from "vue-contextmenujs"
import './common/common.less'
import './icons'
// import Video from 'video.js'
// import 'video.js/dist/video-js.css'
import Resource from 'vue-resource'
import axios from 'axios';
import exportUtil from './util/exportUtil.js'

axios.defaults.withCredentials = true;

Vue.use(Resource)
Vue.use(Contextmenu);

common.init(Vue);
permission.init(Vue);
Vue.prototype.getLog = function (module, content, spark) {
    let url = gl.serverURL+"/addLog";
    let params = {
        module: module,
        content: content ? content : "浏览",
        spark: spark,
    };
    this.$http.get(url, { params }).then((res) => {});
};
Vue.prototype.exportUtil = exportUtil;
const vm = new Vue({
    el: '#main', // 入口位置，在相应位置替换为组件
    router, // 路由信息，根据配置跳转相应组件，等于router:router
    render: h => h(Main) // 指定模板组件
});